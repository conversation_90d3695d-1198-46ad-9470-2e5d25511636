<?xml version="1.0" encoding="UTF-8"?>
<CustomMetadata xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <label>Require Scenario Usage</label>
    <protected>false</protected>
    <values>
        <field>Comments__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Description__c</field>
        <value xsi:type="xsd:string">When set to &apos;false&apos; (default), specifying a scenario is completely optional.

When set to &apos;true&apos;, a scenario is required to be set before any logging can occur. If a logging method is called &amp; the current scenario is null/blank, then Nebula Logger will throw a runtime exception.</value>
    </values>
    <values>
        <field>Value__c</field>
        <value xsi:type="xsd:string">false</value>
    </values>
</CustomMetadata>
