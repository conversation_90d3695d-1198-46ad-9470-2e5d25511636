<?xml version="1.0" encoding="UTF-8"?>
<CustomMetadata xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <label>Store Heap Size Limit</label>
    <protected>false</protected>
    <values>
        <field>Comments__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Description__c</field>
        <value xsi:type="xsd:string">When set to &apos;true&apos; (default), transaction heap limits are retrieved from the class System.Limits and stored on LogEntry__c.

When set to &apos;false&apos;, transaction heap limits are not retrieved or stored. This drastically helps reduce CPU time usage per log entry. The &apos;Store Transaction Limits&apos; Logger Parameter record must also be set to &apos;true&apos; for any limit tracking to occur.</value>
    </values>
    <values>
        <field>Value__c</field>
        <value xsi:type="xsd:string">true</value>
    </values>
</CustomMetadata>
