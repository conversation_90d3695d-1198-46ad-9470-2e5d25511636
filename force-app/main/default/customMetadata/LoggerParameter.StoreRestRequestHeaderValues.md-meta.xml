<?xml version="1.0" encoding="UTF-8"?>
<CustomMetadata xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <label>Store REST Request Header Values</label>
    <protected>false</protected>
    <values>
        <field>Comments__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Description__c</field>
        <value xsi:type="xsd:string">When set to &apos;true&apos; (default), Nebula Logger will store the header values of any instance of an RestRequest that is logged using the instance method LogEntryEventBuilder.setRestRequestDetails().

When set to &apos;false&apos;, the header values are not stored or referenced by Nebula Logger.

Regardless of how this parameter is configured, Nebula Logger will still log the header keys of any instance of an RestRequest that is logged - this parameter only controls if the header values are stored.</value>
    </values>
    <values>
        <field>Value__c</field>
        <value xsi:type="xsd:string">true</value>
    </values>
</CustomMetadata>
