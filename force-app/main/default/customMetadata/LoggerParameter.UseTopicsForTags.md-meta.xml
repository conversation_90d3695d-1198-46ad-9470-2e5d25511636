<?xml version="1.0" encoding="UTF-8"?>
<CustomMetadata xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <label>Use Topics for Tags</label>
    <protected>true</protected>
    <values>
        <field>Comments__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Description__c</field>
        <value xsi:type="xsd:string">When set to &apos;true&apos;, any custom Logger tags will use the standard objects Topic and TopicAssignment

When set to &apos;false&apos; (default), any custom Logger tags will use the custom objects LoggerTag__c and LogEntryTag__c</value>
    </values>
    <values>
        <field>Value__c</field>
        <value xsi:type="xsd:string">false</value>
    </values>
</CustomMetadata>
