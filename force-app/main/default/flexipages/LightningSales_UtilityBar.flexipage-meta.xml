<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <description>The default utility bar for the Sales application.</description>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>eager</name>
                    <type>decorator</type>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>height</name>
                    <type>decorator</type>
                    <value>450</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>icon</name>
                    <type>decorator</type>
                    <value>fallback</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <type>decorator</type>
                    <value>Webex Dialer</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>scrollable</name>
                    <type>decorator</type>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>width</name>
                    <type>decorator</type>
                    <value>420</value>
                </componentInstanceProperties>
                <componentName>cisco_webex:Webex</componentName>
                <identifier>cisco_webex_Webex</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>runtime_sales_todo_list:unifiedToDoListAuraWrapper</componentName>
                <identifier>runtime_sales_todo_list_unifiedToDoListAuraWrapper</identifier>
            </componentInstance>
        </itemInstances>
        <name>utilityItems</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <name>backgroundComponents</name>
        <type>Background</type>
    </flexiPageRegions>
    <masterLabel>Sales App Utility Bar</masterLabel>
    <template>
        <name>one:utilityBarTemplateDesktop</name>
        <properties>
            <name>isLeftAligned</name>
            <value>true</value>
        </properties>
    </template>
    <type>UtilityBar</type>
</FlexiPage>
