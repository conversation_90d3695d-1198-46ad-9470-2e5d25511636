<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>dashboardName</name>
                    <value>LoggerAdmin</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideOnError</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentName>desktopDashboards:embeddedDashboard</componentName>
                <identifier>desktopDashboards_embeddedDashboard</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-f960037f-4935-49be-aef3-5f696b51fc8f</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentName>loggerSettings</componentName>
                <identifier>c_loggerSettings</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-dce32d29-eac4-4969-a853-889e1276ac4e</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentName>logEntryEventStream</componentName>
                <identifier>c_logEntryEventStream</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-7c15c775-623e-411d-99c9-040fac6a118a</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentName>logBatchPurge</componentName>
                <identifier>c_logBatchPurge</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-flyklawwy3t</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>active</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-f960037f-4935-49be-aef3-5f696b51fc8f</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.dashboard</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>active</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-dce32d29-eac4-4969-a853-889e1276ac4e</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.landingPageSettings</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>active</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-7c15c775-623e-411d-99c9-040fac6a118a</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Event Stream</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>active</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-flyklawwy3t</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Batch Purge</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab4</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-d9ee7df8-453b-41c9-9c89-5c00f2d33ab4</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentName>loggerHomeHeader</componentName>
                <identifier>c_loggerHomeHeader</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>Facet-d9ee7df8-453b-41c9-9c89-5c00f2d33ab4</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset</identifier>
            </componentInstance>
        </itemInstances>
        <name>main</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>Nebula Logger: Logger Home Page</masterLabel>
    <template>
        <name>runtime_commerce_oci:homeTemplateOneRegion</name>
    </template>
    <type>HomePage</type>
</FlexiPage>
