<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>eager</name>
                    <type>decorator</type>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>height</name>
                    <type>decorator</type>
                    <value>600</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>icon</name>
                    <type>decorator</type>
                    <value>settings</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <type>decorator</type>
                    <value>Logger Settings</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>scrollable</name>
                    <type>decorator</type>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>width</name>
                    <type>decorator</type>
                    <value>1200</value>
                </componentInstanceProperties>
                <componentName>loggerSettings</componentName>
                <identifier>loggerSettings</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>console:history</componentName>
                <identifier>console_history</identifier>
            </componentInstance>
        </itemInstances>
        <name>utilityItems</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <name>backgroundComponents</name>
        <type>Background</type>
    </flexiPageRegions>
    <masterLabel>Nebula Logger: Logger Console Utility Bar</masterLabel>
    <template>
        <name>one:utilityBarTemplateDesktop</name>
        <properties>
            <name>isLeftAligned</name>
            <value>false</value>
        </properties>
    </template>
    <type>UtilityBar</type>
</FlexiPage>
