<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Name</fieldItem>
                <identifier>RecordNameField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-e9a6a782-648a-4a5d-9392-4b6f39b8a73c</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LoggedByUsernameLink__c</fieldItem>
                <identifier>RecordLoggedByUsernameLink_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ProfileLink__c</fieldItem>
                <identifier>RecordProfileLink_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ImpersonatedByUsernameLink__c</fieldItem>
                <identifier>RecordImpersonatedByUsernameLink_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.ImpersonatedByUsernameLink__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LoggingLevel__c</fieldItem>
                <identifier>RecordLoggingLevel_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Origin__c</fieldItem>
                <identifier>RecordOrigin_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ExceptionType__c</fieldItem>
                <identifier>RecordExceptionType_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.HasException__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ExceptionLocation__c</fieldItem>
                <identifier>RecordExceptionLocation_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.HasException__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Timestamp__c</fieldItem>
                <identifier>RecordTimestamp_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Log__c</fieldItem>
                <identifier>RecordLog_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TransactionEntryNumber__c</fieldItem>
                <identifier>RecordTransactionEntryNumber_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ParentLogLink__c</fieldItem>
                <identifier>RecordParentLogLink_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.ParentLogLink__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.EntryScenarioLink__c</fieldItem>
                <identifier>RecordEntryScenarioLink_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.EntryScenarioLink__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-faa2a30d-73a7-4c68-9c85-9cd53de6cf32</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>numVisibleActions</name>
                    <value>3</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>primaryField</name>
                    <value>Facet-e9a6a782-648a-4a5d-9392-4b6f39b8a73c</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>secondaryFields</name>
                    <value>Facet-faa2a30d-73a7-4c68-9c85-9cd53de6cf32</value>
                </componentInstanceProperties>
                <componentName>record_flexipage:dynamicHighlights</componentName>
                <identifier>record_flexipage_dynamicHighlights</identifier>
            </componentInstance>
        </itemInstances>
        <name>header</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.MessageTruncated__c</fieldItem>
                <identifier>RecordMessageTruncated__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.MessageTruncated__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.MessageMasked__c</fieldItem>
                <identifier>RecordMessageMasked__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.MessageMasked__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Message__c</fieldItem>
                <identifier>RecordMessage__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.StackTrace__c</fieldItem>
                <identifier>RecordStackTrace_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.HasStackTrace__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-30caba7c-11da-4321-bccd-e97e901d03f4</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-30caba7c-11da-4321-bccd-e97e901d03f4</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column3</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-3a75f17f-589a-49f4-bdec-38f730e94676</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OriginSourceApiName__c</fieldItem>
                <identifier>RecordOriginSourceApiName_cField2</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.FlowLabel__c</fieldItem>
                <identifier>RecordFlowLabel__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.FlowProcessType__c</fieldItem>
                <identifier>RecordFlowProcessType__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.FlowTriggerType__c</fieldItem>
                <identifier>RecordFlowTriggerType__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.FlowTriggerSObjectType__c</fieldItem>
                <identifier>RecordFlowTriggerSObjectType_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.FlowTriggerOrder__c</fieldItem>
                <identifier>RecordFlowTriggerOrder_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.FlowRecordTriggerType__c</fieldItem>
                <identifier>RecordFlowRecordTriggerType_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-17586de5-b418-46c3-8e43-07f6a5e7321a</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.FlowVersionRunInMode__c</fieldItem>
                <identifier>RecordFlowVersionRunInMode__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.FlowVersionApiVersionRuntime__c</fieldItem>
                <identifier>RecordFlowVersionApiVersionRuntime__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.FlowVersionNumber__c</fieldItem>
                <identifier>RecordFlowVersionNumber__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-f9b96f42-358a-4ffe-bc2c-a20f42bd59b8</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-17586de5-b418-46c3-8e43-07f6a5e7321a</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column8</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-f9b96f42-358a-4ffe-bc2c-a20f42bd59b8</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column9</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-5f20b7f6-e40f-49b2-905b-5e2ca6c609e9</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OriginSourceApiName__c</fieldItem>
                <identifier>RecordOriginSourceApiName_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OriginSourceActionName__c</fieldItem>
                <identifier>RecordOriginSourceActionName_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OriginSourceCreatedByLink__c</fieldItem>
                <identifier>RecordOriginSourceCreatedByLink_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OriginSourceCreatedDate__c</fieldItem>
                <identifier>RecordOriginSourceCreatedDate_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-65bc1c15-e5f3-4330-9bf7-c91d56d272f9</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OriginSourceMetadataType__c</fieldItem>
                <identifier>RecordOriginSourceType_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OriginSourceApiVersion__c</fieldItem>
                <identifier>RecordOriginSourceApiVersion_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OriginSourceLastModifiedByLink__c</fieldItem>
                <identifier>RecordOriginSourceLastModifiedByLink_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OriginSourceLastModifiedDate__c</fieldItem>
                <identifier>RecordOriginSourceLastModifiedDate_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-400f4865-a661-422c-8e16-4eba064be4ad</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-65bc1c15-e5f3-4330-9bf7-c91d56d272f9</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column21</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-400f4865-a661-422c-8e16-4eba064be4ad</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-c174106c-7ba2-4781-a50f-001c5fa0c38a</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ExceptionType__c</fieldItem>
                <identifier>RecordExceptionType__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ExceptionMessage__c</fieldItem>
                <identifier>RecordExceptionMessage__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ExceptionStackTrace__c</fieldItem>
                <identifier>RecordExceptionStackTrace_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.HasExceptionStackTrace__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-2459e7ac-69ae-42c7-a640-3fd29367e57b</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-2459e7ac-69ae-42c7-a640-3fd29367e57b</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column10</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-2a15cd64-78af-4727-8c6d-2913cb00a278</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ExceptionSourceApiName__c</fieldItem>
                <identifier>RecordExceptionSourceApiName_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ExceptionSourceActionName__c</fieldItem>
                <identifier>RecordExceptionSourceActionName_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ExceptionSourceCreatedByLink__c</fieldItem>
                <identifier>RecordExceptionSourceCreatedByLink_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ExceptionSourceCreatedDate__c</fieldItem>
                <identifier>RecordExceptionSourceCreatedDate_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-b31ccc94-fd23-40e9-aa1c-3fe3b1b5b56f</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ExceptionSourceMetadataType__c</fieldItem>
                <identifier>RecordExceptionSourceMetadataType_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ExceptionSourceApiVersion__c</fieldItem>
                <identifier>RecordExceptionSourceApiVersion_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ExceptionSourceLastModifiedByLink__c</fieldItem>
                <identifier>RecordExceptionSourceLastModifiedByLink_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ExceptionSourceLastModifiedDate__c</fieldItem>
                <identifier>RecordExceptionSourceLastModifiedDate_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-28bcdefa-7228-496e-8459-446e879ab522</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-b31ccc94-fd23-40e9-aa1c-3fe3b1b5b56f</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column24</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-28bcdefa-7228-496e-8459-446e879ab522</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column25</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-7952b55d-c8c4-4c6d-aaf4-0c11b5bed24e</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-3a75f17f-589a-49f4-bdec-38f730e94676</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Message Details</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>sourceMetadata</name>
                    <value>Origin</value>
                </componentInstanceProperties>
                <componentName>logEntryMetadataViewer</componentName>
                <identifier>c_logEntryMetadataViewer</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.HasOriginSourceSnippet__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-5f20b7f6-e40f-49b2-905b-5e2ca6c609e9</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Flow Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection5</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.OriginType__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Flow</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-c174106c-7ba2-4781-a50f-001c5fa0c38a</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Origin Source Details</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection15</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.OriginSourceApiName__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.OriginType__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Flow</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-2a15cd64-78af-4727-8c6d-2913cb00a278</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Exception Details</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection6</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.HasException__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>sourceMetadata</name>
                    <value>Exception</value>
                </componentInstanceProperties>
                <componentName>logEntryMetadataViewer</componentName>
                <identifier>c_logEntryMetadataViewer2</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.HasExceptionSourceSnippet__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-7952b55d-c8c4-4c6d-aaf4-0c11b5bed24e</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Exception Source Details</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection17</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.HasException__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <name>Facet-7beef2bc-28d3-490f-96b8-7a8f85424802</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LimitsAggregateQueries__c</fieldItem>
                <identifier>RecordLimitsAggregateQueries__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LimitsAsyncCalls__c</fieldItem>
                <identifier>RecordLimitsAsyncCalls__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LimitsCallouts__c</fieldItem>
                <identifier>RecordLimitsCallouts__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LimitsCpuTime__c</fieldItem>
                <identifier>RecordLimitsCpuTime__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LimitsDmlRows__c</fieldItem>
                <identifier>RecordLimitsDmlRows__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LimitsDmlStatements__c</fieldItem>
                <identifier>RecordLimitsDmlStatements__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LimitsEmailInvocations__c</fieldItem>
                <identifier>RecordLimitsEmailInvocations__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LimitsFutureCalls__c</fieldItem>
                <identifier>RecordLimitsFutureCalls__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-8e5572ea-8a8e-4426-8154-004cbbcb1314</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LimitsHeapSize__c</fieldItem>
                <identifier>RecordLimitsHeapSize__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LimitsMobilePushApexCalls__c</fieldItem>
                <identifier>RecordLimitsMobilePushApexCalls__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LimitsPublishImmediateDmlStatements__c</fieldItem>
                <identifier>RecordLimitsPublishImmediateDmlStatements__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LimitsQueueableJobs__c</fieldItem>
                <identifier>RecordLimitsQueueableJobs__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LimitsSoqlQueries__c</fieldItem>
                <identifier>RecordLimitsSoqlQueries__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LimitsSoqlQueryLocatorRows__c</fieldItem>
                <identifier>RecordLimitsSoqlQueryLocatorRows__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LimitsSoqlQueryRows__c</fieldItem>
                <identifier>RecordLimitsSoqlQueryRows__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LimitsSoslSearches__c</fieldItem>
                <identifier>RecordLimitsSoslSearches__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-ce9617b4-fac3-4077-93b0-d2ced40ad0b1</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-8e5572ea-8a8e-4426-8154-004cbbcb1314</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column13</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-ce9617b4-fac3-4077-93b0-d2ced40ad0b1</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column14</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-4fa5f475-f3a7-4ac0-b1cd-8285d7b369c7</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-4fa5f475-f3a7-4ac0-b1cd-8285d7b369c7</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Transaction Limits</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection9</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-c02369d8-c1c8-498e-b87b-eb5300fe4f5b</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DatabaseResultCollectionType__c</fieldItem>
                <identifier>RecordDatabaseResultCollectionType__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DatabaseResultType__c</fieldItem>
                <identifier>RecordDatabaseResultType__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DatabaseResultCollectionSize__c</fieldItem>
                <identifier>RecordDatabaseResultCollectionSize_cField1</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DatabaseResultJson__c</fieldItem>
                <identifier>RecordDatabaseResultJson__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-839d3f77-0b78-43a9-9477-6b63126dcf12</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-839d3f77-0b78-43a9-9477-6b63126dcf12</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column11</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-b0b8c0ec-36e3-469b-9106-00d34530d793</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RecordLink__c</fieldItem>
                <identifier>RecordRecordLink__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RecordSObjectClassification__c</fieldItem>
                <identifier>RecordRecordSObjectClassification__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RecordSObjectTypeNamespace__c</fieldItem>
                <identifier>RecordRecordSObjectTypeNamespace__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordSObjectTypeNamespace__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RecordCollectionSize__c</fieldItem>
                <identifier>RecordRecordCollectionSize_cField1</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RecordJsonMasked__c</fieldItem>
                <identifier>RecordRecordJsonMasked__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordJsonMasked__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RecordJson__c</fieldItem>
                <identifier>RecordRecordJson__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.HasRecordJson__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-1da92bf7-f9ed-4842-b880-7625370f3c70</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-1da92bf7-f9ed-4842-b880-7625370f3c70</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column12</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-f6d04c17-cbcd-40c6-aa58-c46313ecd8c4</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-b0b8c0ec-36e3-469b-9106-00d34530d793</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Database Result Details</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection7</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.HasDatabaseResult__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.HasDatabaseResultJson__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-f6d04c17-cbcd-40c6-aa58-c46313ecd8c4</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Related Record Details</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection8</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.HasRecordId__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.HasRecordJson__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <name>Facet-dxcl7nfj6iq</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.HttpRequestEndpoint__c</fieldItem>
                <identifier>RecordHttpRequestEndpoint_cField1</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.HttpRequestEndpointAddress__c</fieldItem>
                <identifier>RecordHttpRequestEndpointAddress_cField1</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.HttpRequestMethod__c</fieldItem>
                <identifier>RecordHttpRequestMethod_cField1</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.HttpRequestHeaderKeys__c</fieldItem>
                <identifier>RecordHttpRequestHeaderKeys_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.HasHttpRequestHeaderKeys__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.HttpRequestHeaders__c</fieldItem>
                <identifier>RecordHttpRequestHeaders_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.HasHttpRequestHeaders__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.HttpRequestCompressed__c</fieldItem>
                <identifier>RecordHttpRequestCompressed_cField1</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.HttpRequestBody__c</fieldItem>
                <identifier>RecordHttpRequestBody_cField1</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.HttpRequestBodyMasked__c</fieldItem>
                <identifier>RecordHttpRequestBodyMasked_cField1</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-4cea44fd-c4f1-41ed-bada-eace6229f158</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-4cea44fd-c4f1-41ed-bada-eace6229f158</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column1</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-25eaa065-25af-4efc-9ce5-9d289a0fc3f0</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.HttpResponseStatusCode__c</fieldItem>
                <identifier>RecordHttpResponseStatusCode_cField1</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.HttpResponseStatus__c</fieldItem>
                <identifier>RecordHttpResponseStatus_cField1</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.HttpResponseHeaderKeys__c</fieldItem>
                <identifier>RecordHttpResponseHeaderKeys_cField1</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.HasHttpResponseHeaders__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>false</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.HttpResponseHeaders__c</fieldItem>
                <identifier>RecordHttpResponseHeaders_cField1</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.HasHttpResponseHeaders__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.HttpResponseBody__c</fieldItem>
                <identifier>RecordHttpResponseBody_cField1</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.HttpResponseBodyMasked__c</fieldItem>
                <identifier>RecordHttpResponseBodyMasked_cField1</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-1f2467a3-2a30-4aad-ace3-80afc0a517d2</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-1f2467a3-2a30-4aad-ace3-80afc0a517d2</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column15</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-6395ceae-5694-468a-911f-714456910736</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-25eaa065-25af-4efc-9ce5-9d289a0fc3f0</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>HTTP Callout Request</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection1</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.HttpRequestEndpoint__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-6395ceae-5694-468a-911f-714456910736</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>HTTP Callout Response</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection10</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2 OR 3</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.HttpResponseStatus__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.HasHttpResponseBody__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.HasHttpResponseHeaderKeys__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <name>Facet-8ec06805-0396-4b22-b718-23193da39f19</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RestRequestResourcePath__c</fieldItem>
                <identifier>RecordRestRequestResourcePath_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RestRequestUri__c</fieldItem>
                <identifier>RecordRestRequestUri_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RestRequestMethod__c</fieldItem>
                <identifier>RecordRestRequestMethod_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RestRequestRemoteAddress__c</fieldItem>
                <identifier>RecordRestRequestRemoteAddress_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RestRequestParameters__c</fieldItem>
                <identifier>RecordRestRequestParameters_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RestRequestHeaderKeys__c</fieldItem>
                <identifier>RecordRestRequestHeaderKeys_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.HasRestRequestHeaders__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>false</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RestRequestHeaders__c</fieldItem>
                <identifier>RecordRestRequestHeaders_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.HasRestRequestHeaderKeys__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RestRequestBody__c</fieldItem>
                <identifier>RecordRestRequestBody_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RestRequestBodyMasked__c</fieldItem>
                <identifier>RecordRestRequestBodyMasked_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-6411b37c-0886-4766-a9c5-5ede0e60cb97</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-6411b37c-0886-4766-a9c5-5ede0e60cb97</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column20</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-f29c9a43-fb99-4d37-be66-925bb2bbeecb</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RestResponseStatusCode__c</fieldItem>
                <identifier>RecordRestResponseStatusCode_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RestResponseHeaderKeys__c</fieldItem>
                <identifier>RecordRestResponseHeaderKeys_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.HasHttpResponseHeaders__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>false</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RestResponseHeaders__c</fieldItem>
                <identifier>RecordRestResponseHeaders_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.HasRestResponseHeaders__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RestResponseBody__c</fieldItem>
                <identifier>RecordRestResponseBody_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RestResponseBodyMasked__c</fieldItem>
                <identifier>RecordRestResponseBodyMasked_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-c8e76a2d-dfaa-4163-b3cf-bbe5fa9cbec8</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-c8e76a2d-dfaa-4163-b3cf-bbe5fa9cbec8</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column19</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-2181992b-5c3d-435c-a152-f9b0147323c2</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-f29c9a43-fb99-4d37-be66-925bb2bbeecb</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Apex REST Service Request</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection14</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RestRequestResourcePath__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-2181992b-5c3d-435c-a152-f9b0147323c2</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Apex REST Service Response</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection13</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2 OR 3</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.HasRestResponseBody__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.HasRestResponseHeaderKeys__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.HasRestResponseHeaders__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <name>Facet-e22db3df-2c92-4784-89d6-ea64fa3a0e25</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>active</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-7beef2bc-28d3-490f-96b8-7a8f85424802</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.detail</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>active</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-c02369d8-c1c8-498e-b87b-eb5300fe4f5b</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Transaction Limits</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab1</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-dxcl7nfj6iq</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Related Records</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab3</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2 OR 3 OR 4</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.HasRecordId__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.HasRecordJson__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.HasDatabaseResult__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.HasDatabaseResultJson__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-8ec06805-0396-4b22-b718-23193da39f19</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>HTTP Callout</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab5</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2 OR 3 OR 4</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.HttpRequestEndpoint__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.HasHttpResponseBody__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.HasHttpResponseHeaderKeys__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.HasHttpResponseHeaders__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-e22db3df-2c92-4784-89d6-ea64fa3a0e25</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Apex REST</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab6</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2 OR 3 OR 4</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.RestRequestResourcePath__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.HasRestResponseBody__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.HasRestResponseHeaderKeys__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.HasRestResponseHeaders__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <name>Facet-ec9d7630-d727-4e4a-9a1a-3ebabfc68c45</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>Facet-ec9d7630-d727-4e4a-9a1a-3ebabfc68c45</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset1</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>force:recordDetailPanelMobile</componentName>
                <identifier>force_recordDetailPanelMobile</identifier>
            </componentInstance>
        </itemInstances>
        <name>main</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Tags__c</fieldItem>
                <identifier>RecordTags_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-1ea06e42-55f6-41d8-8bc9-52c13cd5dcd2</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-1ea06e42-55f6-41d8-8bc9-52c13cd5dcd2</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column16</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-defe921e-2975-4026-bfa5-1fe30baba592</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Timestamp__c</fieldItem>
                <identifier>RecordTimestamp__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LoggingLevel__c</fieldItem>
                <identifier>RecordLoggingLevel__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LoggedByUsernameLink__c</fieldItem>
                <identifier>RecordLoggedByUsernameLink__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Log__c</fieldItem>
                <identifier>RecordLog__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.EventUuid__c</fieldItem>
                <identifier>RecordEventUuid__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.EventUuid__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.EntrySaveReason__c</fieldItem>
                <identifier>RecordEntrySaveReason_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.EntrySaveReason__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Logging Level Met</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.EntrySaveReason__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Save Overridden</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.EntryScenario__c</fieldItem>
                <identifier>RecordEntryScenario__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.EntryScenarioLink__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Trigger__c</fieldItem>
                <identifier>RecordTrigger__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.TriggerIsExecuting__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-cb40f95d-9915-4ba5-815c-f3e53bcc4001</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-cb40f95d-9915-4ba5-815c-f3e53bcc4001</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-506803d6-0468-4ac1-a044-ede56cf84c05</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ApexClassName__c</fieldItem>
                <identifier>RecordApexClassName__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ApexInnerClassName__c</fieldItem>
                <identifier>RecordApexInnerClassName__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.ApexInnerClassName__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-eb0dd886-ca25-4094-b405-b0653a844c4c</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ApexMethodName__c</fieldItem>
                <identifier>RecordApexMethodName__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ApexClassApiVersion__c</fieldItem>
                <identifier>RecordApexClassApiVersion__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-a59c58c4-ed12-445a-b526-b1fdb0225dfd</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-eb0dd886-ca25-4094-b405-b0653a844c4c</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column4</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-a59c58c4-ed12-445a-b526-b1fdb0225dfd</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column5</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-a417c39b-b395-4d13-a87d-2d5bbb9d9d77</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ComponentType__c</fieldItem>
                <identifier>RecordComponentType__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ComponentApiName__c</fieldItem>
                <identifier>RecordComponentApiName__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-dc259053-1bb7-4620-814d-e4f04eca75c2</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ComponentFunctionName__c</fieldItem>
                <identifier>RecordComponentFunctionName__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-91b743b5-7408-4da0-9cf3-e3fbd695545d</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-dc259053-1bb7-4620-814d-e4f04eca75c2</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column6</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-91b743b5-7408-4da0-9cf3-e3fbd695545d</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column7</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-6b39517d-52f9-42f2-bf33-d392592baaa4</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.BrowserUserAgent__c</fieldItem>
                <identifier>RecordBrowserUserAgent_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.BrowserUrl__c</fieldItem>
                <identifier>RecordBrowserUrl_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.BrowserAddress__c</fieldItem>
                <identifier>RecordBrowserAddress_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.BrowserLanguage__c</fieldItem>
                <identifier>RecordBrowserLanguage_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-2dc994c6-5590-48da-8d0a-5f81b3755c5d</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.BrowserFormFactor__c</fieldItem>
                <identifier>RecordBrowserFormFactor_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.BrowserWindowResolution__c</fieldItem>
                <identifier>RecordBrowserWindowResolution_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.BrowserScreenResolution__c</fieldItem>
                <identifier>RecordBrowserScreenResolution_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-a07ca2be-bb73-4048-9b60-7392cf56866a</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-2dc994c6-5590-48da-8d0a-5f81b3755c5d</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column17</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-a07ca2be-bb73-4048-9b60-7392cf56866a</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column18</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-81767854-f045-476a-a24f-aa790ad2f249</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>numberOfTopicsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>searchHint</name>
                    <value>Type a topic name and press Enter.</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Topics</value>
                </componentInstanceProperties>
                <componentName>forceChatter:topicsOnRecordWrapper</componentName>
                <identifier>forceChatter_topicsOnRecordWrapper</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>MassChangeOwner</value>
                        </valueListItems>
                        <valueListItems>
                            <value>New</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>LogEntry__c.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>LogEntryTags__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>CARD</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>LoggerTag__c.NAME</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Log Entry Tags</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>LoggerTag__c.NAME</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Ascending</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-defe921e-2975-4026-bfa5-1fe30baba592</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Tags</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection11</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.HasInlineTags__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-506803d6-0468-4ac1-a044-ede56cf84c05</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-a417c39b-b395-4d13-a87d-2d5bbb9d9d77</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Apex Class Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection3</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2 AND 3</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.OriginType__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Apex</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ApexClassName__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.OriginSourceApiName__c}</leftValue>
                        <operator>EQUAL</operator>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-6b39517d-52f9-42f2-bf33-d392592baaa4</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Lightning Component Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection4</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.OriginType__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Component</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.OriginSourceApiName__c}</leftValue>
                        <operator>EQUAL</operator>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-81767854-f045-476a-a24f-aa790ad2f249</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Browser Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection12</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.BrowserUserAgent__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <name>sidebar</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>Nebula Logger: Log Entry Record Page</masterLabel>
    <sobjectType>LogEntry__c</sobjectType>
    <template>
        <name>flexipage:recordHomeTemplateDesktop</name>
    </template>
    <type>RecordPage</type>
</FlexiPage>
