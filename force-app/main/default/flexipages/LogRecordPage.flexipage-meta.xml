<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Name</fieldItem>
                <identifier>RecordNameField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-85348f99-93fb-4c1d-81a7-03d2daa70c39</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LoggedByUsernameLink__c</fieldItem>
                <identifier>RecordLoggedByUsernameLink_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ProfileLink__c</fieldItem>
                <identifier>RecordProfileLink_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ImpersonatedByUsernameLink__c</fieldItem>
                <identifier>RecordImpersonatedByUsernameLink_cField2</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.ImpersonatedByUsernameLink__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.StartTime__c</fieldItem>
                <identifier>RecordStartTime_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ParentLogLink__c</fieldItem>
                <identifier>RecordParentLogLink_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.ParentLogLink__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TransactionScenarioLink__c</fieldItem>
                <identifier>RecordTransactionScenarioLink_cField2</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.TransactionScenarioText__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.AsyncContextType__c</fieldItem>
                <identifier>RecordAsyncContextType_cField2</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.AsyncContextParentJobId__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.AsyncContextTriggerId__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OwnerId</fieldItem>
                <identifier>RecordOwnerIdField2</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LogRetentionDate__c</fieldItem>
                <identifier>RecordLogRetentionDate_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ClosedBy__c</fieldItem>
                <identifier>RecordClosedBy_cField2</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.IsClosed__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ClosedDate__c</fieldItem>
                <identifier>RecordClosedDate_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.IsClosed__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LogEntriesSummary__c</fieldItem>
                <identifier>RecordLogEntriesSummary_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-3e824aa6-3055-41bf-a476-862cda95f7f0</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>Log__c.OpenViewer</value>
                        </valueListItems>
                        <valueListItems>
                            <value>ChangeOwnerOne</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Log__c.Manage</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Share</value>
                        </valueListItems>
                        <valueListItems>
                            <value>RecordShareHierarchy</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Delete</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>numVisibleActions</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>primaryField</name>
                    <value>Facet-85348f99-93fb-4c1d-81a7-03d2daa70c39</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>secondaryFields</name>
                    <value>Facet-3e824aa6-3055-41bf-a476-862cda95f7f0</value>
                </componentInstanceProperties>
                <componentName>record_flexipage:dynamicHighlights</componentName>
                <identifier>record_flexipage_dynamicHighlights</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>hideUpdateButton</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>variant</name>
                    <value>non-linear</value>
                </componentInstanceProperties>
                <componentName>runtime_sales_pathassistant:pathAssistant</componentName>
                <identifier>runtime_sales_pathassistant_pathAssistant</identifier>
            </componentInstance>
        </itemInstances>
        <name>header</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>30</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Log__c.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>LogEntries__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>LoggingLevel__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Origin__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Message__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>RecordDetailedLink__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>EntryScenarioLink__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>TransactionEntryNumber__c</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Log Entries</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>TransactionEntryNumber__c</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Ascending</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-0f2e71a4-ac08-400c-8f11-6c6e44a085cb</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TransactionId__c</fieldItem>
                <identifier>RecordTransactionId__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RequestId__c</fieldItem>
                <identifier>RecordRequestId_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RequestId__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.StartTime__c</fieldItem>
                <identifier>RecordStartTime__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.SystemModeSummary__c</fieldItem>
                <identifier>RecordSystemModeSummary__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TotalLimitsCpuTimeUsed__c</fieldItem>
                <identifier>RecordTotalLimitsCpuTimeUsed__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-d581561a-9af6-472b-bf5b-f191cbaa0836</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TotalLogEntries__c</fieldItem>
                <identifier>RecordTotalLogEntries__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TotalERRORLogEntries__c</fieldItem>
                <identifier>RecordTotalERRORLogEntries__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TotalWARNLogEntries__c</fieldItem>
                <identifier>RecordTotalWARNLogEntries__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ParentLogLink__c</fieldItem>
                <identifier>RecordParentLogLink__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-af147cb3-e6b1-4ef7-a00b-d4746d5a1c90</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-d581561a-9af6-472b-bf5b-f191cbaa0836</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-af147cb3-e6b1-4ef7-a00b-d4746d5a1c90</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-bd93b6fa-7edb-4404-9635-26fa1ec10da5</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OwnerId</fieldItem>
                <identifier>RecordOwnerIdField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Priority__c</fieldItem>
                <identifier>RecordPriority__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Status__c</fieldItem>
                <identifier>RecordStatus__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TransactionScenarioLink__c</fieldItem>
                <identifier>RecordTransactionScenarioLink__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Issue__c</fieldItem>
                <identifier>RecordIssue__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Comments__c</fieldItem>
                <identifier>RecordComments__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-5847b03d-2740-46a4-81cd-e39ffebaf306</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.IsClosed__c</fieldItem>
                <identifier>RecordIsClosed__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.IsResolved__c</fieldItem>
                <identifier>RecordIsResolved__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ClosedBy__c</fieldItem>
                <identifier>RecordClosedBy__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ClosedDate__c</fieldItem>
                <identifier>RecordClosedDate__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LogRetentionDate__c</fieldItem>
                <identifier>RecordLogRetentionDate__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LogPurgeAction__c</fieldItem>
                <identifier>RecordLogPurgeAction_cField1</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-0d174e01-17f8-4c93-8f5a-8983346600f8</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-5847b03d-2740-46a4-81cd-e39ffebaf306</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-0d174e01-17f8-4c93-8f5a-8983346600f8</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column4</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-45c75e70-6963-46be-859e-c1df0d4b5c08</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LoggedByUsernameLink__c</fieldItem>
                <identifier>RecordLoggedByUsernameLink__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LoggedByFederationIdentifier__c</fieldItem>
                <identifier>RecordLoggedByFederationIdentifier_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.HasLoggedByFederationIdentifier__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ImpersonatedByUsernameLink__c</fieldItem>
                <identifier>RecordImpersonatedByUsernameLink_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.ImpersonatedByUsernameLink__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.UserLoggingLevel__c</fieldItem>
                <identifier>RecordUserLoggingLevel__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ProfileLink__c</fieldItem>
                <identifier>RecordProfileLink__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.UserRoleLink__c</fieldItem>
                <identifier>RecordUserRoleLink__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.UserType__c</fieldItem>
                <identifier>RecordUserType__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-c9a75c95-cc0f-4cad-88ea-3ad05e1d03ef</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Locale__c</fieldItem>
                <identifier>RecordLocale__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TimeZoneName__c</fieldItem>
                <identifier>RecordTimeZoneName__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.UserLicenseId__c</fieldItem>
                <identifier>RecordUserLicenseId__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.UserLicenseName__c</fieldItem>
                <identifier>RecordUserLicenseName__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.UserLicenseDefinitionKey__c</fieldItem>
                <identifier>RecordUserLicenseDefinitionKey__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-5f51eb27-d6d6-40bf-aece-d3dedddd86c3</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-c9a75c95-cc0f-4cad-88ea-3ad05e1d03ef</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column5</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-5f51eb27-d6d6-40bf-aece-d3dedddd86c3</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column6</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-12145474-d012-4654-ba48-4100dbbbc893</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.AsyncContextType__c</fieldItem>
                <identifier>RecordAsyncContextType_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-dd5e3466-ea03-4853-be00-33411ed46ec6</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.AsyncContextParentJobId__c</fieldItem>
                <identifier>RecordAsyncContextParentJobId_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.AsyncContextParentJobId__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.AsyncContextChildJobId__c</fieldItem>
                <identifier>RecordAsyncContextChildJobId_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.AsyncContextChildJobId__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.AsyncContextTriggerId__c</fieldItem>
                <identifier>RecordAsyncContextTriggerId_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.AsyncContextTriggerId__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-22c5f237-327a-4fc9-80b3-8305ba0accab</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-dd5e3466-ea03-4853-be00-33411ed46ec6</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column13</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-22c5f237-327a-4fc9-80b3-8305ba0accab</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column14</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-f8d9fe6c-1984-475a-82e2-ceeb68937d16</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ParentSessionId__c</fieldItem>
                <identifier>RecordParentSessionId_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.ParentSessionId__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.SessionId__c</fieldItem>
                <identifier>RecordSessionId__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.SessionType__c</fieldItem>
                <identifier>RecordSessionType__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.SessionSecurityLevel__c</fieldItem>
                <identifier>RecordSessionSecurityLevel__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.SourceIp__c</fieldItem>
                <identifier>RecordSourceIp__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LoginHistoryId__c</fieldItem>
                <identifier>RecordLoginHistoryId__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LoginType__c</fieldItem>
                <identifier>RecordLoginType__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-a13e63e8-930f-4be1-94cf-3e93d3875283</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LoginApplication__c</fieldItem>
                <identifier>RecordLoginApplication__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LoginPlatform__c</fieldItem>
                <identifier>RecordLoginPlatform__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LoginBrowser__c</fieldItem>
                <identifier>RecordLoginBrowser__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ThemeDisplayed__c</fieldItem>
                <identifier>RecordThemeDisplayed__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LogoutUrl__c</fieldItem>
                <identifier>RecordLogoutUrl__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-8166c7e7-f142-4051-8cf3-1c42335d7402</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-a13e63e8-930f-4be1-94cf-3e93d3875283</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column7</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-8166c7e7-f142-4051-8cf3-1c42335d7402</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column8</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-9fb0fd0b-69be-44f7-8c04-fb654709fe28</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.NetworkId__c</fieldItem>
                <identifier>RecordNetworkId__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.NetworkName__c</fieldItem>
                <identifier>RecordNetworkName__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.NetworkUrlPathPrefix__c</fieldItem>
                <identifier>RecordNetworkUrlPathPrefix__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-8be71933-38d1-4afa-adff-fffceb6867e1</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.NetworkSelfRegistrationUrl__c</fieldItem>
                <identifier>RecordNetworkSelfRegistrationUrl__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.NetworkLoginUrl__c</fieldItem>
                <identifier>RecordNetworkLoginUrl__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.NetworkLogoutUrl__c</fieldItem>
                <identifier>RecordNetworkLogoutUrl__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-f457131f-2dab-408e-9965-d730594b7ea2</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-8be71933-38d1-4afa-adff-fffceb6867e1</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column9</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-f457131f-2dab-408e-9965-d730594b7ea2</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column10</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-f8d82d02-dbf4-468f-aae1-45cfb6763435</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LoggerVersionNumber__c</fieldItem>
                <identifier>RecordLoggerVersionNumber__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OrganizationId__c</fieldItem>
                <identifier>RecordOrganizationId__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OrganizationName__c</fieldItem>
                <identifier>RecordOrganizationName__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OrganizationType__c</fieldItem>
                <identifier>RecordOrganizationType__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OrganizationNamespacePrefix__c</fieldItem>
                <identifier>RecordOrganizationNamespacePrefix__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.OrganizationNamespacePrefix__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OrganizationDomainUrl__c</fieldItem>
                <identifier>RecordOrganizationDomainUrl__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-c4a1f400-d677-4197-9c0c-60bb1e5b426d</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OrganizationEnvironmentType__c</fieldItem>
                <identifier>RecordOrganizationEnvironmentType__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OrganizationInstanceName__c</fieldItem>
                <identifier>RecordOrganizationInstanceName__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OrganizationLocation__c</fieldItem>
                <identifier>RecordOrganizationLocation_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OrganizationApiVersion__c</fieldItem>
                <identifier>RecordOrganizationApiVersion_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OrganizationReleaseNumber__c</fieldItem>
                <identifier>RecordOrganizationReleaseNumber_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OrganizationReleaseVersion__c</fieldItem>
                <identifier>RecordOrganizationReleaseVersion_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-7a49dd50-62e1-43c4-a95b-81a04f2e4a8e</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-c4a1f400-d677-4197-9c0c-60bb1e5b426d</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column11</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-7a49dd50-62e1-43c4-a95b-81a04f2e4a8e</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column12</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-d7efb447-a626-4612-8219-5b554c751159</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-bd93b6fa-7edb-4404-9635-26fa1ec10da5</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-45c75e70-6963-46be-859e-c1df0d4b5c08</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Log Management</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-12145474-d012-4654-ba48-4100dbbbc893</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>User Details</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-f8d9fe6c-1984-475a-82e2-ceeb68937d16</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Async Context Details</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection7</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2 OR 3 OR 4</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.AsyncContextType__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Database.BatchableContext</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.AsyncContextType__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>System.FinalizerContext</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.AsyncContextType__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>System.QueueableContext</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.AsyncContextType__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>System.SchedulableContext</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-9fb0fd0b-69be-44f7-8c04-fb654709fe28</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Session Details</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection4</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.SessionId__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-f8d82d02-dbf4-468f-aae1-45cfb6763435</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Experience Cloud Site Details</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection5</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.NetworkId__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-d7efb447-a626-4612-8219-5b554c751159</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Organization Details</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection6</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>force:recordDetailPanelMobile</componentName>
                <identifier>force_recordDetailPanelMobile</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-82b9e667-1aa4-42ed-8a09-ac611351ece9</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentName>logOrganizationLimits</componentName>
                <identifier>c_logOrganizationLimits</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-fb2a30be-0705-474f-aaae-25d2ed03d33c</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Log__c.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Histories</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>30</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListSingleContainer</componentName>
                <identifier>force_relatedListSingleContainer2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-f428f9bd-8335-4c6e-994c-9fb022741a70</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>Log__c.Manage</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>30</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Log__c.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>ChildLogs__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>TransactionId__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>TotalLogEntries__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>SystemModeSummary__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>StartTime__c</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Related Logs</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>StartTime__c</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Descending</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-0bb71d38-e0b0-4931-838e-b2254d40c2ea</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>active</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-0f2e71a4-ac08-400c-8f11-6c6e44a085cb</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Log Entries</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>customTab</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-82b9e667-1aa4-42ed-8a09-ac611351ece9</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.detail</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>detailTab</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-fb2a30be-0705-474f-aaae-25d2ed03d33c</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Organization Limits</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.HasOrganizationLimits__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-f428f9bd-8335-4c6e-994c-9fb022741a70</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>History</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>customTab2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-0bb71d38-e0b0-4931-838e-b2254d40c2ea</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Related Logs</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>customTab3</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-4be58932-ea47-4516-80d6-14dedd7c1fc0</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>numberOfTopicsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>searchHint</name>
                    <value>Type a topic name and press Enter.</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Topics</value>
                </componentInstanceProperties>
                <componentName>forceChatter:topicsOnRecordWrapper</componentName>
                <identifier>forceChatter_topicsOnRecordWrapper</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>Facet-4be58932-ea47-4516-80d6-14dedd7c1fc0</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset</identifier>
            </componentInstance>
        </itemInstances>
        <name>main</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>Nebula Logger: Log Record Page</masterLabel>
    <sobjectType>Log__c</sobjectType>
    <template>
        <name>flexipage:recordHomeSimpleViewTemplate</name>
    </template>
    <type>RecordPage</type>
</FlexiPage>
