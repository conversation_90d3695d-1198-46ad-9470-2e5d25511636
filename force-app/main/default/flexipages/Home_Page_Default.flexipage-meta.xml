<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>dashboardName</name>
                    <value>RITLJlfIhbMuZajQwToLOwkXnuqnzj</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideOnError</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>desktopDashboards:embeddedDashboard</componentName>
                <identifier>desktopDashboards_embeddedDashboard</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>top</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentName>home:recentRecordContainer</componentName>
                <identifier>home_recentRecordContainer</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>bottomLeft</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentName>home:topDealsContainer</componentName>
                <identifier>home_topDealsContainer</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>bottomRight</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentName>runtime_sales_activities:todayTaskContainer</componentName>
                <identifier>runtime_sales_activities_todayTaskContainer</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>home:assistant</componentName>
                <identifier>home_assistant</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>sidebar</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>Home Page Default</masterLabel>
    <parentFlexiPage>home__desktopDefault</parentFlexiPage>
    <template>
        <name>home:desktopTemplate</name>
    </template>
    <type>HomePage</type>
</FlexiPage>
