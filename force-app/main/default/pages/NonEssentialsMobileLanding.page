<apex:page >

    <apex:image id="mobileLandingImage" value="{!URLFOR($Resource.mobileLandingImage)}" width="100%" height="40%"/>
    
    <div class="titleStyle">Aloha!</div>
    <div>
        <div class="messageStyle">Welcome to your Salesforce trial. With Salesforce, your CRM is with you wherever your business takes you.</div>
        <div class="messageStyle messageStyleVariant1">Visit Salesforce on your computer to set up your trial.</div>
    </div>
    
    <div class="svg-display" id="astroSvg"><svg viewBox="0 0 133 181" xmlns="http://www.w3.org/2000/svg" id="Layer_1"><filter y="0%" x="0%" width="100%" id="filter-7" height="100%"><feGaussianBlur in="SourceGraphic"></feGaussianBlur></filter><filter y="0%" x="0%" width="100%" id="filter-6" height="100%"><feGaussianBlur in="SourceGraphic"></feGaussianBlur></filter><filter y="0%" x="0%" width="100%" id="filter-4" height="100%"><feGaussianBlur in="SourceGraphic"></feGaussianBlur></filter><filter y="0%" x="0%" width="100%" id="filter-3" height="100%"><feGaussianBlur in="SourceGraphic"></feGaussianBlur></filter><filter y="0%" x="0%" width="100%" id="filter-2" height="100%"><feGaussianBlur in="SourceGraphic"></feGaussianBlur></filter><filter y="0%" x="0%" width="100%" id="filter-1" height="100%"><feGaussianBlur in="SourceGraphic"></feGaussianBlur></filter><title>Astro</title><g id="EE"><g transform="translate(-230 -145)" id="iPhone-X-Copy"><g transform="translate(8 68)" id="Group-9"><g transform="matrix(-1 0 0 1 355 77)" id="Astro"><g transform="matrix(-1 0 0 1 135 0)"><ellipse class="st0" id="AstroShadow" ry="7.4" rx="33.1" cy="173.5" cx="73.9"></ellipse><g transform="scale(-1 1) rotate(10 -581.478 -285.7)" id="Tail"><path d="M.1 25.4c1.2 2.8 3.1 4 5.9 3.4 1.4-.3 5.7-1.8 7.7-4.1 2.3-2.6 4.4-6.9 4.6-******* 1 2.7.8 4.5.8-2.5 1.5-6.6 6-11.7C29.3 2.8 35.8.9 38.9.3 37.7 1.6 37 1.9 36.3 4c5.2-2.6 11.2-2.9 16.2-.3 6.8 3.4 7.2 11.9 5.8 14.4-1.1-2.1-4.3-4.1-8.2-1.9-3.9 2.2-2.9 8-3 15.1-.1 7.1-2.4 13.5-11.2 19.4 1.3.4 2.8.2 5.4-1-2.6 3.2-10 6.2-15.7 6.6-2.9.2-5-.3-7-.6.8.9 1.8 1.2 3.4 2.3C6.4 57.9-.3 44.5.1 42.9c.2-1.1.2-6.9 0-17.5z" class="st1" id="TailBKGD"></path><g class="st2" id="TailLight3"><path d="M18.2 15c-.1.9-.3 1.6-.4 2.1-.1.4-.5 1.3-1 2.6l5.6 4.9 1.1 9.1 5.4-1.9-1 6.5 6 1.9L35 53.9c1.6-.8 2.7-1.4 3.3-1.7 1.9-1.1 2.6-2.1 2.9-2.4-.5.2-1.8.7-3.4 1-.6.1-1.3.1-2.1-.1 1.9-1.3 3.2-2.3 4-3 .8-.7 1.6-1.6 2.5-2.7L38 35.7l-6-1.6-.4-5.6.1-6.9-3.8 2.3.2-6.1-5.1 3.6-1.7-8.4c-.6 1.1-.9 1.9-1.2 2.4-.2.5-.6 1.8-1.1 3.8 0-.9-.1-1.6-.2-2-.1-.4-.3-1.2-.6-2.2z" class="st3"></path></g><g class="st4" id="TailLight2"><path d="M9.8 34.9l3.8-1.6-1.3 6 3.8 4.2-3.1 6 1.3 2.3L9 54.3l1.6.9h7.3l3.3-2.3-2-5.3 3.9-4.7-5.5-6.3 1.9-9.9-5.5 1.9-2-2.5c-.8.5-1.5.9-1.9 1.1s-.8.4-1 .6l.7 7.1z" class="st3"></path></g><g class="st5" id="TailLight1"><path d="M28.5 8.3l5.7 2.3s.2 4 .7 11.9l2.9-3.2.9 6.2 4 1.2-1.4 9.8 5.5-8.6V25c0-.6 0-1.6.1-2.8l-3.7-2.7-1.1-9.3-4.6 3 1.8-9.5L35 4.9l-.3-3.4c-.7.2-1.3.4-1.6.5-.3.1-.6.3-.8.4l-3.8 5.9z" class="st3"></path></g><linearGradient gradientTransform="matrix(-57.8209 -10.0338 10.1954 -56.9043 101887.563 195767.734)" y2="3034.551" x2="2294.553" y1="3034.913" x1="2296.445" gradientUnits="userSpaceOnUse" id="TailShadow_1_"><stop stop-opacity="0" stop-color="#645143" offset="0"></stop><stop stop-color="#030100" offset="1"></stop></linearGradient><path d="M.1 25.4c1.2 2.8 3.1 4 5.9 3.4 1.4-.3 5.7-1.8 7.7-4.1 2.3-2.6 4.4-6.9 4.6-******* 1 2.7.8 4.5.8-2.5 1.5-6.6 6-11.7C29.3 2.8 35.8.9 38.9.3 37.7 1.6 37 1.9 36.3 4c5.2-2.6 11.2-2.9 16.2-.3 6.8 3.4 7.2 11.9 5.8 14.4-1.1-2.1-4.3-4.1-8.2-1.9-3.9 2.2-2.9 8-3 15.1-.1 7.1-2.4 13.5-11.2 19.4 1.3.4 2.8.2 5.4-1-2.6 3.2-10 6.2-15.7 6.6-2.9.2-5-.3-7-.6.8.9 1.8 1.2 3.4 2.3C6.4 57.9-.3 44.5.1 42.9c.2-1.1.2-6.9 0-17.5z" class="st6" id="TailShadow"></path></g><path d="M84.6 146.6c-2.5 7.7-5.1 12.5-7.6 14.5-2.5 1.9-1.3 5.5 3.8 10.6 7-5.1 12-10.6 15.2-16.4 2.5-4.6 3.5-11.9 2.9-19.3-.1-1.3-.4-2.9-1-4.8l-5.7-1.9-7.6 17.3z" class="st7" id="RightLeg"></path><path d="M83.7 149.1c-2 5.6-3.9 9.1-5.8 10.7-2 1.7-.5 13.7 2.2 11 .5-.5-.4-8.6 0-9.9.3-1 1.6-2.3 2.3-3.1 1-1.2 1.9-2.5 2.8-3.8 2.8-4.4 4.3-11.6 4.2-19 0-1.3-.2-2.9-.6-4.9l-5.6-2.3.5 21.3z" class="st8" id="RightLegShadow"></path><g transform="rotate(-14 450.837 -226.64)" id="Group-3"><path d="M14.8 3.8c12 14.1 19.3 22.2 21.9 24.1 3.8 2.9 5.7 5.8 4.8 7.7-1 1.9-1.9 5.8-9.5 3.9-5.1-1.3-10.5-7.4-16.2-18.3L.5.9l14.3 2.9z" class="st8" id="RightArm"></path><path d="M16.3 5.8c2.8 3 4.9 5.5 6.5 7.3 4.3 5 8.7 9.7 11.6 12.8 2.4 2.5 2.7 6.3 2.3 7.5-1.6 4.4-1.2 4.9-2.6 4.2-1.9-.9-3.9-2.7-6-5.9-1.1-1.7-2.6-4.9-4.8-9.7-.5-1.2-1.5-3.1-2.9-5.8L1.8 4.9l14.5.9z" class="st7" id="RightArmShadow"></path></g><path d="M46.6 85.8c-7 4.5-10.1 8-9.5 10.6.8 3.3 15.2 9.5 15.2 11.6 0 6.8 2.9 18.3 3.8 23.2.6 3.2 2.9 9 6.7 17.4v10.6c0 4.8 0 9.6 1.9 12.5s3.8 4.8 9.5 5.8 7.6-2.9 6.7-7.7S79 163 79 158.2c0-3.2.3-5.8 1-7.7 6.3-1.9 11.1-4.5 14.3-7.7 4.8-4.8 5.7-4.8 3.8-14.5s-10.5-22.2-13.3-25.1c-2.1-1.9-14.8-7.7-38.2-17.4z" class="st7" id="Body"></path><g class="st9" id="BodyShadow1"><path d="M52.8 114.3c1.2-.4 2.1-.6 2.6-.5s1.7.4 3.5 1l-5.8 1.8c-.1-.5-.2-.9-.2-1.1 0-.3 0-.7-.1-1.2z" class="st8"></path></g><g class="st10" id="LegShadow"><path d="M62.8 163.5c.1 1.3.3 2.5.6 3.5.9 2.7 2.6 4.3 4.9 5.7 3.7 2.3 7.7 3.2 11.9 2.8-.7 1.7-3.2 2.3-4.7 2.2-1.5-.1-6.7-.6-9.6-4.2-.6-.7-1.6-2.1-2.3-3.9-.6-2.3-.8-3.7-.8-6.1z" class="st8"></path></g><path d="M87.5 147.5c-.9.3-1.9.6-3.2.8-1.9.3-2 .3-3.5.3-.6 1.3-1 1.9-1 1.9 1.3-.4 2.6-.8 3.9-1.3.7-.3 1.7-.2 2.4-.5.5-.3.9-.6 1.4-1.2z" class="st8" id="BodyShadow2"></path><path d="M126.6 44.2c2.9 7.9 3.5 17.2 1.3 27.6-6.1 26.2-27.7 39.6-58 32-33.2-7.6-46-34.6-39.9-56.9 3.1-11.3 9.4-21.5 19.6-27.6 9.6-31.1 21.4-16.1 27-5.9 4.7.3 9.7 1.1 15.2 2.5 7.7 1.9 14.5 5 20.1 9.1 5.9-2.1 33-10.3 14.7 19.2z" class="st7" id="HeadOutline"></path><path d="M124.4 22.2c2.4.2 3.9.4 4.5.7 2.4 1.1 2.5 1.6 3 2.3-3.3-1.1-7-1.2-11.2-.1-5.1 1.3-8.2 3.2-9.5 5.8C91 19.3 72.9 20.9 57 35.7 45.8 46.1 40 62.6 45.6 80.1c2.3 7.3 5.7 12.5 15.2 18.3 4.3 2.6 10.4 5.4 17.9 7.3-8.2-1.3-13.8-3.3-18.1-4.6-1.3-.4-2.7-.7-4.4-1.1-8-4.5-13.4-8.3-16.3-11.3s-6.8-9.2-11.7-18.4c0-12.8 1-22.3 3.2-28.3 3.4-9.9 9.5-17.6 18.2-23.1C53.2 7 57.6.9 62.7.5c4.4-.2 9.1 4.1 14 12.8 5.7.4 10.9 1.2 15.7 2.5 8.4 2.4 16.3 6.5 19.4 9 4.6-1.6 8.8-2.4 12.6-2.6z" class="st8" id="HeadShadow"></path><path d="M75.2 100.4c21.8 5.4 37.3-4.1 41.6-22.8 4.3-20.7-6.9-35.2-26-39.9-21.8-6.8-38 5.4-42.3 22.8s2.9 34.5 26.7 39.9z" class="st11" id="Face"></path><path d="M64.3 49.2c-3.5 6.2-5.6 10.6-6.4 13.3-3.8 12.7-3.4 23.9 1.6 31.2-2.4-1.8-6.4-5-9.6-11.5-2-4.1-2.8-9.6-2.3-16.5l17.7-25.9-1 9.4z" class="st12" id="Path-10"></path><path d="M55.1 57c0 1.1 0 3.9 2.8 6.7 1-2.9 1-9.6 8.6-15.4 0 .5 7.6 8.7 15.2 7.7-.7-.5-1.9-7.7-.9-11.6 1.9 0 19.6 5.4 24.7 23.1 1-1 3.8-6.8 2.9-12.5 5.7 6.8 4.7 17.3 3.8 19.3 2.4 0 5.5-4 5.5-4.9 0-.2.3-3.3-1-8.4-2.1-8.5-7-13.7-13.1-17.9-6.8-4.6-24-11-37.6-4.6-8.6 4-12.7 10-15.4 15.7-3.8 8.1-3.6 16.8-2.4 22.5.5-5.2 3.1-14.3 6.9-19.7z" class="st13" id="Hair"></path><path d="M73.1 72.3c-.7 3.7-3.4 6.2-6 5.7s-4.1-3.9-3.3-7.5c.7-3.7 3.4-6.2 6-5.7 2.6.4 4 3.8 3.3 7.5" class="st13" id="LeftEye"></path><path d="M108.2 80.2c-.9 3.6-3.7 6-6.3 5.4-2.5-.6-3.8-4-2.9-7.6.9-3.6 3.7-6 6.3-5.4 2.6.6 3.9 4 2.9 7.6" class="st13" id="RightEye"></path><path d="M20 85.8c3.8 9 7.9 15.1 12.4 18.3C46.6 115.8 57 114.8 59 114.8c1.3 0 1.9-4.5 1.9-13.5-7.6-3.9-12-6.8-13.3-8.7s-2.9-6.4-4.8-13.5L20 85.8z" class="st8" id="LeftArmDark"></path><path d="M24.6 83.5c3.2 9.2 6.9 15.6 11.1 19.1 13.4 12.5 23.9 12.3 25.8 12.4 1.3.1 2.2-4.2 2.9-12.9-6.9-2.9-12.1-6-15.4-9.3-3.4-3.3-5.4-7.3-6-12l-18.4 2.7z" class="st7" id="LeftArmLight"></path><path d="M39 84.9c.3 6.2-4.7 9.1-15.2 8.7-1.3-4.2-2.1-8.1-2.6-11.6h15l2.8 2.9z" class="st8" id="LeftArmShadow"></path><path d="M46.7 78.4c4.7-5.2 5.7-8.7 2.4-12.2-1.8-2-4.4-.3-6.4 1.5-1 .9-2.6.4-2.8-1-.5-3.7-2-8.9-6.4-11.4-7.2-4-13.4-3-17.7 3.5-4 6.1-5.5 17.3 2.7 27.7 3.5 4.5 11 5.8 14.9 4.5 6.9-2.4 8.7-7.4 13.3-12.6z" class="st14" id="LeftHand"></path><path d="M15.9 58.8c-4 6.1-5.2 17.1 2.7 27.7 3.3 4.5 11 5.8 14.9 4.5 3.6-1.2 5.8-3.2 7.8-5.7-3.2 1.4-3.7 1.9-9.3 1.4-3.3-.3-7.2-2.4-10-4.7-2.7-2.2-4.8-6.8-5.5-12-.7-3.7-.8-7.5-.6-11.2z" class="st15" id="LeftHandDark"></path><path d="M57.2 20.8C55 16.5 58.4 3.7 64.1 3.3c0 0 2.6.3 3.8 2.1 0 0 9.2 12 2 16.1-6.3 3.6-11.3 2.1-12.7-.7" class="st16" id="InnerEarLeft"></path><path d="M123.1 41.8c3.9-1.3 9.4-11.2 6.1-14.9 0 0-1.8-1.3-3.7-1 0 0-13.1 1.7-10.9 8.7 1.9 5.9 6 8.1 8.5 7.2" class="st17" id="InnerEarRight"></path><path d="M86.7 140.3c6.7-2.4 6.9-9.5 6.3-15.2-.6-5.8-3.8-11.2-9.6-10.7-10.6.5-11.7 7.2-10.3 12.4 1.6 8 6 15 13.6 13.5z" class="st18" id="Tummy"></path></g><g transform="matrix(-1 0 0 1 39 100)" id="Phone"><path d="M38.9 10.1v1.4c0 .2-.1.4-.2.5L7.8 35.3c-.2.2-.6.2-.8 0l-1-.9c-.4-.4-.7-.9-.7-1.4l-.6-4.5 14.1-10L33.1 5.3l5.3 3.9c.3.2.5.5.5.9z" class="st19" id="PhoneSide"></path><path d="M.5 19.6L27.2 1.2c.2-.1.5-.1.7 0l10.6 8c.3.2.3.6.1.8l-.1.1L6.4 34c-.3.2-.6.1-.8-.1l-.1-.1L.2 20.3c-.1-.3 0-.6.3-.7z" class="st20" id="PhoneBody"></path><path d="M3 19.5L26.5 3.2c.1-.1.3-.1.4 0L35 9.8c.1.1.1.3 0 .4l-.1.1L8.3 29.9c-.1.1-.3.1-.4-.1L3 19.9c-.1-.2-.1-.3 0-.4z" class="st21" id="PhoneScreen"></path><path d="M3 19.5l2.3-1.6 5.5 10-2.6 1.9c-.1.1-.3.1-.4-.1l-4.9-9.9c0-.1 0-.2.1-.3z" class="st22" id="PhoneBar"></path><path d="M22.2 6.2l7.8 7.9 5-3.7c.1-.1.2-.3.1-.4l-.1-.1-8.1-6.7c-.1-.1-.3-.1-.4 0l-4.3 3z" class="st21" id="PhoneHeader"></path><path d="M30.1 13.9l4.6-3.5c.1-.1.1-.3 0-.4L28 4.2 25.7 7c-1.8 2.2-4.6 5.5-7.9 9.5-2.4 2.9-5.1 6.1-7.9 9.5-.4.4-.9 1.8-1.5 3.9l21.7-16z" class="st23" id="Combined-Shape"></path></g><g transform="translate(4 112)" id="Hand"><g transform="matrix(-1 0 0 1 25.222 0)" id="Group-4"><path d="M15.4 18.9c4.7-3.3 6.2-5.6 5.7-10.8-1-6.8-10.6-6.3-11.8-2.2-.7 2.6-2.7 5-4.2 6.8-.9 1.1-.9 2.1-.9 3.6 0 4.1 6.5 5.9 11.2 2.6z" class="st14" id="HandBack"></path><path d="M4.8 13.1c0 2.5 2.1 5.5 6.1 5.4 2.4-.1 5.5-1.6 9.1-4-.8 1.4-2.4 3.7-4.7 5.2-2.4 1.5-4.8 2-7.2 1.8S3.5 19 3.5 18.1c0-.8-.6-2.2 1.3-5z" class="st24" id="handBackShadow"></path></g></g></g></g></g></g></svg></div>
    
    <a class="buttonStyle" id="theLink" name="theLink" href="/lightning/o/Task/home">Start Exploring</a>

    <style type="text/css">
        
        .titleStyle{
            font-family: 'Salesforce Sans', Arial, sans-serif;
            margin-left: 1.5rem;
            font-weight: 300;
            font-size: 2rem;
        
            padding-top: 1.25rem;
            padding-bottom: .25rem;
        }

        .messageStyle{
            font-family: 'Salesforce Sans', Arial, sans-serif;
            margin-left: 1.5rem;
            margin-right: 1.5rem;
            font-size: .875rem;
            line-height: 1.5rem;
            padding-top: 1.25rem;
        }
        
        .messageStyleVariant1{
            padding-top: .75rem;
        }
        
        .buttonStyle {
            font-family: 'Salesforce Sans', Arial, sans-serif;
            font-size: .875rem;
            font-weight: 700;
            padding: 1.5rem 0;
            float: left;
            margin: 5%;
            width: 90%;
            text-align: center;
            border-radius: .25rem;
            
            background-color: rgb(21, 137, 238);
            color: rgb(255, 255, 255);
            text-decoration: none !important;
        }
        
        .svg-display {
            width: 133px;
            height: 181px;
            position: absolute;
            top: 13%;
            right: 3%;
        }

        .st0 {
            opacity: .1;
            filter: url(#filter-1);
            enable-background: new
        }
        
        .st1 {
            fill: #735d4f
        }
        
        .st2 {
            filter: url(#filter-2)
        }
        
        .st3 {
            fill: #7e6554
        }
        
        .st4 {
            filter: url(#filter-3)
        }
        
        .st5 {
            filter: url(#filter-4)
        }
        
        .st6 {
            fill: url(#TailShadow_1_)
        }
        
        .st7 {
            fill: #8e735e
        }
        
        .st8 {
            fill: #725d4e
        }
        
        .st9 {
            filter: url(#filter-6)
        }
        
        .st10 {
            filter: url(#filter-7)
        }
        
        .st11 {
            fill: #cfb089
        }
        
        .st12 {
            fill: #c09b76
        }
        
        .st13 {
            fill: #030100
        }
        
        .st14 {
            fill: #9e9f9f
        }
        
        .st15 {
            fill: #8f8f8f
        }
        
        .st16 {
            fill: #645143
        }
        
        .st17 {
            fill: #7d6454
        }
        
        .st18 {
            opacity: .7;
            fill: #725d4e;
            enable-background: new
        }
        
        .st19 {
            fill: #16325c
        }
        
        .st20 {
            fill: #54688d
        }
        
        .st21 {
            fill: #1589ee
        }
        
        .st22 {
            fill: #f4f6f9
        }
        
        .st23 {
            fill: #fff;
            fill-opacity: .2
        }
        
        .st24 {
            fill: #8e8e8e
        }
    </style>
    
</apex:page>