<apex:page id="fsc_screenFlow" showHeader="false" sidebar="false" lightningStylesheets="true">
    <html>
    <head>
        <apex:includeLightning />
    </head>
    <body class="slds-scope">
    <div id="fsc_screenFlow"/>
    <script>
        let statusChange = function (event) {
            console.log('statusChange');
            parent.postMessage({
                flowStatus: event.getParam("status"),
                flowParams: event.getParam("outputVariables"),
                flowOrigin: "{!$CurrentPage.parameters.origin}"
            }, "{!$CurrentPage.parameters.origin}");
        };
        $Lightning.use("c:fsc_screenFlowApp", function () {
            // Create the flow component and set the onstatuschange attribute
            $Lightning.createComponent("lightning:flow", {"onstatuschange": statusChange},
                "fsc_screenFlow",
                function (component) {
                    component.startFlow("{!$CurrentPage.parameters.flowname}", {!$CurrentPage.parameters.params});
                }
            );
        });
    </script>
    </body>
    </html>
</apex:page>