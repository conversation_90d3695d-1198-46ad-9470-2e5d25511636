//
//
//
function rating_desc(rating_code) {

	if (rating_code == "Silver") return "Companies will be stable overall. They will have healthy financials, and a normal history of filing compliance. Gearing will be within an acceptable range, with reasonable levels of liquidity. If the trend is not upward, any declines will be modest, with few if any significant or recent legal notices. Considered to be low risk and open credit is recommended.";
	else if (rating_code == "Gold") return "Companies will be very healthy overall. They will have sound financials, a good history of filing compliance, with ideal levels of gearing (proportion of loan capital), optimal liquidity, and a generally favourable trend, with no significant or recent legal notices. Considered to be very low risk and open credit is recommended.";
	else if (rating_code == "Bronze") return "Companies will be in acceptable health. They may not have published accounts or are a newly formed, but nothing significantly detrimental is known. Where financials are present, gearing may be higher than normal, or liquidity may be lower than ideal. The financial trend may be down, and there may be some history of legal notices. Considered to be a fair trade risk and open credit is recommended.";
	else if (rating_code == "One Red Flag") return "Companies are in the weakest 20% in their size category and display risk factors that might include a deteriorating financial position, sub-optimal gearing/liquidity, and/or the presence of more recent, or significant legal notices. The risk is elevated, and suppliers should seek suitable assurances or guarantees.";
	else if (rating_code == "Two Red Flags") return "Companies are in the weakest 20% in their size category and display risk factors that might include a deteriorating financial position, sub-optimal gearing/liquidity, and/or the presence of more recent, or significant legal notices, but with additional risk factors, such as recent county court judgments of a materially significant value. Very high risk and guarantees advised.";
	else if (rating_code == "Three Red Flags") return "Companies are in the weakest 20% in their size category and display risk factors that might include a deteriorating financial position, sub-optimal gearing/liquidity, and/or the presence of more recent, or significant legal notices, but with additional risk factors, such as recent county court judgments of a materially significant value. Very high risk and guarantees advised, and 56% of companies with three red flags will cease to trade in the next seven days.";
	else if (rating_code == "Amber") return "Companies are newly incorporated or have only passed the lower threshold of the credit score algorithms, and display some financial, payment or filing characteristics that make them an elevated risk. Considered to be moderate risk and open credit is only recommended with caution.";
	else if (rating_code == "Not Trading") return "This simply indicates that according to information filed at Companies House the company had a non- trading status. Should credit be sought in the name of the company care should be taken and a full disclosure sought.";
	else if (rating_code == "Insolvent") return "Company has undergone some form of Insolvency; please check the full record. All credit transactions should be stopped.";
	else if (rating_code == "Strike Off") return "A Striking-Off action at Companies House has been registered against the company or limited liability partnership. This can be voluntary or instigated by Companies House. The full record should be reviewed and if credit is sought then care should be taken and a full disclosure requested to ascertain the current trading status.";
	else if (rating_code == "Dissolved") return "This company has been dissolved by Companies House and no longer exists. Should you be trading with a company in this name you should 1) review your situation immediately, 2) obtain a full disclosure of affairs before entering into any further transactions and 3) ensure you are trading with the correct company going forward.";
	else if (rating_code == "N/A") return "A Health Rating is not currently available.";
	else if (rating_code == "Business Discontinued") return "This business has been discontinued";
    else if (rating_code == "Provisional Bronze") return "Companies will be in acceptable health. They may not have published accounts or are a newly formed, but nothing significantly detrimental is known. Where financials are present, gearing may be higher than normal, or liquidity may be lower than ideal. The financial trend may be down, and there may be some history of legal notices. Considered to be a fair trade risk and open credit is recommended.";
    else if (rating_code == "Provisional Silver") return "Companies will be stable overall. They will have healthy financials, and a normal history of filing compliance. Gearing will be within an acceptable range, with reasonable levels of liquidity. If the trend is not upward, any declines will be modest, with few if any significant or recent legal notices. Considered to be low risk and open credit is recommended.";
    else if (rating_code == "Provisional One Red Flag") return "Companies are in the weakest 20% in their size category and display risk factors that might include a deteriorating financial position, sub-optimal gearing/liquidity, and/or the presence of more recent, or significant legal notices. The risk is elevated, and suppliers should seek suitable assurances or guarantees."; 
    else if (rating_code == "Provisional Two Red Flags") return "Companies are in the weakest 20% in their size category and display risk factors that might include a deteriorating financial position, sub-optimal gearing/liquidity, and/or the presence of more recent, or significant legal notices, but with additional risk factors, such as recent county court judgments of a materially significant value. Very high risk and guarantees advised.";
    else if (rating_code == "Newly Incorporated") return "These companies are newly incorporated and have not filed accounts, as such we do not have the data required to score them, and they remain in this holding rating until the point at which they have the information necessary to score.";
    else if (rating_code == "Pre Insolvent") return "Pre-insolvent companies usually are in the early stages of formally declaring or being declared insolvent and in 98% of circumstances will fail within 28 days. This rating is a pre-cursor to insolvency and as such we would not recommend credit in any circumstance.";
	else return "A Health Rating is not currently available.";
}
/*****************************************************************************************************************************************************************/

function formatDate(date) {
    var d = new Date(date),
    month = '' + (d.getMonth() + 1),
    day = '' + d.getDate(),
    year = d.getFullYear();
    if (month.length < 2) month = '0' + month;
    if (day.length < 2) day = '0' + day;
    return [day, month, year].join('/');
}
/*******************************************************************************************************************************************************************************/

function RatingColour(ip_rating) { 

    if (ip_rating == "Gold") return "box BGGold"; 
    if (ip_rating == "Silver") return "box BGSilver"; 
    if (ip_rating == "Bronze") return "box BGBronze"; 
    if (ip_rating == "Amber")  return "box BGAmber"; 
    if (ip_rating == "One Red Flag" || ip_rating == "Two Red Flags" || ip_rating == "Three Red Flags") return "box BGRed"; 
    if (ip_rating == "Provisional Bronze") return "box BGBronze";
    if (ip_rating == "Provisional Silver") return "box BGSilver"; 
    if (ip_rating == "Provisional One Red Flag") return "box BGRed";
    if (ip_rating == "Provisional Two Red Flags") return "box BGRed";
    if (ip_rating == "Newly Incorporated") return "box BGAmber";
    if (ip_rating == "Pre Insolvent") return "box BGRed";
    if (ip_rating == "Insolvent") return "box BGRed";
    return "box BGGray"; 
}
/****************************************************************************************************************************************************************/

function convertDate(date) {
    var yyyy = date.getFullYear().toString();
    var mm = (date.getMonth()+1).toString();
    var dd  = date.getDate().toString();
    var mmChars = mm.split('');
    var ddChars = dd.split('');
    return yyyy + '-' + (mmChars[1]?mm:"0"+mmChars[0]) + '-' + (ddChars[1]?dd:"0"+ddChars[0]);
}
/*******************************************************************************************************************************************************************************/

const toTitleCase = (phrase) => {
	return phrase
    .toLowerCase()
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

function getratingcode(ip_rfa_rating) 
{
    if (ip_rfa_rating == "GOLD") return "Gold";
    if (ip_rfa_rating == "SILVER") return "Silver";
    if (ip_rfa_rating == "PROVISIONAL_SILVER") return "Provisional Silver";
    if (ip_rfa_rating == "BRONZE") return "Bronze";
    if (ip_rfa_rating == "PROVISIONAL_BRONZE") return "Provisional Bronze";
    if (ip_rfa_rating == "NEWLY_INCORPORATED") return "Newly Incorporated";
    if (ip_rfa_rating == "ONE_RED_FLAG") return "One Red Flag";
    if (ip_rfa_rating == "PROVISIONAL_ONE_RED_FLAG") return "Provisional One Red Flag";
    if (ip_rfa_rating == "TWO_RED_FLAGS") return "Two Red Flags";
    if (ip_rfa_rating == "PROVISIONAL_TWO_RED_FLAGS") return "Provisional Two Red Flags";
    if (ip_rfa_rating == "THREE_RED_FLAGS") return "Three Red Flags";
    if (ip_rfa_rating == "PRE_INSOLVENT") return "Pre Insolvent";
    if (ip_rfa_rating == "INSOLVENT") return "Insolvent";
    if (ip_rfa_rating == "STRIKE_OFF") return "Strike Off";
    if (ip_rfa_rating == "DISSOLVED") return "Dissolved";
    if (ip_rfa_rating == "NOT_TRADING") return "Not Trading";
    return "n/a";
}
/****************************************************************************************************************************************************************/

function CheckAppPermissions(ip_App, ip_Permission) 
{
    if (ip_App) {
        var App_Arr = ip_App.split(",");
        for (var i = 0; i < App_Arr.length ;i++) { if (App_Arr[i] == ip_Permission) return true; }
        return false;
    } else return false;
}
/*******************************************************************************************************************************************************************************/

function PopulateDomainField(ip_htmlthis)
{
    if (ip_htmlthis.website) {
        var Domain = ip_htmlthis.website;
        Domain = Domain.replace("wwww.", "");   
        ip_htmlthis.Domain = Domain;
        if (ip_htmlthis.website == "n/a") {
            if (ip_htmlthis.rfaemail) {
                console.log("Use email domain, as no website domain!");
                var Domain = ip_htmlthis.rfaemail;
                Domain = Domain.split('@').pop();
                ip_htmlthis.Domain = Domain;
            }   
        }
    }
    else if (ip_htmlthis.rfaemail) {
        var Domain = ip_htmlthis.rfaemail;
        Domain = Domain.split('@').pop();
        ip_htmlthis.Domain = Domain;
    }
    ip_htmlthis.Domain_Value = Domain;
}
/****************************************************************************************************************************************************************/

function formatNumber(num) { return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,') }

function date_diff_indays() {
    var dt1 = new Date();
    var dt2 = new Date(dt1.getFullYear(), dt1.getMonth() + 1, 0, 23, 59, 59);
    return Math.floor((Date.UTC(dt2.getFullYear(), dt2.getMonth(), dt2.getDate()) - Date.UTC(dt1.getFullYear(), dt1.getMonth(), dt1.getDate()) ) /(1000 * 60 * 60 * 24));
}
/****************************************************************************************************************************************************************/

function getaddresses(ip_addresses) 
{
    var data = {};
    data.address = null;
    data.type = null;
    if (ip_addresses) {
        for (var x=0; x < ip_addresses.length; x++) {
            if (ip_addresses[x].address_line_1) data.address = toTitleCase(ip_addresses[x].address_line_1);
            if (ip_addresses[x].address_line_2) data.address += " " + toTitleCase(ip_addresses[x].address_line_2);
            if (ip_addresses[x].address_line_3) data.address += " " + toTitleCase(ip_addresses[x].address_line_3);
            if (ip_addresses[x].address_line_4) data.address += " " + toTitleCase(ip_addresses[x].address_line_4);
            if (ip_addresses[x].postcode) data.address += " " + ip_addresses[x].postcode;
            if (ip_addresses[x].type) data.type = ip_addresses[x].type;
            if (ip_addresses[x].type = "trading") break;
        }
    }
    return data;
}
/****************************************************************************************************************************************************************/

function comparedates(ip_date) {
    var inputdate = new Date(ip_date);
    var year = inputdate.getFullYear();
    var month = inputdate.getMonth();
    var day = inputdate.getDate()
    console.log("📘: inputdate", year, month, day);
    var todaysdate = new Date();    
    var tyear = todaysdate.getFullYear();
    var tmonth = todaysdate.getMonth();
    var tday = todaysdate.getDate()
    console.log("📘: today", tyear, tmonth, tday);
    if (year == tyear && month == tmonth && day == tday) {
        return true;
    }
    return false;
}
/****************************************************************************************************************************************************************/

function BuildSICCodes2(ip_searchstring, ip_this, ip_lowercase, ip_siccodes) 
{
    if (ip_siccodes) {
        if (ip_searchstring == "") {
            ip_this.siccodes1class = "displaynone"; ip_this.siccodes2class = "displaynone"; ip_this.siccodes4class = "displaynone"; ip_this.siccodes5class = "displaynone"; ip_this.siccodes6class = "displaynone"; ip_this.siccodes7class = "displaynone";
            ip_this.siccodes8class = "displaynone"; ip_this.siccodes9class = "displaynone"; ip_this.siccodes10class = "displaynone"; ip_this.siccodes11class = "displaynone"; ip_this.siccodes12class = "displaynone";
            ip_this.siccodesvalue1 = "OFF"; ip_this.siccodesvalue2 = "OFF"; ip_this.siccodesvalue3 = "OFF"; ip_this.siccodesvalue4 = "OFF"; ip_this.siccodesvalue5 = "OFF"; ip_this.siccodesvalue6 = "OFF";
            ip_this.siccodesvalue7 = "OFF"; ip_this.siccodesvalue8 = "OFF"; ip_this.siccodesvalue9 = "OFF"; ip_this.siccodesvalue10 = "OFF"; ip_this.siccodesvalue11 = "OFF"; ip_this.siccodesvalue12 = "OFF";
        }
        else {
            if (ip_lowercase == "Y") var searchstring = ip_searchstring.toLowerCase();
            else var searchstring = "(" + ip_searchstring + ")";
            var labelnumber = 1;
            for (var i=0; i < ip_siccodes.length; i++) {
                if (ip_lowercase == "Y") var label = ip_siccodes[i].label.toLowerCase();
                var label = ip_siccodes[i].label;
                if (label.includes(searchstring)) {
                    if (labelnumber == 1) { ip_this.siccodeslabel1 = ip_siccodes[i].label; ip_this.siccodesvalue1 = "OFF"; } if (labelnumber == 2) { ip_this.siccodeslabel2 = ip_siccodes[i].label; ip_this.siccodesvalue2 = "OFF"; }                    
                    if (labelnumber == 3) { ip_this.siccodeslabel3 = ip_siccodes[i].label; ip_this.siccodesvalue3 = "OFF"; } if (labelnumber == 4) { ip_this.siccodeslabel4 = ip_siccodes[i].label; ip_this.siccodesvalue4 = "OFF"; }                    
                    if (labelnumber == 5) { ip_this.siccodeslabel5 = ip_siccodes[i].label; ip_this.siccodesvalue5 = "OFF"; } if (labelnumber == 6) { ip_this.siccodeslabel6 = ip_siccodes[i].label; ip_this.siccodesvalue6 = "OFF"; }                    
                    if (labelnumber == 7) { ip_this.siccodeslabel7 = ip_siccodes[i].label; ip_this.siccodesvalue7 = "OFF"; } if (labelnumber == 8) { ip_this.siccodeslabel8 = ip_siccodes[i].label; ip_this.siccodesvalue8 = "OFF"; }                    
                    if (labelnumber == 9) { ip_this.siccodeslabel9 = ip_siccodes[i].label; ip_this.siccodesvalue9 = "OFF"; } if (labelnumber == 10) { ip_this.siccodeslabel10 = ip_siccodes[i].label; ip_this.siccodesvalue10 = "OFF"; }                    
                    if (labelnumber == 11) { ip_this.siccodeslabel11 = ip_siccodes[i].label; ip_this.siccodesvalue11 = "OFF"; } if (labelnumber == 12) { ip_this.siccodeslabel12 = ip_siccodes[i].label; ip_this.siccodesvalue12 = "OFF"; }                    
                    labelnumber += 1;
                }
            }
            if (labelnumber > 1) ip_this.siccodes1class = "display"; else ip_this.siccodes1class = "displaynone";  if (labelnumber > 2) ip_this.siccodes2class = "display"; else ip_this.siccodes2class = "displaynone";
            if (labelnumber > 3) ip_this.siccodes3class = "display"; else ip_this.siccodes3class = "displaynone";  if (labelnumber > 4) ip_this.siccodes4class = "display"; else ip_this.siccodes4class = "displaynone";
            if (labelnumber > 5) ip_this.siccodes5class = "display"; else ip_this.siccodes5class = "displaynone";  if (labelnumber > 6) ip_this.siccodes6class = "display"; else ip_this.siccodes6class = "displaynone";
            if (labelnumber > 7) ip_this.siccodes7class = "display"; else ip_this.siccodes7class = "displaynone";  if (labelnumber > 8) ip_this.siccodes8class = "display"; else ip_this.siccodes8class = "displaynone";
            if (labelnumber > 9) ip_this.siccodes9class = "display"; else ip_this.siccodes9class = "displaynone";  if (labelnumber > 10) ip_this.siccodes10class = "display"; else ip_this.siccodes10class = "displaynone";
            if (labelnumber > 11) ip_this.siccodes11class = "display"; else ip_this.siccodes11class = "displaynone"; if (labelnumber > 12) ip_this.siccodes12class = "display"; else ip_this.siccodes12class = "displaynone";
            ip_this.siccodesvalue1 = "OFF"; ip_this.siccodesvalue2 = "OFF"; ip_this.siccodesvalue3 = "OFF"; ip_this.siccodesvalue4 = "OFF"; ip_this.siccodesvalue5 = "OFF"; ip_this.siccodesvalue6 = "OFF";
            ip_this.siccodesvalue7 = "OFF"; ip_this.siccodesvalue8 = "OFF"; ip_this.siccodesvalue9 = "OFF"; ip_this.siccodesvalue10 = "OFF"; ip_this.siccodesvalue11 = "OFF"; ip_this.siccodesvalue12 = "OFF";
        }
    }
}

export { rating_desc, formatDate, RatingColour, convertDate, toTitleCase, formatNumber, getratingcode, CheckAppPermissions, PopulateDomainField, date_diff_indays, getaddresses, comparedates, BuildSICCodes2 };