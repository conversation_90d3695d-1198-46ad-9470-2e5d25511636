import { rating_desc, formatDate, RatingColour, convertDate, formatNumber, getratingcode } from './rfafunctions';
import { fb_updatefield } from './rfagooglefunctions';

/*******************************************************************************************************************************************************************************/

function PopulateMainBoxes(ip_this, ip_namespace, ip_fm_fields)
{
    console.log("📘: PopulateMainBoxes - ip_namespace", ip_namespace);

    // namespace not used
    ip_namespace = "";

    //console.log("📘: ip_fm_fields", ip_fm_fields);
    //console.log("📘: ip_this", ip_this);
    //console.log("📘: ip_this.account", ip_this.account);
    //console.log("📘: ip_this.objectApiName", ip_this.objectApiName);

    if (!ip_this.account) {
        console.log("📕: ERROR : PopulateMainBoxes ip_this.account not populated", ip_this); 
        return;
    }
    var index = null;
    var module = "leads";
    if (ip_this.objectApiName == "Account") module = "accounts";
    //
    // Populate Boxes
    //
    ip_this.companydiv1  = "flex-container4 companydiv";
    ip_this.RatingStyleName1 = "box BGGold";
    var VRFARating = null;
    index = fm_get_rfa_field("RFA Rating Code", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value) VRFARating = ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value;
    }
    if (VRFARating) ip_this.rfarating1 = VRFARating;
    else ip_this.rfarating1 = "n/a";
    ip_this.RatingStyleName1 = RatingColour(ip_this.rfarating1);

    var VCreditLimit = null;
    index = fm_get_rfa_field("RFA Credit Limit", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value) VCreditLimit = ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value;
    }
    ip_this.rfacreditlimit1 = "n/a";
    ip_this.creditlimitStyleName1 = "box BGGray";    
    if (VCreditLimit && VCreditLimit > 0) {
        ip_this.rfacreditlimit1       = formatNumber(VCreditLimit);
        ip_this.creditlimitStyleName1 = "box BGGreen";    
    }
    var VCashInBank = null;
    index = fm_get_rfa_field("RFA Cash In Bank", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value) VCashInBank = ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value;
    }
    ip_this.rfacashinbank1 = "n/a";
    ip_this.cashinbankStyleName1 = "box BGGray";    
    if (VCashInBank && VCashInBank > 0) {
        ip_this.rfacashinbank1 = formatNumber(VCashInBank);
        ip_this.cashinbankStyleName1 = "box BGGreen";    
    }
    var VNetWorth = null;
    index = fm_get_rfa_field("RFA Net Worth", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value) VNetWorth = ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value;
    }
    ip_this.rfanetworth1 = "n/a";
    ip_this.networthStyleName1 = "box BGGray";    
    if (VNetWorth) {
        ip_this.rfanetworth1 = formatNumber(VNetWorth);
        var net_worth_int = Number(VNetWorth);
        if (net_worth_int <0) ip_this.networthStyleName1 = "box BGRed";    
        else ip_this.networthStyleName1 = "box BGGreen";    
    }
    var VCreditorDays = null;
    index = fm_get_rfa_field("RFA Creditor Days", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value) VCreditorDays = ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value;
    }
    ip_this.rfacreditordays1 = "n/a";
    ip_this.CreditorDaysStyleName1 = "box BGGray";
    if (VCreditorDays) {
        ip_this.rfacreditordays1 = VCreditorDays;
        if (ip_this.rfacreditordays1 > 30) ip_this.CreditorDaysStyleName1 = "box BGRed";
        else ip_this.CreditorDaysStyleName1 = "box BGGreen";
    }
    var Vccjsamount = null;
    var Vnumberofccjs = null;
    index = fm_get_rfa_field("RFA Number of CCJs", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value) Vnumberofccjs = ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value;
    }
    index = fm_get_rfa_field("RFA CCJs Amount", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value) Vccjsamount = ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value;
    }
    if (ip_this.rfarating1 == "n/a") {
        ip_this.CcjRatingStyleName1 = "box BGGray";    
        ip_this.rfaccjs1            = "n/a";
    }
    else {
        ip_this.CcjRatingStyleName1 = "box BGGreen";    
        ip_this.rfaccjs1            = "None";
        var ccjsamount          = Vccjsamount;
        var numberofccjs        = Vnumberofccjs;
        if (ccjsamount && ccjsamount > 0) {
            ip_this.CcjRatingStyleName1 = "box BGRed";
            ip_this.rfaccjs1 = formatNumber(ccjsamount) + " (" + numberofccjs + ")";
        }  
    }
    var growthscore = null;
    index = fm_get_rfa_field("RFA Growth Score", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value) growthscore = ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value;
    }
    ip_this.GrowthscoreStyleName = "box BGGray";    
    ip_this.growthscore          = "n/a";
    if (growthscore) {
        ip_this.growthscore = growthscore;
        if (growthscore == "Likely" || growthscore == "Very Likely") ip_this.GrowthscoreStyleName = "box BGGreen";
        if (growthscore == "Very Unlikely" || growthscore == "Unlikely") ip_this.GrowthscoreStyleName = "box BGRed";
    }
    ip_this.appstatus = "";
    console.log("📘: FINISHED : PopulateMainBoxes :📘");
}
/****************************************************************************************************************************************************************/

async function fm_dbupdatefield(ip_new_api_field_name, ip_new_status, ip_fm_data_arr, ip_field, ip_sf_fields_arr, ip_namespace) 
{
    console.log("📘: fm_dbupdatefield - namespace", ip_namespace);
    var new_fieldtype = "";
    var custom = null;

    if (ip_new_api_field_name) {
        var new_api_field_name = ip_new_api_field_name;
        // Get new salesforce field name
        for (var i = 0; i < ip_sf_fields_arr.length ;i++) 
        { 
            if (ip_sf_fields_arr[i].value == ip_new_api_field_name) {
                var new_field_name = ip_sf_fields_arr[i].label;
                new_fieldtype  = ip_sf_fields_arr[i].dataType;
                custom         = ip_sf_fields_arr[i].custom;
                break;
            }
        }
    }
    else {
        // Get current field name
        var new_api_field_name = ip_field.sf_field_api_name;
        var new_field_name     = ip_field.sf_field_name;
        new_fieldtype          = ip_field.rfa_field_type;

    }
    console.log("📘: rfa_fieldtype", ip_field.rfa_field_type,"new_fieldtype", new_fieldtype);
    console.log("📘: custom", custom);

    // No Salesforce field has been selected
    if (new_api_field_name == "none" && ip_new_status == "ON") {
        return "NONE";
    }

    if ((ip_field.rfa_field_type == "String") && (new_fieldtype == "String" || new_fieldtype == "TextArea" || new_fieldtype == "Phone")) { // 28/09/2023
    //if ((ip_field.rfa_field_type == "String") && (new_fieldtype == "String" || new_fieldtype == "TextArea")) {
        console.log("📘: Validation passed, String can equal String or TextArea");
    }
    else if (new_fieldtype !== ip_field.rfa_field_type) {

        // 28/09/2023
        if (ip_new_status == "ON") return "INVALIDTYPE";
        else {
            if (!new_field_name) {
                console.log("📘: new_field_name = none");
                new_field_name = "none";
            }
        }
        // 28/09/2023

    }
    console.log("📘: fm_dbupdatefield NEW status", ip_new_status);
    console.log("📘: new_field_name", new_field_name);
    if (custom = true) new_api_field_name = ip_namespace + new_api_field_name;
    console.log("📘: new_api_field_name", new_api_field_name);
   
    for (var i = 0; i < ip_fm_data_arr.length ;i++) 
    { 
        if (ip_field.rfa_field_name == ip_fm_data_arr[i].rfa_field_name) {
            var status = await fb_updatefield(ip_fm_data_arr[i].key, new_api_field_name, new_field_name, ip_new_status);
            if (status == "SUCCESS") {
                console.log("📗: SUCCESS : fb_updatefield");
                return "SUCCESS";
            }
        }
    }
    console.log("📕: FAILED : fb_updatefield");
    return "FAILED";
}
/****************************************************************************************************************************************************************/

function fm_count_on_fields(ip_data_arr)
{
    var count = 0;
    for (var i = 0; i < ip_data_arr.length ;i++) { 
        if (ip_data_arr[i].status == "ON") count += 1;
    }
    return count;
}
/****************************************************************************************************************************************************************/
//
// Build fields to update for leads or accounts
//
function fm_buildupdatefields(ip_this, ip_companydata, ip_directors, ip_fm_fields, ip_namespace, ip_clientname)
{
    var module = "leads";
    if (ip_this.objectApiName == "Account") module = "accounts";
    
    console.log("📘: START : fm_buildupdatefields");

    // Mandatory fields
    var fields = {};
    fields.Id = ip_this.recordId;
    var todaysDate = new Date();
    fields[ip_namespace + "RFA_Update_Date__c"] = convertDate(todaysDate);
    fields[ip_namespace + "CompanyNumber__c"] = ip_companydata.company_number;
    if (ip_companydata.companyid) fields[ip_namespace + "Company_ID__c"] = ip_companydata.companyid.toString();   
    if (ip_companydata.parent_companyid) fields[ip_namespace + "RFA_Parent_Company_ID__c"] = ip_companydata.parent_companyid.toString();        

    // Optional fields
    var index = null;
    ip_namespace = "";

    // 1
    if (ip_companydata.name) {
        index = fm_get_rfa_field("RFA Company name", module, ip_fm_fields);
        if (index !== null) {
            if (ip_clientname.includes("charlesdean")) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.name = ip_companydata.name.toUpperCase();
            else fields[ip_fm_fields[index].sf_field_api_name] = ip_companydata.name;
        }
    }
    // 2
    if (ip_companydata.company_number) {
        index = fm_get_rfa_field("RFA Company number", module, ip_fm_fields);
        if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = ip_companydata.company_number;
    }
    // 3
    if (ip_companydata.website) {
        index = fm_get_rfa_field("RFA Website", module, ip_fm_fields);
        if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = ip_companydata.website;
    }
    // 4
    if (ip_this.rfaphone == "n/a" || ip_this.rfaphone === undefined) { 
        if (ip_companydata.telephone)  {
            index = fm_get_rfa_field("RFA Telephone number", module, ip_fm_fields);
            if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = ip_companydata.telephone;
        }
    }
    // 5
    if (ip_companydata.sic07_group_descriptions) {
        index = fm_get_rfa_field("RFA SIC Description 2", module, ip_fm_fields);
        if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = ip_companydata.sic07_group_descriptions;
    }
    // 6
    if (ip_companydata.sic07_descriptions) {
        index = fm_get_rfa_field("RFA SIC Description", module, ip_fm_fields);
        if (index !== null) {
            var sic07_desc = ip_companydata.sic07_descriptions;
            if (sic07_desc.length > 80) sic07_desc = sic07_desc.substring(0,80);  // SIC Desc is only 80 characters!
            fields[ip_fm_fields[index].sf_field_api_name] = sic07_desc;
        }
    }
    // 7
    var employees = null;
    if (ip_companydata.employees)                employees = ip_companydata.employees;            
    else if (ip_companydata.estimated_employees) employees = ip_companydata.estimated_employees;  
    if (employees) {
        index = fm_get_rfa_field("RFA Employees", module, ip_fm_fields);
        if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = employees;
    }
    // 8
    if (ip_companydata.sic07_codes) {
        index = fm_get_rfa_field("RFA SIC Codes", module, ip_fm_fields);
        if (index !== null) {
            var sic07_codes = ip_companydata.sic07_codes;
            if(sic07_codes.length > 20) sic07_codes = sic07_codes.substring(0,255);  
            fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = sic07_codes;
        }
    }
    // 9
    if (ip_companydata.directors) {
        index = fm_get_rfa_field("RFA Directors", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.directors;
    }
    // 10
    if (ip_companydata.generated_rfa_rating) {
        index = fm_get_rfa_field("RFA Rating Code", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = getratingcode(ip_companydata.generated_rfa_rating);
    }
    // 11
    if (ip_companydata.creditor_days) {
        index = fm_get_rfa_field("RFA Creditor Days", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.creditor_days;
    }
    // 12
    if (ip_companydata.incorporation_date) {
        index = fm_get_rfa_field("RFA Incorporation Date", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.incorporation_date;
    }
    // 13
    if (ip_companydata.cash_in_bank) {
        index = fm_get_rfa_field("RFA Cash In Bank", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.cash_in_bank;
    }
    // 14
    if (ip_companydata.net_worth) {
        index = fm_get_rfa_field("RFA Net Worth", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.net_worth;
    }
    // 15
    // Don't update email if value exists
    if (ip_this.rfaemail == "n/a" || ip_this.rfaemail === undefined) {
        var namespace = "";
        if (module == "accounts") namespace = ip_namespace;
        if (ip_companydata.email) {
            index = fm_get_rfa_field("RFA Email", module, ip_fm_fields);
            if (index !== null) fields[namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.email;
        }
    }
    // 16
    /*
    var turnover = null;
    if (ip_companydata.turnover)                 turnover = ip_companydata.turnover;            
    else if (ip_companydata.estimated_turnover)  turnover = ip_companydata.estimated_turnover;      
    if (turnover) {
        index = fm_get_rfa_field("RFA Turnover", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = turnover;
    }
    */
    if (ip_companydata.turnover) {
        index = fm_get_rfa_field("RFA Turnover", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.turnover;
        // If estimated is mapped, set to null.
        index = fm_get_rfa_field("RFA Estimated Turnover", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    }
    if (ip_companydata.estimated_turnover) {
        index = fm_get_rfa_field("RFA Estimated Turnover", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.estimated_turnover;
        // If estimated is mapped, set to null.
        index = fm_get_rfa_field("RFA Turnover", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    }
    // 17
    if (ip_companydata.credit_limit) {
        index = fm_get_rfa_field("RFA Credit Limit", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.credit_limit;
    }
    // 18
    if (ip_companydata.latest_action) {
        index = fm_get_rfa_field("RFA Latest Action", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.latest_action;
    }
    // 19
    if (ip_companydata.latest_action_date) {
        if (ip_companydata.latest_action_date !== "n/a") {
            index = fm_get_rfa_field("RFA Latest Action Date", module, ip_fm_fields);
            if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.latest_action_date;
        }
    }
    // 20
    if (ip_companydata.vat_number) {
        index = fm_get_rfa_field("RFA VAT Number", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.vat_number;
    }
    // 21
    if (ip_companydata.sic07_group_descriptions) {
        index = fm_get_rfa_field("RFA SIC Group Description", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.sic07_group_descriptions;
    }
    // 22
    if (ip_companydata.last_filed_accounts) {
        index = fm_get_rfa_field("RFA Latest Accounts Filed Date", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.last_filed_accounts;
    }
    // 23
    if (ip_companydata.number_of_ccjs == 0) {}
    else {
        index = fm_get_rfa_field("RFA Number of CCJs", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.number_of_ccjs;
        index = fm_get_rfa_field("RFA CCJs Amount", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.ccjs_amount;
    }

    if (ip_clientname.includes("charlesdean")) {
        console.log("📘: Update for Charles Dean V3");
        if (ip_companydata.company_number) fields.Company_Registration_Number__c = ip_companydata.company_number;
        if (ip_companydata.incorporation_date) fields.Date_Established__c = ip_companydata.incorporation_date;    
        if (ip_companydata.sic07_codes) {
            var sic07_codes = ip_companydata.sic07_codes;
            if(sic07_codes.length > 255) sic07_codes = sic07_codes.substring(0,255);  
            fields.SIC_Codes__c = sic07_codes;
        }
        if (ip_companydata.sic07_group_descriptions) fields.SicDesc = ip_companydata.sic07_group_descriptions.substring(0,255);    
        if (ip_directors) {
            if (ip_directors.name) fields.Director_Full_Name__c = ip_directors.name;
            if (ip_directors.address) {
                if (htmlthis.account.fields.Director_address__c && ip_this.account.fields.Director_address__c.value) console.log("📘: Director_address__c - has value, don't update!");                                
                else fields.Director_address__c = ip_directors.address;    
            }
            if (ip_directors.dob) {
                if (ip_this.account.fields.Director_DOB__c && ip_this.account.fields.Director_DOB__c.value) console.log("📘: Director_DOB__c - has value, don't update!");                                
                else fields.Director_DOB__c = ip_directors.dob;
            }
            if (ip_directors.name2) fields.Director_2_Full_Name__c = ip_directors.name2;
            if (ip_directors.address2) {
                if (ip_this.account.fields.Director_2_Address__c && ip_this.account.fields.Director_2_Address__c.value) console.log("📘: Director_2_Address__c - has value, don't update!");                                
                else fields.Director_2_Address__c = ip_directors.address2;
            }
            if (ip_directors.dob2) {
                if (ip_this.account.fields.Director_2_DOB__c && ip_this.account.fields.Director_2_DOB__c.value) console.log("📘: Director_2_DOB__c - has value, don't update!");                                
                else fields.Director_2_DOB__c = ip_directors.dob2;
            }
            if (ip_directors.name3) fields.Director_3_Full_Name__c = ip_directors.name3;
            if (ip_directors.address3) {
                if (ip_this.account.fields.Director_3_Address__c && ip_this.account.fields.Director_3_Address__c.value) console.log("📘: Director_3_Address__c - has value, don't update!");                                
                else fields.Director_3_Address__c = ip_directors.address3;
            }
            if (ip_directors.dob3) {
                if (ip_this.account.fields.Director_3_DOB__c && ip_this.account.fields.Director_3_DOB__c.value) console.log("📘: Director_3_DOB__c - has value, don't update!");                                
                else fields.Director_3_DOB__c = ip_directors.dob3;
            }
            if (ip_directors.name4) fields.Director_4_Full_Name__c = ip_directors.name4;
            if (ip_directors.address4) {
                if (ip_this.account.fields.Director_4_Address__c && ip_this.account.fields.Director_4_Address__c.value) console.log("📘: Director_4_Address__c - has value, don't update!");                                
                else fields.Director_4_Address__c = ip_directors.address4;
            }
            if (ip_directors.dob4) {
                if (ip_this.account.fields.Director_4_DOB__c && ip_this.account.fields.Director_4_DOB__c.value) console.log("📘: Director_4_DOB__c - has value, don't update!");                                
                else fields.Director_4_DOB__c = ip_directors.dob4;
            }
        }
    }
    // 24
    if (ip_companydata.parent_company_number) {
        index = fm_get_rfa_field("RFA Parent Company Number", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.parent_company_number;
    }
    // 25
    if (ip_companydata.parent_companyname) {
        index = fm_get_rfa_field("RFA Parent Company Name", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.parent_companyname;
    }
    // 26
    var ShippingStreet = null;
    if (ip_companydata.rfa_trading_address_1) ShippingStreet     = ip_companydata.rfa_trading_address_1;
    if (ip_companydata.rfa_trading_address_2) ShippingStreet    += "," + ip_companydata.rfa_trading_address_2;
    if (ip_companydata.rfa_trading_address_3) ShippingStreet    += "," + ip_companydata.rfa_trading_address_3;
    if (ShippingStreet) {
        index = fm_get_rfa_field("RFA Trading Street", module, ip_fm_fields);
        if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = ShippingStreet;
    }
    // 27
    if (ip_companydata.rfa_trading_town) {
        index = fm_get_rfa_field("RFA Trading Town", module, ip_fm_fields);
        if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = ip_companydata.rfa_trading_town;
    }
    // 28
    if (ip_companydata.rfa_trading_postcode) {
        index = fm_get_rfa_field("RFA Trading Postcode", module, ip_fm_fields);
        if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = ip_companydata.rfa_trading_postcode;
    }
    // 29
    if (ip_companydata.rfa_trading_country) {
        index = fm_get_rfa_field("RFA Trading Country", module, ip_fm_fields);
        if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = ip_companydata.rfa_trading_country;
    }
    // 30
    var BillingStreet = null;
    if (ip_companydata.rfa_reg_address_1) BillingStreet     = ip_companydata.rfa_reg_address_1;
    if (ip_companydata.rfa_reg_address_2) BillingStreet    += "," + ip_companydata.rfa_reg_address_2;
    if (ip_companydata.rfa_reg_address_3) BillingStreet    += "," + ip_companydata.rfa_reg_address_3;
    if (BillingStreet) {
        index = fm_get_rfa_field("RFA Registered Street", module, ip_fm_fields);
        if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = BillingStreet;
    }
    // 31
    if (ip_companydata.rfa_reg_town) {
        index = fm_get_rfa_field("RFA Registered Town", module, ip_fm_fields);
        if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = ip_companydata.rfa_reg_town;
    }
    // 32
    if (ip_companydata.rfa_reg_postcode) {
        index = fm_get_rfa_field("RFA Registered Postcode", module, ip_fm_fields);
        if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = ip_companydata.rfa_reg_postcode;
    }
    // 33
    if (ip_companydata.rfa_reg_country) {
        index = fm_get_rfa_field("RFA Registered Country", module, ip_fm_fields);
        if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = ip_companydata.rfa_reg_country;
    }
    // 34
    if (ip_companydata.trading_address) {
        index = fm_get_rfa_field("RFA Trading Address", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.trading_address;
    }
    // Other Fields
    // 35
    if (ip_companydata.net_from_operating_activities) {
        index = fm_get_rfa_field("RFA Net From Operating Activities", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.net_from_operating_activities;
    }
    // 36
    if (ip_companydata.net_from_return_investment_servicing) {
        index = fm_get_rfa_field("RFA Net From Return Investment Servicing", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.net_from_return_investment_servicing;
    }
    // 37
    if (ip_companydata.increase_in_cash) {
        index = fm_get_rfa_field("RFA Increase In Cash", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.increase_in_cash;
    }
    // 38
    if (ip_companydata.net_before_financing) {
        index = fm_get_rfa_field("RFA Net Before Financing", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.net_before_financing;
    }
    // 39
    if (ip_companydata.net_from_financing) {
        index = fm_get_rfa_field("RFA Net From Financing", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.net_from_financing;
    }
    // 40
    if (ip_companydata.working_capital_turnover) {
        index = fm_get_rfa_field("RFA Working Capital Turnover", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.working_capital_turnover;
    }
    // 41
    if (ip_companydata.total_current_assets) {
        index = fm_get_rfa_field("RFA Total Current Assets", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.total_current_assets;
    }
    // 42
    if (ip_companydata.total_fixed_assets) {
        index = fm_get_rfa_field("RFA Total Fixed Assets", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.total_fixed_assets;
    }
    // 43
    if (ip_companydata.tangible_fixed_assets) {
        index = fm_get_rfa_field("RFA Tangible Fixed Assets", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.tangible_fixed_assets;
    }
    // 44
    if (ip_companydata.pl_account_reserve) {
        index = fm_get_rfa_field("RFA PL Account Reserve", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.pl_account_reserve;
    }
    // 45
    if (ip_companydata.working_capital) {
        index = fm_get_rfa_field("RFA Working Capital", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.working_capital;
    }
    // 46
    if (ip_companydata.trade_debtors) {
        index = fm_get_rfa_field("RFA Trade Debtors", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.trade_debtors;
    }
    // 47
    if (ip_companydata.shareholders_funds) {
        index = fm_get_rfa_field("RFA Shareholders Funds", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.shareholders_funds;
    }
    // 48
    if (ip_companydata.stocks) {
        index = fm_get_rfa_field("RFA Stocks", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.stocks;
    }
    // 49
    if (ip_companydata.misc_current_assets) {
        index = fm_get_rfa_field("RFA Misc Current Assets", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.misc_current_assets;
    }
    // 50
    if (ip_companydata.total_long_term_liabilities) {
        index = fm_get_rfa_field("RFA Total Long Term Liabilities", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.total_long_term_liabilities;
    }
    // 51
    if (ip_companydata.creditors_amounts_falling) {
        index = fm_get_rfa_field("RFA Creditors Amounts Falling", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.creditors_amounts_falling;
    }
    // 52
    if (ip_companydata.total_assets_less_liabilities) {
        index = fm_get_rfa_field("RFA Total Assets Less Liabilities", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.total_assets_less_liabilities;
    }
    // 53
    if (ip_companydata.revaluation_reserve) {
        index = fm_get_rfa_field("RFA Revaluation Reserve", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.revaluation_reserve;
    }
    // 54
    if (ip_companydata.share_capital_reserve) {
        index = fm_get_rfa_field("RFA Share Capital Reserve", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.share_capital_reserve;
    }
    // 55
    if (ip_companydata.total_liabilities) {
        index = fm_get_rfa_field("RFA Total Liabilities", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.total_liabilities;
    }
    // 56
    if (ip_companydata.total_assets) {
        index = fm_get_rfa_field("RFA Total Assets", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.total_assets;
    }
    // 57
    if (ip_companydata.contingent_liabilities) {
        index = fm_get_rfa_field("RFA Contingent Liabilities", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.contingent_liabilities;
    }
    // 58
    if (ip_companydata.intangible_assets) {
        index = fm_get_rfa_field("RFA Intangible Assets", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.intangible_assets;
    }
    // 59
    if (ip_companydata.pre_tax_profit) {
        index = fm_get_rfa_field("RFA Pre Tax Profit", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.pre_tax_profit;
    }
    // 60
    if (ip_companydata.director_renum) {
        index = fm_get_rfa_field("RFA Directors Remuneration", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.director_renum;
    }
    // 61
    if (ip_companydata.retained_profit) {
        index = fm_get_rfa_field("RFA Retained Profit", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.retained_profit;
    }
    // 62
    if (ip_companydata.audit_fee) {
        index = fm_get_rfa_field("RFA Audit Fee", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.audit_fee;
    }
    // 63
    if (ip_companydata.interest_payable) {
        index = fm_get_rfa_field("RFA Interest Payable", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.interest_payable;
    }
    // 64
    if (ip_companydata.non_trading_income) {
        index = fm_get_rfa_field("RFA Non Trading Income", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.non_trading_income;
    }
    // 65
    if (ip_companydata.operating_profit) {
        index = fm_get_rfa_field("RFA Operating Profit", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.operating_profit;
    }
    // 66
    if (ip_companydata.dividends) {
        index = fm_get_rfa_field("RFA Dividends", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.dividends;
    }
    // 67
    if (ip_companydata.taxation) {
        index = fm_get_rfa_field("RFA Taxation", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.taxation;
    }
    // 68
    if (ip_companydata.gross_profit) {
        index = fm_get_rfa_field("RFA Gross Profit", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.gross_profit;
    }
    // 69
    if (ip_companydata.non_audit_fee) {
        index = fm_get_rfa_field("RFA Non Audit Fee", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.non_audit_fee;
    }
    // 70
    if (ip_companydata.post_tax_profit) {
        index = fm_get_rfa_field("RFA Post Tax Profit", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.post_tax_profit;
    }
    // 71
    if (ip_companydata.value_added) {
        index = fm_get_rfa_field("RFA Value Added", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.value_added;
    }
    // 72
    if (ip_companydata.cost_of_sales) {
        index = fm_get_rfa_field("RFA Cost Of Sales", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.cost_of_sales;
    }
    // 73
    if (ip_companydata.auditor) {
        index = fm_get_rfa_field("RFA Auditor", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.auditor;
    }
    // 74
    if (ip_companydata.growth_score) {
        index = fm_get_rfa_field("RFA Growth Score", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = ip_companydata.growth_score;
    }
    // 75
    if (ip_companydata.company_type) {
        index = fm_get_rfa_field("RFA Company Type", module, ip_fm_fields);
        if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = ip_companydata.company_type;
    }
    // 76
    if (ip_companydata.numberdirectors) {
        index = fm_get_rfa_field("RFA Number of Active Directors", module, ip_fm_fields);
        if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = ip_companydata.numberdirectors;
    }
    // 77
    if (ip_companydata.Satisified) {
        index = fm_get_rfa_field("RFA CCJs Satisfied", module, ip_fm_fields);
        if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = ip_companydata.Satisified;
    }
    // 78
    if (ip_companydata.tps_registered) {
        index = fm_get_rfa_field("RFA TPS Registered", module, ip_fm_fields);
        if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = ip_companydata.tps_registered;
    }
    // 79
    if (ip_companydata.ebitda) {
        index = fm_get_rfa_field("RFA EBITDA", module, ip_fm_fields);
        if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = ip_companydata.ebitda;
    }
    return fields;
}
/****************************************************************************************************************************************************************/
//
// Display fields using field mapping for leads and accounts
//
function fm_builduifields(ip_this, ip_fm_fields, ip_namespace)
{
    if (!ip_this.account) return;
    var index = null;
    console.log("📘: fm_builduifields - ip_this.account.fields", ip_this.account.fields);
    //
    // Mandatory fields
    //
    if (ip_this.account.fields[ip_namespace + "CompanyNumber__c"]) ip_this.rfa_company_number = ip_this.account.fields[ip_namespace + "CompanyNumber__c"].value;
    else ip_this.rfa_company_number = "n/a";
    if (ip_this.account.fields[ip_namespace + "RFA_Update_Date__c"].value) ip_this.rfaupdatedate = formatDate(ip_this.account.fields[ip_namespace + "RFA_Update_Date__c"].value);
    else ip_this.rfaupdatedate = "n/a";
    //
    // Mapping fields
    //
    var module = "leads";
    if (ip_this.objectApiName == "Account") module = "accounts";

    ip_this.rfa_company_name = "n/a";
    index = fm_get_rfa_field("RFA Company name", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_fm_fields[index].sf_field_api_name].value) {
            ip_this.CompanyName       = ip_this.account.fields[ip_fm_fields[index].sf_field_api_name].value;
            ip_this.CompanyName_Value = ip_this.account.fields[ip_fm_fields[index].sf_field_api_name].value;
            ip_this.rfa_company_name  = ip_this.account.fields[ip_fm_fields[index].sf_field_api_name].value;
            const splitString = ip_this.rfa_company_name.split(" ");
            var string = "";
            for (var i = 0; i < splitString.length; i++) { string += splitString[i] + "%20"; }
            ip_this.companylinkedinurl = "https://www.linkedin.com/search/results/companies/?keywords=" + string + "&origin=SWITCH_SEARCH_VERTICAL";
            ip_this.companylinkedin = ip_this.rfa_company_name;

            ip_this.parentcompanynumber = "n/a";
            var parent_company_number = null;
            index = fm_get_rfa_field("RFA Parent Company Number", module, ip_fm_fields);
            if (index !== null) {
                if (ip_this.account.fields[ip_fm_fields[index].sf_field_api_name].value) parent_company_number = ip_this.account.fields[ip_fm_fields[index].sf_field_api_name].value;
            }
            var parent_company_name = null;
            index = fm_get_rfa_field("RFA Parent Company Name", module, ip_fm_fields);
            if (index !== null) {
                if (ip_this.account.fields[ip_fm_fields[index].sf_field_api_name].value) parent_company_name = ip_this.account.fields[ip_fm_fields[index].sf_field_api_name].value;
            }
            if (parent_company_number && parent_company_name) {
                ip_this.parentcompanynumber = parent_company_number;
                ip_this.parentcompanynumber = parent_company_name + " (" + parent_company_number + ")";
                ip_this.parentcompanynumberurl = "https://app.redflagalert.net/app/check/report/" + ip_this.account.fields[ip_namespace + "RFA_Parent_Company_ID__c"].value + "/GBR";
            }
        }
    }
    ip_namespace = "";

    ip_this.website = "n/a";
    index = fm_get_rfa_field("RFA Website", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_fm_fields[index].sf_field_api_name].value) {
            ip_this.website = ip_this.account.fields[ip_fm_fields[index].sf_field_api_name].value;
            if (ip_this.website.includes("https")) {}
            else ip_this.websiteurl = "https://" + ip_this.website;
        }
    }
    ip_this.rfaphone = "n/a";
    index = fm_get_rfa_field("RFA Telephone number", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_fm_fields[index].sf_field_api_name].value) ip_this.rfaphone = ip_this.account.fields[ip_fm_fields[index].sf_field_api_name].value;
    }
    ip_this.rfasiccodes = "n/a";
    index = fm_get_rfa_field("RFA SIC Codes", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value) ip_this.rfasiccodes = ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value;
    }
    ip_this.rfasicdesc = "n/a";
    index = fm_get_rfa_field("RFA SIC Description", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_fm_fields[index].sf_field_api_name].value) ip_this.rfasicdesc = ip_this.account.fields[ip_fm_fields[index].sf_field_api_name].value;
    }
    ip_this.employees = "n/a";
    index = fm_get_rfa_field("RFA Employees", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_fm_fields[index].sf_field_api_name].value) ip_this.employees = formatNumber(ip_this.account.fields[ip_fm_fields[index].sf_field_api_name].value);
    }
    // Email
    var namespace = "";
    if (ip_this.objectApiName == "Account") namespace = ip_namespace;
    ip_this.rfaemail = "n/a";
    index = fm_get_rfa_field("RFA Email", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[namespace + ip_fm_fields[index].sf_field_api_name].value) ip_this.rfaemail = ip_this.account.fields[namespace + ip_fm_fields[index].sf_field_api_name].value;
    }
    // Directors
    ip_this.Directors = "n/a"; 
    ip_this.personlinkedinurl  = ""; 
    ip_this.personlinkedin = "n/a";
    index = fm_get_rfa_field("RFA Directors", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value) {

            ip_this.Directors = ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value;
            var director_arr = ip_this.Directors.split(", "); 

            for (var i = 0; i < director_arr.length ;i++) {
                if (director_arr[i].includes("null")) { continue };
                var res = director_arr[i].split("(");
                var director_name = res[0].trim();
                if (res.length > 1) occupation = res[1].slice(0, -1); // We have an occupation 
                const splitString = director_name.split(" ");
                var string = splitString[0] + "%20"; 	// Only get first and last name for Search
                string += splitString[splitString.length-1] + "%20";
                var person_url = "https://www.linkedin.com/search/results/people/?" + "keywords=" + string + "&" + "origin=GLOBAL_SEARCH_HEADER&" + "page=1&" + "refresh=false&" + "skillExplicit=%5B%5D&" + "topic=%5B%5D";
                ip_this.personlinkedinurl = person_url;
                ip_this.personlinkedin = splitString[0] + ' ' + splitString[splitString.length-1];
                break;
            }
        }
    }
    ip_this.turnovertitle = "Turnover";
    ip_this.rfaturnover = "n/a";
    index = fm_get_rfa_field("RFA Turnover", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value) {
            ip_this.rfaturnover = formatNumber(ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value);
            ip_this.turnovertitle = "Actual Turnover";
        }
    }
    index = fm_get_rfa_field("RFA Estimated Turnover", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value) {
            ip_this.rfaturnover = formatNumber(ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value);
            ip_this.turnovertitle = "Estimated Turnover";
        }
    }
    ip_this.rfarating = "n/a";
    index = fm_get_rfa_field("RFA Rating Code", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value) ip_this.rfarating = ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value;
    }
    ip_this.RatingStyleName = RatingColour(ip_this.rfarating);
    ip_this.rfaratingdesc = rating_desc(ip_this.rfarating);

    ip_this.rfaincorporationdate = "n/a";
    index = fm_get_rfa_field("RFA Incorporation Date", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value) ip_this.rfaincorporationdate = formatDate(ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value);
    }
     var latestactiondate = null;
    index = fm_get_rfa_field("RFA Latest Action Date", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value) latestactiondate = ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value;
    }
    ip_this.rfalastaction = "n/a";
    index = fm_get_rfa_field("RFA Latest Action", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value) {
            if (latestactiondate) ip_this.rfalastaction = ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value + " (" + formatDate(latestactiondate) + ")";
            ip_this.rfalastaction = ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value;
        }
    }
    ip_this.rfavatnumber = "n/a";
    index = fm_get_rfa_field("RFA VAT Number", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value) ip_this.rfavatnumber = ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value;
    }
    ip_this.sicgroupdesc = "n/a";
    index = fm_get_rfa_field("RFA SIC Group Description", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value) ip_this.sicgroupdesc = ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value;
    }
    ip_this.lastfiledaccounts = "n/a";
    index = fm_get_rfa_field("RFA Latest Accounts Filed Date", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value) ip_this.lastfiledaccounts = formatDate(ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value);
    }
    var ccjsamount = null;
    var numberofccjs = null;
    index = fm_get_rfa_field("RFA Number of CCJs", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value) numberofccjs = ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value;
    }
    index = fm_get_rfa_field("RFA CCJs Amount", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value) ccjsamount = ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value;
    }
    ip_this.CcjRatingStyleName = "box BGGreen";    
    ip_this.rfaccjs = "None";
    if (ccjsamount && ccjsamount > 0) {
        ip_this.CcjRatingStyleName = "box BGRed";
        ip_this.rfaccjs = formatNumber(ccjsamount) + " (" + numberofccjs + ")";
    }  
    ip_this.rfacreditlimit = "n/a";
    ip_this.creditlimitStyleName = "box BGGray";    
    index = fm_get_rfa_field("RFA Credit Limit", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value) {
            ip_this.rfacreditlimit = formatNumber(ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value);
            if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value > 0) ip_this.creditlimitStyleName = "box BGGreen";    
        }
    }
    ip_this.rfacashinbank = "n/a";
    ip_this.cashinbankStyleName = "box BGGray";    
    index = fm_get_rfa_field("RFA Cash In Bank", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value) {
            ip_this.rfacashinbank = formatNumber(ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value);
            if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value > 0) ip_this.cashinbankStyleName = "box BGGreen";    
        }
    }
    ip_this.rfanetworth = "n/a";
    ip_this.networthStyleName = "box BGGray";    
    index = fm_get_rfa_field("RFA Net Worth", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value) {
            var networth = Number(ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value);
            ip_this.rfanetworth = formatNumber(networth);
            if (networth > 0) ip_this.networthStyleName = "box BGGreen";    
            if (networth < 0) ip_this.networthStyleName = "box BGRed";    
        }
    }
    ip_this.rfacreditordays = "n/a";
    ip_this.CreditorDaysStyleName = "box BGGray";
    index = fm_get_rfa_field("RFA Creditor Days", module, ip_fm_fields);
    if (index !== null) {
        if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value) {
            var creditordays = ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value;
            ip_this.rfacreditordays = formatNumber(creditordays);
            if (creditordays > 30) ip_this.CreditorDaysStyleName = "box BGRed";
            else ip_this.CreditorDaysStyleName = "box BGGreen";
        }
    }
    ip_this.rfaaddress = "n/a";
    if (ip_this.objectApiName == "Account") {
        index = fm_get_rfa_field("RFA Trading Address", module, ip_fm_fields);
        if (index !== null) {
            if (ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value) ip_this.rfaaddress = ip_this.account.fields[ip_namespace + ip_fm_fields[index].sf_field_api_name].value;
        }
        console.log("📘: ip_this.rfaaddress", ip_this.rfaaddress);
    }
    else {
        // No Namespace
        var tradingstreet = null;
        index = fm_get_rfa_field("RFA Trading Street", module, ip_fm_fields);
        if (index !== null) {
            if (ip_this.account.fields[ip_fm_fields[index].sf_field_api_name].value) {
                tradingstreet = ip_this.account.fields[ip_fm_fields[index].sf_field_api_name].value;
            }
        }
        //console.log("📘:tradingstreet", tradingstreet);

        var tradingtown = null;
        index = fm_get_rfa_field("RFA Trading Town", module, ip_fm_fields);
        if (index !== null) {
            if (ip_this.account.fields[ip_fm_fields[index].sf_field_api_name].value) {
                tradingtown = ip_this.account.fields[ip_fm_fields[index].sf_field_api_name].value;
            }
        }
        //console.log("📘:tradingtown", tradingtown);

        var tradingpostcode = null;
        index = fm_get_rfa_field("RFA Trading Postcode", module, ip_fm_fields);
        if (index !== null) {
            if (ip_this.account.fields[ip_fm_fields[index].sf_field_api_name].value) {
                tradingpostcode = ip_this.account.fields[ip_fm_fields[index].sf_field_api_name].value;
            }
        }
        //console.log("📘:tradingpostcode", tradingpostcode);

        var tradingcountry = null;
        index = fm_get_rfa_field("RFA Trading Country", module, ip_fm_fields);
        if (index !== null) {
            if (ip_this.account.fields[ip_fm_fields[index].sf_field_api_name].value) {
                tradingcountry = ip_this.account.fields[ip_fm_fields[index].sf_field_api_name].value;
            }
        }
        //console.log("📘:tradingcountry", tradingcountry);

        var tradingaddress = "";
        if (tradingstreet) tradingaddress = tradingstreet;
        if (tradingtown) tradingaddress += ", " + tradingtown;
        if (tradingpostcode) tradingaddress += ", " + tradingpostcode;
        if (tradingcountry) tradingaddress += ", " + tradingcountry;
        //console.log("📘: tradingaddress", tradingaddress);
        if (tradingaddress == "") ip_this.rfaaddress = "n/a";
        else ip_this.rfaaddress = tradingaddress;
    }
    console.log("📘: ip_this.rfaaddress", ip_this.rfaaddress);
    console.log("📘: FINISHED : fm_builduifields :📘");
}
/****************************************************************************************************************************************************************/

function fm_build_cleardata(ip_module, ip_recordId, ip_client, ip_namespace, ip_fm_fields)
{
    console.log("📘: fm_build_cleardata", ip_module, ip_recordId, ip_client);

    var fields = {};
    fields.Id = ip_recordId;
    var todaysDate = new Date();
    fields[ip_namespace + "RFA_Update_Date__c"]       = convertDate(todaysDate);
    fields[ip_namespace + "CompanyNumber__c"]         = null;
    fields[ip_namespace + "Company_ID__c"]            = null;
    fields[ip_namespace + "RFA_Parent_Company_ID__c"] = null;

    var index = null;
    ip_namespace = "";

    if (ip_module == "Account") {

        var module = "accounts";
        // Default fields
        /*
        fields.Phone              = null;        
        fields.ShippingStreet     = null; 
        fields.ShippingCity       = null; 
        fields.ShippingPostalCode = null; 
        fields.ShippingCountry    = null;
        fields.BillingStreet      = null; 
        fields.BillingCity        = null; 
        fields.BillingPostalCode  = null; 
        fields.BillingCountry     = null;
        */
        if (ip_client.includes("charlesdean")) {
            fields.Company_Registration_Number__c = "";            
            fields.Date_Established__c = "";            
            fields.SIC_Codes__c = "";            
            fields.SicDesc = "";
            fields.Director_Full_Name__c = ""; 
            fields.Director_address__c = ""; 
            fields.Director_DOB__c = ""; 
            fields.Director_2_Full_Name__c = ""; 
            fields.Director_2_Address__c = "";
            fields.Director_2_DOB__c = ""; 
            fields.Director_3_Full_Name__c = ""; 
            fields.Director_3_Address__c = ""; 
            fields.Director_3_DOB__c = ""; 
            fields.Director_4_Full_Name__c = ""; 
            fields.Director_4_Address__c = ""; 
            fields.Director_4_DOB__c = "";
        }

        index = fm_get_rfa_field("RFA SIC Description 2", module, ip_fm_fields);
        if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = null;
        index = fm_get_rfa_field("RFA Email", module, ip_fm_fields);
        if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    }
    else {
        var module = "leads";
        if (ip_client.includes("charlesdean")) fields.Switchboard_Phone__c = null;

        // Default fields
        /*
        fields.Email             = null;
        fields.Phone             = null;
        fields.PostalCode        = null;
        fields.Street            = null;
        fields.City              = null;
        fields.State             = null;
        fields.Country           = null;
        */
    }

    /*
    index = fm_get_rfa_field("RFA Telephone number", module, ip_fm_fields);
    if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Trading Street", module, ip_fm_fields);
    if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Trading Town", module, ip_fm_fields);
    if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Trading Postcode", module, ip_fm_fields);
    if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Trading Country", module, ip_fm_fields);
    if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Registered Street", module, ip_fm_fields);
    if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Registered Town", module, ip_fm_fields);
    if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Registered Postcode", module, ip_fm_fields);
    if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Registered Country", module, ip_fm_fields);
    if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = null;
    */

    index = fm_get_rfa_field("RFA Website", module, ip_fm_fields);
    if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA SIC Description", module, ip_fm_fields);
    if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Employees", module, ip_fm_fields);
    if (index !== null) fields[ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA SIC Codes", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Directors", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Rating Code", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Creditor Days", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Incorporation Date", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Cash In Bank", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Net Worth", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Turnover", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Credit Limit", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Latest Action", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Latest Action Date", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA VAT Number", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA SIC Group Description", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Latest Accounts Filed Date", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Number of CCJs", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA CCJs Amount", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Parent Company Number", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Parent Company Name", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Trading Address", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;

    // Common Fields
    index = fm_get_rfa_field("RFA Net From Operating Activities", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Net From Return Investment Servicing", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Increase In Cash", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Net Before Financing", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Net From Financing", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Working Capital Turnover", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Total Current Assets", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Total Fixed Assets", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Tangible Fixed Assets", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA PL Account Reserve", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Working Capital", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Trade Debtors", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Shareholders Funds", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Stocks", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Misc Current Assets", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Total Long Term Liabilities", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Creditors Amounts Falling", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Total Assets Less Liabilities", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Revaluation Reserve", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Share Capital Reserve", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Total Liabilities", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Total Assets", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Contingent Liabilities", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Intangible Assets", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Pre Tax Profit", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Directors Remuneration", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Retained Profit", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Audit Fee", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Interest Payable", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Non Trading Income", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Operating Profit", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Dividends", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Taxation", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Gross Profit", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Non Audit Fee", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Post Tax Profit", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Value Added", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Cost Of Sales", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Auditor", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Growth Score", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Company Type", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Number of Active Directors", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA CCJs Satisfied", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA TPS Registered", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA EBITDA", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;
    index = fm_get_rfa_field("RFA Estimated Turnover", module, ip_fm_fields);
    if (index !== null) fields[ip_namespace + ip_fm_fields[index].sf_field_api_name] = null;

    return fields;
}
/*****************************************************************************************************************************************************************/

function fm_get_rfa_field(ip_field, ip_module, ip_fm_fields)
{
    for (var i=0; i < ip_fm_fields.length; i++) {
        if (ip_fm_fields[i].rfa_field_name == ip_field) {
            if (ip_fm_fields[i].status == "ON") return i;
            else return null;
        }
    }
    return null;
}
/*****************************************************************************************************************************************************************/

export { PopulateMainBoxes, fm_dbupdatefield, fm_count_on_fields, fm_buildupdatefields, fm_builduifields, fm_build_cleardata };