.first-boxes {
    margin-top:     10px;
    display: inline-block;
}
.second-boxes {
    margin-top : 10px;
    /* 03/10/2023 */
    /*margin-right : 12px;*/
    margin-right : 4px;
    /* 03/10/2023 */
    float : right;
}
.box-rating-title {
    color: #ffffff;
    font-size:  11px;
    font-weight: 600;
    margin-top: 8px;
    margin-left: 10px;
    margin-right: 10px;
    margin-bottom: 0px;
    padding: 0px;
    padding-bottom: 0px;
}
.box-rating {
    color: #ffffff;
    
    /* 03/10/2023 */
    /*font-size:  22px;*/
    font-size:  16px;
    /* 03/10/2023 */

    font-weight: 800;
    margin-top: 0px;
    
    /* 03/10/2023 */
    /*margin-left: 10px;*/
    /*margin-right: 10px;*/
    margin-left: 5px;
    margin-right: 5px;
    /* 03/10/2023 */
    margin-bottom: 10px;
    padding: 0px;
}
.box {
    margin: 0px;
    padding: 0px;
    display: inline-block;
    text-align: center;
    /*background-color: #989898;*/
    background-image: linear-gradient(to bottom right, #7a7a7a, #cfcece);
    border-top-left-radius: 5px;
    border-bottom-right-radius: 5px;
    -webkit-transform: skewX(-5deg);
    -moz-transform: skewX(-5deg);
    -ms-transform: skewX(-5deg);
    transform: skewX(-5deg);
    margin-top: -10px;
    margin-left: 4px;
    /* 03/10/2023 */
    /*margin-bottom:  14px;*/
    margin-bottom:  20px;
    /* 03/10/2023 */

    /*box-shadow: rgba(50, 50, 93, 0.25) 0px 6px 12px -2px, rgba(0, 0, 0, 0.3) 0px 3px 7px -3px;*/
    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
}
.companydiv {
    box-shadow: rgba(50, 50, 93, 0.25) 0px 6px 12px -2px, rgba(0, 0, 0, 0.3) 0px 3px 7px -3px;
}
.flex-container3 {
    justify-content: center;
    background: #f1f4f7;
    padding: 10px;
}
.flex-container4 {
    justify-content: center;
    background: #f1f4f7;
    padding: 10px;
    margin-bottom: 20px;
}
.column1 {
    float: left;
    width: 10%;
    padding: 0px;
    line-height: 1.0em;
}
.column2 {
    float: left;
    width: 40%;
    padding: 0px;
    line-height: 1.0em;
     word-wrap: break-word; /* 24/11/2019 */
}
.column3 {
    float: left;
    width: 10%;
    padding: 0px;
    line-height: 1.0em;
}
.column4 {
    float: left;
    width: 40%;
    padding: 0px;
    line-height: 1.0em;
    word-wrap: break-word; /* 24/11/2019 */
}
.row:after {
  content: "";
  display: table;
  clear: both;
}
.row {
    margin-bottom: 10px;
}
.format_data {
    font-weight: 700;
}
.box2 {
    margin: 0px;
    padding: 0px;
    display: inline-block;
    text-align: center;
    background-color: red;
    /*background-image: linear-gradient(to bottom right, #7a7a7a, #cfcece);*/
    border-top-left-radius: 5px;
    border-bottom-right-radius: 5px;
    -webkit-transform: skewX(-5deg);
    -moz-transform: skewX(-5deg);
    -ms-transform: skewX(-5deg);
    transform: skewX(-5deg);
    margin-top: -10px;
    margin-left: 4px;
    margin-bottom:  14px;
    box-shadow: rgba(50, 50, 93, 0.25) 0px 6px 12px -2px, rgba(0, 0, 0, 0.3) 0px 3px 7px -3px;    
}

.BGGreen  { background-image: linear-gradient(to bottom right, #55913c, #97ce39); }
.BGRed    { background-image: linear-gradient(to bottom right, #9c120f, #ee5d59); }
.BGGold   { background-image: linear-gradient(to bottom right, #e0be04, #ffd701); }
.BGSilver { background-image: linear-gradient(to bottom right, #cfcece, #7a7a7a); }
.BGBronze { background-image: linear-gradient(to bottom right, #cd7f32, #f0bc89); }
.BGAmber  { background-image: linear-gradient(to bottom right, #ff7f01, #fdcc9b); }
.BGGray   { background-image: linear-gradient(to bottom right, #7a7a7a, #cfcece); }
.BGRFA    { background-image: linear-gradient(to bottom right, #1B1526, #1B1526); }

.LookupDiv {
    display:none;
}
.displaynone {
    display:none;
}
.display {
    display:block;
}
.display2 {
    display:block;
}
a.linkedin:link {
    color:#ffffff;
    /* 03/10/2023 */
    /*font-size: 18px;*/
    font-size: 14px;
    /* 03/10/2023 */
}
a.linkedin:visited {
    color:#ffffff;
}
a.linkedin:hover {color:#67b24b;}
a.linkedin-director:link 
{
    color:#ffffff;
    font-size: 18px;
}
a.linkedin-director:visited {color:#ffffff;}
a.linkedin-director:hover {color:#67b24b;}
a.linkedin-director2:link {
    color: #67b24b;
    font-size: 14px;
}
a.linkedin-director2:visited {color:#67b24b;}
a.linkedin-director2:hover {color:#67b24b;}

.linkedin-image {
    display: inline-block;
    padding-top:    7px;
    padding-bottom: 7px;
    padding-left:   5px;
    padding-right:  5px;
}
.box-linkedin {
    background-image: linear-gradient(to bottom right, hsl(201, 89%, 60%), hsl(201, 89%, 28%));
    vertical-align: top;
    font-size: 10px;
}
p {
    line-height: normal;
}
.slds-modal__content{
    height:initial !important;
    min-height:500px !important;
}
.float-left {
    float: left!important;
}
/*
.multi-select-combobox__dropdown {
    max-height: 500px;
    overflow-y: auto;
}
.multi-select-combobox__input {
    background-color: #ffffff;
    border: 1px solid #dddbda;
    border-radius: 0.25rem;
    width: 100%;
    transition: border 0.1s linear, background-color 0.1s linear;
    display: inline-block;
    padding: 0 1rem 0 0.75rem;
    line-height: 1.875rem;
    min-height: calc(1.875rem + (1px * 2));
}
.multi-select-combobox__input:disabled {
    background-color: #ecebea;
    border: 1px solid #c9c7c5;
}
.multi-select-combobox__icon {
    margin-right: -3px;
}
.multi-select-combobox__listbox {
    width: 100%;
}
*/
.fm_column1 {
    float: left;
    width: 40%;
    padding: 0px;
    line-height: 1.0em;
}
.fm_modal {
    background-color: #d8d4d4 !important;
}
/* New colors */
.newrfa {
    background-color: #1B1526;
}
.newrfa_text {
    color: #ffffff
}
.my-brand {
    --slds-c-button-brand-color-background: #1B1526 !important;
    --slds-c-button-color-border: #1B1526 !important;
}
.rfaspace {
    margin-bottom : 15px;
}