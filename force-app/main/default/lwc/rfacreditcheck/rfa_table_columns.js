const fm_tablecolumns  = [{label:'Field Name',fieldName:'rfa_field_name'},{label:'Field Description',fieldName:'rfa_field_description'},{label:'Field Resource',fieldName:'rfa_field_resource'},{label:'Field Type',fieldName: 'rfa_field_type'},{label: 'Salesforce Field Name',fieldName:'sf_field_name'},{label:'Salesforce Field API Name',fieldName: 'sf_field_api_name'},{label: 'Status',fieldName: 'status'}];
const SearchTableColumns         = [{label: 'Company Number',fieldName:'companynumber'},{label:'Company Name',fieldName:'companyname'},{label:'Address',fieldName:'address'},{label:'Type',fieldName:'type'},{label:'Rating',fieldName:'rating'}];
const B2BSearchTableColumns      = [{label: 'Company Number',fieldName:'companynumber'},{label:'Company Name',fieldName:'companyname'},{label:'Type',fieldName:'type'},{label:'SIC Description',fieldName: 'sicdescription'},         { label: 'Address',fieldName:'address'},{label:'Turnover',fieldName: 'turnover'},{label: 'Rating',fieldName: 'rating'}];
const contactSearchTableColumns  = [{label: 'First Name',   fieldName: 'firstName'   },    { label: 'Last Name',    fieldName: 'lastName'},      { label: 'Email',        fieldName: 'email'       },    { label: 'Status',       fieldName: 'status'},{label:'Position',fieldName: 'position'    },    { label: 'Type',         fieldName: 'type'        },    { label: 'Company Name', fieldName: 'CompanyName' },    { label: 'Source Page',  fieldName: 'sourcePage'}];
const contactSearchTableColumns2 = [{label: 'First Name',   fieldName: 'firstName'   },    { label: 'Last Name',fieldName:'lastName'},      { label: 'Email',        fieldName: 'email'       },    { label: 'Status',       fieldName: 'status'},{label:'Position',fieldName:'position'},{label:'Type',fieldName: 'type'        },    { label: 'Company Name', fieldName: 'CompanyName' },    { label: 'Source Page',  fieldName: 'sourcePage'  }];

export { fm_tablecolumns, SearchTableColumns, B2BSearchTableColumns, contactSearchTableColumns, contactSearchTableColumns2 };