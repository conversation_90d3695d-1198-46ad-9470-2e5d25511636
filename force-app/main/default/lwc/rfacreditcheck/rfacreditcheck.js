// 14/02/2023 JD Beta 1.15 Move comments to README.md
var gv_version               = "2.02";
import { LightningElement, api, wire, track } from 'lwc';
import { getObjectInfos }   from 'lightning/uiObjectInfoApi';
import { ShowToastEvent }   from 'lightning/platformShowToastEvent';
import { getRecord }        from 'lightning/uiRecordApi';
import { updateRecord }     from 'lightning/uiRecordApi'; 
import { createRecord }     from 'lightning/uiRecordApi';
import Id                   from '@salesforce/user/Id';
import UserNameFld          from '@salesforce/schema/User.Name';
import userEmailFld         from '@salesforce/schema/User.Email';
import userIsActiveFld      from '@salesforce/schema/User.IsActive';
import userAliasFld         from '@salesforce/schema/User.Alias';
import leadMainObject       from '@salesforce/schema/Lead';
import LEADRFACompanyNumber from '@salesforce/schema/Lead.CompanyNumber__c';
import LEADRFAUPDATEDATE    from '@salesforce/schema/Lead.RFA_Update_Date__c';
import conMainObject        from '@salesforce/schema/Contact';
import conFirstName         from '@salesforce/schema/Contact.FirstName';
import conLastName          from '@salesforce/schema/Contact.LastName';
import conEmail             from '@salesforce/schema/Contact.Email';
import conAccountId         from '@salesforce/schema/Contact.AccountId';
import conTitle             from '@salesforce/schema/Contact.Title';
import conSocialMedia       from '@salesforce/schema/Contact.RFASocial_Media_Link__c';
import RFAScheduler_conMainObject        from '@salesforce/schema/RFAScheduler__c';
import RFAScheduler_Field_SearchName     from '@salesforce/schema/RFAScheduler__c.Search__c';
import RFAScheduler_Field_Status         from '@salesforce/schema/RFAScheduler__c.SchedulerStatus__c';
import RFAScheduler_Field_NewLeads       from '@salesforce/schema/RFAScheduler__c.New_Leads__c';
import RFAScheduler_Field_QuerySkip      from '@salesforce/schema/RFAScheduler__c.Query_Skip__c';
import RFAScheduler_Field_Query          from '@salesforce/schema/RFAScheduler__c.Query__c';
import RFAScheduler_Field_TotalRecords   from '@salesforce/schema/RFAScheduler__c.Total_Records__c';
import RFAScheduler_Field_DuplicateLeads from '@salesforce/schema/RFAScheduler__c.Duplicate_Leads__c';
import RFAScheduler_Field_Key            from '@salesforce/schema/RFAScheduler__c.Key__c';
import RFAScheduler_With_Contacts        from '@salesforce/schema/RFAScheduler__c.With_Contacts__c';
import RFAScheduler_Job_Filter           from '@salesforce/schema/RFAScheduler__c.Job_Filter__c';
import Apex_B2B_Scheduler                from "@salesforce/apex/B2B_Scheduler.B2B_Scheduler";
import Apex_B2B_SetupSchedulerJobs       from "@salesforce/apex/B2B_SetupScheduleJobs.B2B_SchedulerJobs";
import RFA_batch_monthly_stop   from "@salesforce/apex/RFA_batch_monthly_stop.RFA_batch_monthly_stop";
import RFA_batch_monthly_start  from "@salesforce/apex/RFA_batch_monthly_start.RFA_batch_monthly_start";
import RFA_batch_monthly_now    from "@salesforce/apex/RFA_batch_monthly_now.RFA_batch_monthly_now";
import RFA_batch_monthly_exists from "@salesforce/apex/RFA_batch_monthly_exists.RFA_batch_monthly_exists";
import RFA_gazette_start        from "@salesforce/apex/RFA_gazette_start.RFA_gazette_start";
import RFA_gazette_stop         from "@salesforce/apex/RFA_gazette_stop.RFA_gazette_stop";
import RFA_gazette_exists       from "@salesforce/apex/RFA_gazette_exists.RFA_gazette_exists";
import { PopulateMainBoxes, fm_dbupdatefield, fm_count_on_fields, fm_buildupdatefields, fm_builduifields, fm_build_cleardata } from './rfacommon';
import { convertDate, toTitleCase, formatNumber, getratingcode, CheckAppPermissions, PopulateDomainField, date_diff_indays, getaddresses, comparedates, BuildSICCodes2 } from './rfafunctions';
import { GetCoordinates, RFAGetCompanyData, RFAGetCompanyDataDirectors, GetUserDetailsFromFireBase, fm_getfields, GetSICCodes, firebase_update_accountrefresh, gf_updatecredits, gf_updateuserschedulerjobs } from './rfagooglefunctions';
import { fm_tablecolumns, SearchTableColumns, B2BSearchTableColumns, contactSearchTableColumns, contactSearchTableColumns2 } from './rfa_table_columns';
import LightningConfirm from "lightning/confirm";
var gv_selectedcompanynumber = "";
var gv_toomanyrows           = "";
var gv_userdetails           = {};
var gv_ContactSelectedRows   = new Array();
var gv_ContactData           = new Array();
var Dummy_Arr                = new Array();
var gv_B2B_schedulerecord    = null;
const MAXRECORDS             = 10000; // Maximum GQL records
const GRAPHQL_TAKE           = 200; 
var gv_siccodes              = null;
var gv_generateleads         = "Y"; 
var gv_namespace             = ""; 
if (window.location.origin == "https://toradigital-dev-ed.lightning.force.com") gv_namespace = "redflagalert__";
// Field Mapping
var gv_fm_field         = null;
var gv_fm_fields        = null; // Used for updating fm fields
var gv_record_fm_fields = null; // Current record fm fields
var gv_sf_user_details  = null;
var gv_default          = "NO";
const disclaimermessage = "Please make sure your field mappings are correct, as all your account records, will be updated using the field mappings. Telephone and Addresses will NOT be updated.";

/****************************************************************************************************************************************************************/

export default class CompanyCreditCheck extends LightningElement {
    @track fields;
    async connectedCallback() {
        console.log("📘📘📘: START : initialize connectedCallback", this.objectApiName, window.location.origin, gv_namespace, ":📘📘📘");
        if (this.objectApiName == "Account") {
            gv_fm_fields = await fm_getfields(window.location.origin, "account", gv_namespace);
            // If no mapping fields, get default
            if (gv_fm_fields == "FAILED") {
                console.log("📕: FAILED : Get from DEFAULT"); 
                var defaultname = "DEFAULT";
                if (gv_namespace == "redflagalert__") defaultname = "DEFAULT_REDFLAG";
                gv_fm_fields = await fm_getfields(defaultname, "account", gv_namespace);
                gv_default = "YES";
            }
            this.fields = ['Account.Id'];
        }
        else {
            gv_fm_fields = await fm_getfields(window.location.origin, "lead", gv_namespace);
            // If no mapping fields, get default
            if (gv_fm_fields == "FAILED") {
                console.log("📕: FAILED : Get from DEFAULT"); 
                gv_fm_fields = await fm_getfields("DEFAULT", "lead", gv_namespace);
                gv_default = "YES";
            }
            this.fields = ['Lead.Id'];
        }
        console.log("📘: gv_fm_fields : records", gv_fm_fields.length);
        gv_record_fm_fields = gv_fm_fields;
        this.fields.push(this.objectApiName + '.' + gv_namespace + 'CompanyNumber__c');
        this.fields.push(this.objectApiName + '.' + gv_namespace + 'Company_ID__c');
        this.fields.push(this.objectApiName + '.' + gv_namespace + 'RFA_Parent_Company_ID__c');
        this.fields.push(this.objectApiName + '.' + gv_namespace + 'RFA_Update_Date__c');
        for (var i=0; i < gv_fm_fields.length; i++) {
            if (gv_fm_fields[i].sf_field_api_name == "none") continue;
            if (gv_fm_fields[i].status == "OFF") continue; // 28/09/2023
            this.fields.push(this.objectApiName + '.' + gv_fm_fields[i].sf_field_api_name);
        }
        //console.log("📘: this.fields",this.fields);

        // Monthly Refresh
        this.mr_status = "n/a";
        this.mr_class = "box BGGray";
        RFA_batch_monthly_exists({  }).then(result => { 
            console.log("📘: RFA_batch_monthly_exists - result: " + result); 
            if (result == "FOUND") {
                this.mr_status = "ON";
                this.mr_mainstatus = "ON (" + date_diff_indays() + " days)";
                this.mr_class = "box BGRFA";
            }
            else {
                this.mr_status = "OFF";  
                this.mr_mainstatus = "OFF";
            }
        })
        .catch(error => { console.log("📕: ERROR : RFA_batch_monthly_exists - error " + error); });
        // Monthly Refresh
        this.mr_gazette_status = "OFF";
        this.mr_gazette_class = "box BGGray";
        RFA_gazette_exists({  }).then(result => { 
            console.log("📘: RFA_gazette_exists - result: " + result); 
            if (result == "FOUND") {
                this.mr_gazette_status = "ON";
                this.mr_gazette_class = "box BGRFA";
                this.mr_gazette_mainstatus = "ON";
            }
            else this.mr_gazette_mainstatus = "OFF";
        })
        .catch(error => { console.log("📕: ERROR : RFA_gazette_exists - error " + error); });
        console.log("📘: FINISHED : initialize connectedCallback :📘");
    }
    jobfiltervalue = 'ALL';
    get jobfilteroptions() { return [ {label:'ALL',value:'ALL'}, {label:'Senior Management',value:'SM'}, {label:'Middle Management',value:'MM'}, {label:'Marketing and Sales',value:'MS'}, {label:'Human Resources',value:'HR'}, {label:'Information Technology',value:'IT'}, {label:'Finance',value:'F'}, {label:'Credit',value:'CREDIT' },]; }
    handlejobfilterchange(event) { this.jobfiltervalue = event.detail.value; }
    searchisccodesvalue = "";
    handleB2B_searchsiccodes(event) { BuildSICCodes2(event.detail.value, this, "Y", gv_siccodes) }  // 27/08/2023
    siccodes1class = "displaynone"; siccodes2class = "displaynone"; siccodes3class = "displaynone"; siccodes4class = "displaynone"; siccodes5class = "displaynone"; siccodes6class = "displaynone"; siccodes7class = "displaynone"; siccodes8class = "displaynone"; siccodes9class = "displaynone"; siccodes10class = "displaynone"; siccodes11class = "displaynone"; siccodes12class = "displaynone";    
    growthvalue = 'None';
    get growthoptions() { return [ { label:'None',value:'None'},{label:'Very Unlikely',value:'veryunlikely'},{label:'Unlikely',value: 'unlikley'},{label:'Likely',value: 'likely'},{label:'Very Likely',value:'verylikely'},];}
    handlegrowthChange(event) { this.growthvalue = event.detail.value; }
    companytypevalue = 'ALL';
    get companytypeoptions() { return [ {label:'ALL',value:'ALL'},{label:'Limited',value:'limited'},{label:'PLC',value:'plc'},{label:'LLP',value:'llp'},{label:'Non-Limited / Other',value:'nonlimited' },];}
    handlecompanytypechange(event) { this.companytypevalue = event.detail.value; }
    // Field Mapping - Option List - Choose Module
    fmtypevalue = "account";
    get fmtypeoptions() { return [ {label:'Accounts',value:'account'},{label:'Leads',value:'lead'}];}
    async handlefmtypechange(event) { 
        this.fmtypevalue = event.detail.value; 
        gv_fm_fields = await fm_getfields(gv_userdetails.baseurl, this.fmtypevalue, gv_namespace); 
        if (this.fmtypevalue == "account") {
            this.sf_field_style      = "display";
            this.sf_lead_field_style = "displaynone";
        }
        else {
            this.sf_field_style      = "displaynone";
            this.sf_lead_field_style = "display";
        }
        gv_fm_field = null;
        this.fm_data_arr                    = gv_fm_fields; 
        this.fieldmapping_modaltitle_module = this.fmtypevalue;
        this.fieldmapping_modaltitle_mapped = fm_count_on_fields(gv_fm_fields); 
        this.fieldmapping_modaltitle_fields = gv_fm_fields.length;
        this.ShowB2BModal = false; this.showModal = false; this.ShowContactModal = false; this.fieldmapping_modal = true;
    }
    // Field Mapping - Option List - Choose Module
    mortgagestatus = 'None'; mortgagestatusvalue = null;
    get mortgagestatusoptions() { return [ {label:'None',value:'None'},{label:'No',value:'no'},{label:'Fully',value:'fully'},];}
    handlemortgagestatuschange(event) { if (event.detail.value == "no" || event.detail.value == "fully") this.mortgagestatusvalue = event.detail.value; else this.mortgagestatusvalue = null; }
    sicsectionvalue = 'ALL';
    get sicsectionoptions() { return [ { label: 'ALL', value: 'ALL' }, { label: 'A - Agriculture, Forestry and Fishing', value: 'A' }, { label: 'B - Mining and Quarrying', value: 'B' }, { label: 'C - Manufacturing', value: 'C' }, { label: 'D - Electricity, gas, steam and air conditioning supply', value: 'D' }, { label: 'E - Water supply, sewerage, waste management and remediation activities', value: 'E' },            { label: 'F - Construction', value: 'F' }, { label: 'G - Wholesale and retail trade; repair of motor vehicles and motorcycles', value: 'G' },            { label: 'H - Transportation and storage', value: 'H' }, { label: 'I - Accommodation and food service activities', value: 'I' }, { label: 'J - Information and communication', value: 'J' },            { label: 'K - Financial and insurance activities', value: 'K' },            { label: 'L - Real estate activities', value: 'L' }, { label: 'M - Professional, scientific and technical activities', value: 'M' }, { label: 'N - Administrative and support service activities', value: 'N' },            { label: 'O - Public administration and defence; compulsory social security', value: 'O' },            { label: 'P - Education', value: 'P' }, { label: 'Q - Human health and social work activities', value: 'Q' }, { label: 'R - Arts, entertainment and recreation', value: 'R' },{ label: 'S - Other service activities', value: 'S' }, { label: 'T - Activities of households as employers; undifferentiated goods- and services-producing activities of households for own use', value: 'T' }, { label: 'U - Activities of extraterritorial organisations and bodies', value: 'U' } ]; }
    handlesicsectionChange(event) { this.sicsectionvalue = event.detail.value; BuildSICCodes2(event.detail.value, this, "N", gv_siccodes) }     // 28/08/2023
    get siccodesoptions1() { return [ { label: 'On', value: 'ON' }, { label: 'Off', value: 'OFF' }, ]; }
    siccodeschange1(event) { this.siccodesvalue1 = event.detail.value; }
    get siccodesoptions2() { return [ { label: 'On', value: 'ON' }, { label: 'Off', value: 'OFF' }, ]; }
    siccodeschange2(event) { this.siccodesvalue2 = event.detail.value; }
    get siccodesoptions3() { return [ { label: 'On', value: 'ON' }, { label: 'Off', value: 'OFF' }, ]; }
    siccodeschange3(event) { this.siccodesvalue3 = event.detail.value; }
    get siccodesoptions4() { return [ { label: 'On', value: 'ON' }, { label: 'Off', value: 'OFF' }, ]; }
    siccodeschange4(event) { this.siccodesvalue4 = event.detail.value; }
    get siccodesoptions5() { return [ { label: 'On', value: 'ON' }, { label: 'Off', value: 'OFF' }, ]; }
    siccodeschange5(event) { this.siccodesvalue5 = event.detail.value; }
    get siccodesoptions6() { return [ { label: 'On', value: 'ON' }, { label: 'Off', value: 'OFF' }, ]; }
    siccodeschange6(event) { this.siccodesvalue6 = event.detail.value; }
    get siccodesoptions7() { return [ { label: 'On', value: 'ON' }, { label: 'Off', value: 'OFF' }, ]; }
    siccodeschange7(event) { this.siccodesvalue7 = event.detail.value; }
    get siccodesoptions8() { return [ { label: 'On', value: 'ON' }, { label: 'Off', value: 'OFF' }, ]; }
    siccodeschange8(event) { this.siccodesvalue8 = event.detail.value; }
    get siccodesoptions9() { return [ { label: 'On', value: 'ON' }, { label: 'Off', value: 'OFF' }, ]; }
    siccodeschange9(event) { this.siccodesvalue9 = event.detail.value; }
    get siccodesoptions10() { return [ { label: 'On', value: 'ON' }, { label: 'Off', value: 'OFF' }, ]; }
    siccodeschange10(event) { this.siccodesvalue10 = event.detail.value; }
    get siccodesoptions11() { return [ { label: 'On', value: 'ON' }, { label: 'Off', value: 'OFF' }, ]; }
    siccodeschange11(event) { this.siccodesvalue11 = event.detail.value; }
    get siccodesoptions12() { return [ { label: 'On', value: 'ON' }, { label: 'Off', value: 'OFF' }, ]; }
    siccodeschange12(event) { this.siccodesvalue12 = event.detail.value; }
    locationtypevalue = 'both';
    get locationtypeoptions() { return [ { label: 'Both', value: 'both' }, { label: 'Trading', value: 'trading' }, { label: 'Registered', value: 'registered' } ];    }
    locationtypechange(event) { this.locationtypevalue = event.detail.value; }
    values = [ {label : 'All',value : 'ALL', selected : true}, {label : 'Gold', value : 'GOLD', selected : false}, {label : 'Silver', value : 'SILVER', selected : false}, {label : 'Bronze', value : 'BRONZE', selected : false},{label : 'One Red Flag', value : 'ONE_RED_FLAG'}, {label : 'Two Red Flags', value : 'TWO_RED_FLAGS'}, {label : 'Three Red Flags', value : 'THREE_RED_FLAGS'},        {label : 'Business Discontinued', value : 'BUSINESS_DISCONTINUED'},         {label : 'Dissolved', value : 'DISSOLVED'},         {label : 'Insolvent', value : 'INSOLVENT'}, {label : 'Not Trading', value : 'NOT_TRADING'},        {label : 'Strike Off',value : 'STRIKE_OFF'},{label : 'Provisional Silver', value : 'PROVISIONAL_SILVER'},        {label : 'Provisional Bronze', value : 'PROVISIONAL_BRONZE'}, {label : 'Newly Incorporated', value : 'NEWLY_INCORPORATED'},        {label : 'Provisional One Red Flag', value : 'PROVISIONAL_ONE_RED_FLAG'},        {label : 'Provisional Two Red Flags', value : 'PROVISIONAL_TWO_RED_FLAGS'},        {label : 'Pre Insolvent', value : 'PRE_INSOLVENT'} ]; 
    hasnamedcontactvalue = "OFF";
    get hasnamedcontactoptions() { return [ { label: 'On', value: 'ON' }, { label: 'Off', value: 'OFF' }, ]; }
    hasnamedcontactchange(event) { this.hasnamedcontactvalue = event.detail.value; }
    hasfiledaccountsvalue = "OFF";
    get hasfiledaccountsoptions() { return [ { label: 'On', value: 'ON' }, { label: 'Off', value: 'OFF' }, ]; }
    hasfiledaccountschange(event) { this.hasfiledaccountsvalue = event.detail.value; }
    hasphonecontactvalue = "OFF";
    get hasphonecontactoptions() { return [ { label: 'On', value: 'ON' }, { label: 'Off', value: 'OFF' }, ]; }
    hasphonecontactchange(event) { this.hasphonecontactvalue = event.detail.value; }
    excludenontradingvalue = "ON";
    get excludenontradingoptions() { return [ { label: 'On', value: 'ON' }, { label: 'Off', value: 'OFF' }, ]; }
    excludenontradingchange(event) { this.excludenontradingvalue = event.detail.value; }
    hasemailcontactvalue = "OFF";
    get hasemailcontactoptions() { return [ { label: 'On', value: 'ON' }, { label: 'Off', value: 'OFF' }, ]; }
    hasemailcontactchange(event) { this.hasemailcontactvalue = event.detail.value; }
    excludetpsvalue = "OFF";
    get excludetpsgoptions() { return [ { label: 'On', value: 'ON' }, { label: 'Off', value: 'OFF' }, ]; }
    excludetpschange(event) { this.excludetpsvalue = event.detail.value; }
    postcodevalue = "";
    handleB2B_searchaddress(event) { this.postcodevalue = event.detail.value; }
    milesvalue = "";
    handleB2B_miles(event) { this.milesvalue = event.detail.value; }
    @api recordId; account; name; Website; Phone; SicDesc; NumberOfEmployees; redflagalert__SIC_Codes__c; 
    redflagalert__Directors__c; redflagalert__CompanyNumber__c; redflagalert__RFA_Rating2__c; redflagalert__creditordays__c; 
    redflagalert__Incorporation_Date__c; redflagalert__CCJs_Amount__c; redflagalert__Number_of_CCJs__c; redflagalert__Credit_Limit__c;
    redflagalert__Cash_In_Bank__c; redflagalert__Net_Worth__c; redflagalert__Company_Email__c; redflagalert__Turnover__c; 
    redflagalert__Credit_Limit__c; redflagalert__Latest_Action__c; redflagalert__Latest_Action_Date__c; redflagalert__RFA_Vat_Number__c;
    redflagalert__SIC_Group_Description__c; redflagalert__Last_Filed_Accounts__c; redflagalert__RFA_Update_Date__c; redflagalert__Trading_Address__c; 
    redflagalert__Company_ID__c; Directors__c;CompanyNumber__c; RFA_Rating2__c; creditordays__c; Incorporation_Date__c; CCJs_Amount__c; 
    Number_of_CCJs__c; Credit_Limit__c; Cash_In_Bank__c; Net_Worth__c; Company_Email__c;Turnover__c; Credit_Limit__c; 
    Latest_Action__c; Latest_Action_Date__c; RFA_Vat_Number__c; SIC_Group_Description__c; Last_Filed_Accounts__c; RFA_Update_Date__c; Trading_Address__c; Company_ID__c
    updatedisabled  = true; searchdisabled  = true; refreshdisabled = true;      
    userId = Id;
    @api objectApiName;
    @track error;
    companydata        = [];
    SearchTableColumns = SearchTableColumns;
    contactdata        = [];
    contactSearchTableColumns = contactSearchTableColumns;
    contactdata2        = []; // Dummy for refresh of status!
    contactSearchTableColumns2 = contactSearchTableColumns2;
    companydata2        = [];
    B2BSearchTableColumns = B2BSearchTableColumns;
    currentUserName; currentUserEmailId; currentIsActive; currentUserAlias;
    error;
    // Get current user details
    @wire(getRecord, { recordId: Id, fields: [UserNameFld, userEmailFld, userIsActiveFld, userAliasFld ]}) // Get User Details from Firebase Database
    async userDetails({error, data}) {
        this.appstatus = "Loading, please wait...";    
        if (data) { gv_sf_user_details = { baseurl : window.location.origin,username : data.fields.Name.value, useremail : data.fields.Email.value, userapps  : "CC" } } 
        else if (error) this.error = error;
    }
    // Get Account or Lead Record
    @wire(getRecord, { recordId: '$recordId', fields: '$fields' })
    async wiredRecord({ error, data }) {
        if (error) {
            let message = 'Unknown error';
            if (Array.isArray(error.body)) message = error.body.map(e => e.message).join(', ');
            else if (typeof error.body.message === 'string') message = error.body.message;
            this.dispatchEvent( new ShowToastEvent({ title: 'Error loading account', message, variant: 'error', }),);
        } 
        else if (data) {
            console.log("📘: START : Get Record :📘");
            this.account = data;
            // Get user details from FIREBASE
            gv_userdetails = await GetUserDetailsFromFireBase(gv_sf_user_details);
            console.log("📘: FIREBASE gv_userdetails", gv_userdetails);
            if (!gv_userdetails.Status) {
                htmlthis.appstatus = "Error retrieving your account details, please contact Red Flag Alert on 0330 460 9877 <NAME_EMAIL>.";    
                htmlthis.mainbuttondisabled = true; htmlthis.updatedisabled = true; htmlthis.searchdisabled = true; htmlthis.refreshdisabled = true; htmlthis.finddisabled = true;
                return;
            } 
            // Has to get default mapping fields, now get correct mapping fields.
            console.log("📘: gv_default", gv_default);
            if (gv_default == "YES") {
                if (this.objectApiName == "Account") gv_fm_fields = await fm_getfields(window.location.origin, "account", gv_namespace);
                else gv_fm_fields = await fm_getfields(window.location.origin, "lead", gv_namespace);
                gv_record_fm_fields = gv_fm_fields;
            }
            GetUserDetails(gv_sf_user_details, this);
            PopulateMainBoxes(this, gv_namespace, gv_record_fm_fields);
            console.log("📘📘📘: FINISHED : Get Record :📘📘📘");
        }
        this.imageURL   = "https://toradigital.co.uk/hubspot/live/rfalogo2.webp";
        this.companydiv = "flex-container3 companydiv displaynone";
        this.appversion = gv_version;
        this.apptitle   = "Red Flag Alert - Credit Check & B2B Leads - " + gv_version;
    }
    // Field Mapping
    fm_new_sf_field_api_name = null; fm_new_sf_field_name = null;
    fm_statuschange(event) { this.fm_status = event.detail.value; }
    fm_tablecolumns = fm_tablecolumns;
 
    // Get Account/Lead fields for option list 'Choose Salesforce API Field'
    @wire(getObjectInfos, { objectApiNames: [ 'Account', 'Lead' ] })
    objectsInfo({error, data}) {
        if (data) { 
            this.fm_arr = []; 
            for (let key in data.results[0].result.fields) { 
                if (data.results[0].result.fields.hasOwnProperty(key)) this.fm_arr.push({ value: data.results[0].result.fields[key].apiName, label: data.results[0].result.fields[key].label, dataType: data.results[0].result.fields[key].dataType, custom: data.results[0].result.fields[key].custom }); 
            }         
            this.fm_arr.sort((a, b) => a.label.localeCompare(b.label));
            this.sf_field_style = "display";
            this.sf_lead_field_style = "displaynone";
            this.fm_lead_arr = [];
            for (let key in data.results[1].result.fields) { 
                if (data.results[1].result.fields.hasOwnProperty(key)) this.fm_lead_arr.push({ value: data.results[1].result.fields[key].apiName, label: data.results[1].result.fields[key].label, dataType: data.results[1].result.fields[key].dataType, custom: data.results[1].result.fields[key].custom }); 
            }         
            this.fm_lead_arr.sort((a, b) => a.label.localeCompare(b.label));
            console.log("📘: account api fields", this.fm_arr.length, "lead api fields", this.fm_lead_arr.length);
        }
        else if (error) {
            this.fm_arr = []; 
            this.fm_lead_arr = [];
            console.log("📕: ERROR : Get account/lead fields"); 
        }
    }
    get sf_fieldoptions() { return this.fm_arr; }
    get sf_lead_fieldoptions() { return this.fm_lead_arr; }
    @api fm_close() { this.accountrefresh_modal = false; this.fieldmapping_modal = false; this.showModal = false; this.ShowContactModal = false; this.ShowB2BModal = false; }
    @api fmsave_close_modal() { this.showModal = false; this.ShowContactModal = false; this.ShowB2BModal = false; this.fieldmappingsave_modal = false; }
    // On click field mapping row
    @api fm_updatefield() { 
        if (!gv_fm_field) { 
            this.dispatchEvent( new ShowToastEvent ({ title : 'Error updating field', message : "Please choose one field at a time to update.", variant : 'error' }) ); 
            return; 
        }
        this.ShowB2BModal = false; 
        this.showModal = false; 
        this.ShowContactModal = false; 
        this.fieldmapping_modal = true; 
        this.fieldmappingsave_modal = true; 
        this.fm_title                  = "Update Custom RFA Field: " + gv_fm_field.rfa_field_name;
        this.fm_field_description      = gv_fm_field.rfa_field_description; 
        this.fm_resource               = gv_fm_field.rfa_field_resource; 
        this.fm_field_type             = gv_fm_field.rfa_field_type; 
        this.fm_current_field_name     = gv_fm_field.sf_field_name; 
        this.fm_current_field_api_name = gv_fm_field.sf_field_api_name; 
        this.fm_status                 = gv_fm_field.status;
        this.fm_new_sf_field_api_name  = gv_fm_field.sf_field_api_name; // 27/08/2023
    }
    handle_sf_fieldchange(event) { 
        this.fm_new_sf_field_api_name = event.target.value; 
        this.fm_new_sf_field_name = event.target.label; 
    }
    handle_sf_lead_fieldchange(event) { 
        this.fm_new_sf_field_api_name = event.target.value; 
        this.fm_new_sf_field_name = event.target.label; 
    }
    @api async fmsave_save_modal() { 
        if (this.fmtypevalue == "account") var status = await fm_dbupdatefield(this.fm_new_sf_field_api_name, this.fm_status, this.fm_data_arr, gv_fm_field, this.fm_arr, gv_namespace);
        else var status = await fm_dbupdatefield(this.fm_new_sf_field_api_name, this.fm_status, this.fm_data_arr, gv_fm_field, this.fm_lead_arr, gv_namespace);
        if (status == "INVALIDTYPE") { 
            this.dispatchEvent( new ShowToastEvent ({ title : 'Error updating field', message : "Invalid field type", variant : 'error' }) ); 
            return; 
        }
        if (status == "NONE") {
            this.dispatchEvent( new ShowToastEvent ({ title : 'Error updating field', message : "Please choose a Salesforce field", variant : 'error' }) ); 
            return; 
        }
        gv_fm_field = null;
        gv_fm_fields = await fm_getfields(gv_userdetails.baseurl, this.fmtypevalue, gv_namespace); 
        this.fm_data_arr  = gv_fm_fields; 
        console.log("📘: this.objectApiName", this.objectApiName, this.fmtypevalue);
        if ((this.objectApiName == "Lead" && this.fmtypevalue == "lead") || (this.objectApiName == "Account" && this.fmtypevalue == "account")) {
            console.log("📘: Current module fm fields has changed, so update gv_record_fm_fields");
            gv_record_fm_fields = gv_fm_fields;
        }
        this.fieldmapping_modaltitle_module = this.fmtypevalue;
        this.fieldmapping_modaltitle_mapped = fm_count_on_fields(gv_fm_fields);
        this.fieldmapping_modaltitle_fields = gv_fm_fields.length;
        this.fieldmappingsave_modal = false; this.showModal = false; this.ShowContactModal = false; this.ShowB2BModal = false; 
        this.dispatchEvent( new ShowToastEvent({ title : 'Success', message : 'Field updated successfully.', variant : 'success' }) );
        this.template.querySelector('lightning-datatable').maxRowSelection=0;
        this.template.querySelector('lightning-datatable').maxRowSelection=1;
    }
    // Field Mapping - On click row
    fm_handleRowSelection(event) {
        const selectedRows = event.detail.selectedRows;        
        if (selectedRows.length > 1) gv_fm_field = null;
        else for (let i = 0; i < selectedRows.length; i++) { gv_fm_field = selectedRows[i]; }    
        console.log("Field Mapping - On click row - gv_fm_field", gv_fm_field);
    }
    // Field Mapping
    // Monthly Refresh
    async mr_start_button() { 
        if (this.mr_status == "ON") {
            this.dispatchEvent( new ShowToastEvent( { title : 'Error', message : 'The scheduler is already ON', variant : 'error' }) );
            return;
        }
        const result = await LightningConfirm.open({
            message: disclaimermessage,
            variant: "yellow", // headerless
            label: "Confirmation for refreshing ALL your account records with Red Flag Alert Data."
        });
        if (result) this.handleSuccessAlertClick_startbutton();
        else this.handleErrorAlertClick();
    }
    // Scheduler On Confirmation button
    handleSuccessAlertClick_startbutton() {
        RFA_batch_monthly_start({  }).then(result => { 
            console.log("📘: result", result); 
            this.mr_status = "ON";
            this.mr_class = "box BGRFA";
            this.mr_mainstatus = "ON (" + date_diff_indays() + " days)";
            this.dispatchEvent( new ShowToastEvent({ title : 'Success', message : 'Scheduler started successfully. The scheduler will run on the last day of the month, every month.', variant : 'success' }) );
            this.fm_close();
        })
        .catch(error => { 
            console.log("📕: ERROR : RFA_batch_monthly_start", error); 
            this.dispatchEvent( new ShowToastEvent( { title : 'Error', message : 'ERROR: Starting scheduler. Please Contact System Admin', variant : 'error' }) );
        });
    }
    @api mr_stop_button() { 
        if (this.mr_status == "OFF") {
            this.dispatchEvent( new ShowToastEvent( { title : 'Error', message : 'The scheduler is already OFF', variant : 'error' }) );
            return;
        }
        RFA_batch_monthly_stop({  }).then(result => { 
            console.log("📘: result", result); 
            this.mr_status = "OFF";
            this.mr_class = "box BGGray";
            this.mr_mainstatus = "OFF";
            this.dispatchEvent( new ShowToastEvent({ title : 'Success', message : 'Scheduler stopped successfully.', variant : 'success' }) );
            this.fm_close();
        })
        .catch(error => { 
            console.log("📕: ERROR : RFA_batch_monthly_stop", error); 
            this.dispatchEvent( new ShowToastEvent( { title : 'Error', message : 'ERROR: Stopping scheduler. Please Contact System Admin', variant : 'error' }) );
        });
    }
    async mr_now_button() { 
        // Check refresh accounts manually has been run today.
        console.log("📘: gv_userdetails.accountrefresh", gv_userdetails.accountrefresh); 
        if (gv_userdetails.accountrefresh) {
            var results = comparedates(gv_userdetails.accountrefresh);
            if (results == true) {
                this.dispatchEvent( new ShowToastEvent( { title : 'Error', message : 'You can only run the refresh accounts manually once per day.', variant : 'error' }) );
                return;
            }
        }
        const result = await LightningConfirm.open({
            message: disclaimermessage,
            variant: "yellow", // headerless
            label: "Confirmation for refreshing ALL your account records with Red Flag Alert Data."
        });
        if (result) this.handleSuccessAlertClick();
        else this.handleErrorAlertClick();
    }
    // Scheduler Run now confirmation button
    async handleSuccessAlertClick() {
        RFA_batch_monthly_now({  }).then(result => { 
            console.log("📘: result", result); 
            firebase_update_accountrefresh(gv_userdetails.key);
        })
        .catch(error => { 
            console.log("📕: ERROR : RFA_batch_monthly_now", error); 
            this.dispatchEvent( new ShowToastEvent( { title : 'Error', message : 'ERROR: Refresh accounts manually. Please Contact System Admin', variant : 'error' }) );
        });
        this.dispatchEvent( new ShowToastEvent({ title : 'Success', message : 'Refresh accounts manually started successfully.', variant : 'success' }) );
        gv_userdetails.accountrefresh = new Date();
        this.fm_close();
    }
    async handleErrorAlertClick() { this.fm_close(); }
    // Monthly Refresh
    // Gazette
    @api mr_gazette_start_button() { 
        if (this.mr_gazette_status == "ON") {
            this.dispatchEvent( new ShowToastEvent( { title : 'Error', message : 'Gazette notifications is already ON', variant : 'error' }) );
            return;
        }
        RFA_gazette_start({  }).then(result => { 
            console.log("📘: result", result); 
            this.mr_gazette_status = "ON";
            this.mr_gazette_class = "box BGRFA";
            this.mr_gazette_mainstatus = "ON";
            this.dispatchEvent( new ShowToastEvent({ title : 'Success', message : 'Gazette notifications started successfully.', variant : 'success' }) );
            this.fm_close();
        })
        .catch(error => { 
            console.log("📕: ERROR : RFA_gazette_start", error); 
            this.dispatchEvent( new ShowToastEvent( { title : 'Error', message : 'ERROR: Starting Gazette notifications. Please Contact System Admin', variant : 'error' }) );
        });
    }
    @api mr_gazette_stop_button() { 
        if (this.mr_gazette_status == "OFF") {
            this.dispatchEvent( new ShowToastEvent( { title : 'Error', message : 'Gazette notifications is already OFF', variant : 'error' }) );
            return;
        }
        RFA_gazette_stop({  }).then(result => { 
            console.log("📘: result", result); 
            this.mr_gazette_status = "OFF";
            this.mr_gazette_class = "box BGGray";
            this.mr_gazette_mainstatus = "OFF";
            this.dispatchEvent( new ShowToastEvent({ title : 'Success', message : 'Gazette notifications stopped successfully.', variant : 'success' }) );
            this.fm_close();
        })
        .catch(error => { 
            console.log("📕: ERROR : RFA_gazette_stop", error); 
            this.dispatchEvent( new ShowToastEvent( { title : 'Error', message : 'ERROR: Stopping Gazette notifications. Please Contact System Admin', variant : 'error' }) );
        });
    }
    // Gazette
    @api showModal = false;
    @api appstatus = "";
    @api searchstatus;
    @api searchstatus2;
    @api searchcriteriastatus;
    @api openModal() { this.showModal = true; }
    @api closeModal() { this.showModal = false; }
    @api clearrfadata() {
        var fields = fm_build_cleardata(this.objectApiName, this.recordId, window.location.origin, gv_namespace, gv_record_fm_fields);
        console.log("📘: Clear fields", fields);
        const recordInput = { fields };
        updateRecord(recordInput).then(() => {
            this.dispatchEvent( new ShowToastEvent({ title : 'Success', message : 'Red Flag Alert data has been removed successfully.', variant : 'success' }) );
            this.appstatus = "Red Flag Alert data have been removed successfully."; this.showModal = false;
        }).catch(error => { 
            this.dispatchEvent( new ShowToastEvent ({ title : 'Error updating record', message : error.body.message, variant : 'error' }) ); 
        });
    }
    @api contactcloseModal() { this.ShowContactModal = false; this.showModal = true; }
    @api ContactcloseModal() { this.ShowContactModal = false; this.showModal = true; }
    @api B2BcloseModal() { this.showModal = false; this.ShowContactModal = false; this.ShowB2BModal = false; }
    @api ResultsB2BModalClose() { this.showModal = false; this.ShowContactModal = false; this.ShowB2BModal = true; this.ResultsB2BModal = false; }
    @api handlerfareport() {
        if (this.account.fields.CompanyNumber__c) { var CompanyID = this.account.fields.Company_ID__c.value; }
        else var CompanyID = this.account.fields.redflagalert__Company_ID__c.value;
        if (CompanyID) window.open("https://app.redflagalert.net/app/check/report/" + CompanyID + "/GBR",'_blank');
        else this.appstatus = "No Red Flag Alert report available for this company."; 
    }
    @api B2B_searchforleads() { Status = B2B_Searchforrecords(this); }  // Seach for Leads Button
    @api B2B_GenerateLeads() { Status = B2B_CreateSchedule(this,"No"); }     // Generate Leads Button
    @api B2B_GenerateLeadscon() { Status = B2B_CreateSchedule(this,"Yes"); }     // Generate Leads Button
    B2B_CampaignName = "Campaign name";    B2B_CampaignName_Value = "Campaign name";
    handleB2B_CampaignName(event) { this.B2B_CampaignName_Value = event.target.value; }
    B2B_auditorvalue = "";
    handleB2B_auditor(event) { this.B2B_auditorvalue = event.target.value; }
    B2B_mortgageholder_Value = "";
    handleB2B_mortgageholder(event) { this.B2B_mortgageholder_Value = event.target.value; }
    B2B_mortgagedatefrom_Value = "";
    handleB2B_mortgagedatefrom(event) { this.B2B_mortgagedatefrom_Value = event.target.value; }
    B2B_mortgagedateto_Value = "";
    handleB2B_mortgagedateto(event) { this.B2B_mortgagedateto_Value = event.target.value; }
    B2B_incorporationdatefrom_Value = "";
    handleB2B_incorporationdatefrom(event) { this.B2B_incorporationdatefrom_Value = event.target.value; }
    B2B_incorporationdateto_Value = "";
    handleB2B_incorporationdateto(event) { this.B2B_incorporationdateto_Value = event.target.value; }
    B2B_lastupdatefrom_Value = "";
    handleB2B_lastupdatedfrom(event) { this.B2B_lastupdatefrom_Value = event.target.value; }
    B2B_lastupdateto_Value = "";
    handleB2B_lastupdatedto(event) { this.B2B_lastupdateto_Value = event.target.value; }
    B2B_turnoverfrom_Value = 0;
    handleB2B_turnoverfrom(event) { this.B2B_turnoverfrom_Value = event.target.value; }
    B2B_turnoverto_Value = 0;
    handleB2B_turnoverto(event) { this.B2B_turnoverto_Value = event.target.value; }
    B2B_estimateturnovertoggle_Value = "OFF";
    handleB2B_estimateturnoverestoggle(event) { if (this.B2B_estimateturnovertoggle_Value == "OFF") this.B2B_estimateturnovertoggle_Value = "ON";            else if (this.B2B_estimateturnovertoggle_Value == "ON") this.B2B_estimateturnovertoggle_Value = "OFF";    }
    B2B_employeesfrom_Value = 0;
    handleB2B_employeesfrom(event) { if (event.target.value == "") this.B2B_employeesfrom_Value = 0; else this.B2B_employeesfrom_Value = event.target.value;     }
    B2B_employeesto_Value = 0;
    handleB2B_employeesto(event) { if (event.target.value == "") this.B2B_employeesto_Value = 0; else this.B2B_employeesto_Value = event.target.value;     }
    B2B_estimateemployeestoggle_Value = "OFF";
    handleB2B_estimateemployeestoggle(event) { if (this.B2B_estimateemployeestoggle_Value == "OFF") this.B2B_estimateemployeestoggle_Value = "ON"; else if (this.B2B_estimateemployeestoggle_Value == "ON") this.B2B_estimateemployeestoggle_Value = "OFF"; }
    B2B_totalassetsfrom_Value = 0;
    handleB2B_totalassetsfrom(event) { if (event.target.value == "") this.B2B_totalassetsfrom_Value = 0; else this.B2B_totalassetsfrom_Value = event.target.value; }
    B2B_totalassetsto_Value = 0;
    handleB2B_totalassetsto(event) { if (event.target.value == "") this.B2B_totalassetsto_Value = 0; else this.B2B_totalassetsto_Value = event.target.value; }
    B2B_creditlimitfrom_Value = 0;
    handleB2B_creditlimitfrom(event) { if (event.target.value == "") this.B2B_creditlimitfrom_Value = 0; else this.B2B_creditlimitfrom_Value = event.target.value; }
    B2B_creditlimitto_Value = 0;
    handleB2B_creditlimitto(event) { if (event.target.value == "") this.B2B_creditlimitto_Value = 0; else this.B2B_creditlimitto_Value = event.target.value; }
    B2B_keywordfilter_Value = "";
    handleB2B_keywordfilter(event) { this.B2B_keywordfilter_Value = event.target.value; }
    @api FindModal() {
        PopulateDomainField(this);
        this.showModal = false;
        this.ShowContactModal = true;
        this.InputFieldsClass2 = "slds-align_absolute-center"; 
        this.contactsearchstatus = "";
        if (this.objectApiName == "Account") { this.creatleaddisabled     = true; this.creatcontactsdisabled = false; }
        else { this.creatleaddisabled = false; this.creatcontactsdisabled = true; }
    }
    @api ContactshandleClick() { // Search for contacts Button
        if (this.Domain_Value == "") {
            var Dummy_Arr = new Array();
            this.contactdata2 = Dummy_Arr; 
            this.contactsearchstatus = "Please enter a domain name."; return;
        }
        var Status = SearchForContacts(this);
    }
    @api updateModal() {     // Company Update Button
        if (gv_toomanyrows == "Y") { this.searchstatus = "Please select one row only."; return; }
        if (gv_toomanyrows == "N") {}
        else { this.searchstatus = "Please select a row before updating.";return; }
        var Status = UpdateAccountRecord(this,"No");
    }
    // Refresh Button
    @api refreshModal() {
        if (this.account.fields.CompanyNumber__c) var VCompanyNumber =  this.account.fields.CompanyNumber__c.value;
        else var VCompanyNumber = this.account.fields.redflagalert__CompanyNumber__c.value;
        if (VCompanyNumber) {
            this.searchstatus = "Refreshing data, please wait...";
            gv_selectedcompanynumber = VCompanyNumber;
            var Status = UpdateAccountRecord(this,"Yes");
        }
        else this.searchstatus = "Account record does not have a company number";
    }
    CompanyName_Value = "";
    handleCompanyNameChange(event) { this.CompanyName_Value = event.target.value; }
    CompanyNumber_Value = "";                                                                 
    handleCompanyNumberChange(event) { this.CompanyNumber_Value = event.target.value; }       
    handleRowSelection(event) {
        const selectedRows = event.detail.selectedRows
        for (let i = 0; i < selectedRows.length; i++) { gv_selectedcompanynumber = selectedRows[i].companynumber; }
        if (selectedRows.length > 1) { 
            gv_toomanyrows = "Y"; 
            this.searchstatus = "Please select only one row";
        }
        else { gv_toomanyrows = "N"; this.searchstatus = ""; }
    }
    handleRowSelection1(event) { this.searchstatus2 = "You cannot select individual companies."; }
    Domain_Value = "";
    handleDomainChange(event) { this.Domain_Value = event.target.value; }
    contacthandleRowSelection(event) {
        gv_ContactSelectedRows = new Array();
        const selectedRows = event.detail.selectedRows;
        for (let i = 0; i < selectedRows.length; i++) {
            var Row = {};
            Row.email = selectedRows[i].email;
            gv_ContactSelectedRows.push(Row);
        }
    }
    // Create Lead(s) - Email Lookup
    handleleadclick(event) {
        if (gv_ContactSelectedRows.length == 0) {
            var Dummy_Arr = new Array(); 
            this.contactdata2 = Dummy_Arr; this.contactsearchstatus = "Please select at least one row.";
            return;
        } else {
            var Dummy_Arr = new Array(); 
            this.contactdata2 = Dummy_Arr; this.contactsearchstatus = "Creating lead(s) records...";
            CreateLeads(this);
        }
    }
    clickedButtonLabel; 
    async handleClick(event) {
        this.clickedButtonLabel = event.target.label;
        if (this.clickedButtonLabel == "Accounts/Gazette") {
            this.ShowB2BModal = false; this.showModal = false; this.ShowContactModal = false; this.accountrefresh_modal = true;
        }
        // BUTTON : Field Mapping 
        if (this.clickedButtonLabel == "Field Mapping") {
            this.fm_data_arr                    = gv_fm_fields;       
            this.fieldmapping_modaltitle_module = this.fmtypevalue;
            this.fieldmapping_modaltitle_mapped = fm_count_on_fields(gv_fm_fields);
            this.fieldmapping_modaltitle_fields = gv_fm_fields.length;
            this.ShowB2BModal = false; this.showModal = false; this.ShowContactModal = false; this.fieldmapping_modal = true;
        }
        // BUTTON : Find Leads
        if (this.clickedButtonLabel == "Find Leads") {
            if (gv_generateleads == "N") {                                              
                this.appstatus = "You do not have permissions to generate leads.";      
                return;                                                                 
            }       
            if (!gv_siccodes) gv_siccodes = await GetSICCodes(); 
            this.ShowB2BModal = true; this.showModal = false; this.ShowContactModal = false;
        }
        // BUTTON : Credit Check This Account
        if (this.clickedButtonLabel == "Credit Check" || this.clickedButtonLabel == "View Finances" ) { 
            fm_builduifields(this, gv_record_fm_fields, gv_namespace); // Field Mapping
            if (this.clickedButtonLabel == "View Finances") {
                if (this.rfa_company_number) {
                    this.InputFieldsClass = "slds-align_absolute-center displaynone";
                    this.updatedisabled = true;
                }
                else {
                    this.appstatus = "No information to show, please perform a credit check first.";    
                    return;
                }
            } 
            else { 
                this.InputFieldsClass = "slds-align_absolute-center"; this.updatedisabled = false; 
            }
            this.showModal = true;
            // If the Company number is poplulated display RFA data
            if (this.rfa_company_number) {
                this.searchstatus = "";
                this.companydiv = "flex-container3 companydiv display"; // Show Data
                this.LookupDiv = "displaynone";                         // Hide Search Table
            }
            else {
                this.companydiv = "flex-container3 companydiv displaynone";         // Hide Data
                this.LookupDiv = "display";                                        // Show Search Table
            }
        }
        if (this.clickedButtonLabel == "Search") {
            this.companydiv   = "flex-container3 companydiv displaynone";
            this.LookupDiv    = "display";  
            this.searchstatus = "Searching for companies, please wait...";
            var Status = SearchCompany(this.CompanyName_Value, this, this.CompanyNumber_Value);
        }
        if (this.clickedButtonLabel == "Create Contact(s)") {
            if (gv_ContactSelectedRows.length == 0) {
                var Dummy_Arr = new Array(); // This forces refresh of ip_htmlthis.contactsearchstatus
                this.contactdata2 = Dummy_Arr; 
                this.contactsearchstatus = "Please select at least one row.";
                return;
            } else {
                var Dummy_Arr = new Array();                 // This forces refresh of ip_htmlthis.contactsearchstatus
                this.contactdata2 = Dummy_Arr; 
                this.contactsearchstatus = "Creating contact(s) records...";
                CreateContacts(this);
            }
        }
    }
}
/****************************************************************************************************************************************************************/
async function GetUserDetails(UserDetails, htmlthis) {
    if (!gv_userdetails.accountfields) gv_userdetails.accountfields = ""; 
    if (!gv_userdetails.leadfields) gv_userdetails.leadfields = "";       
    if (gv_userdetails.schedulercreated == "NO") {
        Apex_B2B_SetupSchedulerJobs({  }).then(result => { 
            console.log("📘: Apex_B2B_SetupSchedulerJobs - result: " + result); 
            gf_updateuserschedulerjobs(gv_userdetails.key); // 27/08/2023   
        })
        .catch(error => { 
            const event = new ShowToastEvent( { title : 'Error', message : 'Error creating RFA Scheduler Jobs. Please Contact System Admin', variant : 'error' });
            this.dispatchEvent(event); 
        });
    }
    htmlthis.credits     = formatNumber(gv_userdetails.creditcounter);
    htmlthis.creditlimit = formatNumber(gv_userdetails.creditlimit);
    htmlthis.substatus   = gv_userdetails.accountlock;
    if (gv_userdetails.accountlock == "Disabled") {
        htmlthis.appstatus = "Your account is disabled, please contact Red Flag Alert on 0330 460 9877 <NAME_EMAIL>.";    
        htmlthis.mainbuttondisabled = true; htmlthis.updatedisabled = true; htmlthis.searchdisabled = true; htmlthis.refreshdisabled = true; htmlthis.finddisabled = true;
        return;
    } 
    else htmlthis.mainbuttondisabled = false;
    var Permission = "CC";
    var Status = CheckAppPermissions(gv_userdetails.userapps, Permission);
    if (Status == false) {
        htmlthis.appstatus = "Your account does not have permissions for the credit check App, please contact Red Flag Alert on 0330 460 9877 <NAME_EMAIL>.";    
        htmlthis.mainbuttondisabled = true; htmlthis.updatedisabled = true; htmlthis.searchdisabled = true; htmlthis.refreshdisabled = true; htmlthis.finddisabled = true;
        return;
    }
    else console.log("📘: Has permissions for: " + Permission);
    if (gv_userdetails.creditcounter > 0) { // Enable or Disable Update button
        htmlthis.updatedisabled = false; htmlthis.searchdisabled = false; htmlthis.refreshdisabled = false; htmlthis.finddisabled = false;
    }
    else {
        htmlthis.updatedisabled = true; htmlthis.searchdisabled = true; htmlthis.refreshdisabled = true; htmlthis.finddisabled = true;
        htmlthis.appstatus = "You have no credits left, please contact Red Flag Alert on 0330 460 9877 <NAME_EMAIL>.";    
        htmlthis.searchstatus = "You have no credits left, please contact Red Flag Alert on 0330 460 9877 <NAME_EMAIL>.";    
    }
    if (gv_userdetails.generateleads && gv_userdetails.generateleads !== "ALL") {
        var havepermissions = "N";
        var emails_arr = gv_userdetails.generateleads.split(',');
        for (var i=0; i < emails_arr.length; i++) {
            if (UserDetails.useremail == emails_arr[i]) {
                havepermissions = "Y";
                break;
            }
        }
    }
    // 03/10/2023
    htmlthis.button_fieldmapping = true;
    htmlthis.button_gazette = true;
    if (gv_userdetails.app_permissions) {
        if (gv_userdetails.app_permissions.includes(UserDetails.useremail)) {
            console.log("📘: Has permission for field mapping and Accounts/Gazette");
            htmlthis.button_fieldmapping = false;
            htmlthis.button_gazette = false;
        }
    }
    // 03/10/2023
}
/****************************************************************************************************************************************************************/
async function UpdateCredits(htmlthis) {
    var status = await gf_updatecredits(gv_userdetails.key, gv_userdetails.creditcounter); // 27/08/2023
    gv_userdetails.creditcounter = gv_userdetails.creditcounter - 1;
    htmlthis.credits = formatNumber(gv_userdetails.creditcounter);
}
/****************************************************************************************************************************************************************/
async function SearchCompany(ip_searchfield, htmlthis, ip_companynumber) {
    if (htmlthis.CompanyNumber == "") ip_companynumber = "";         
    var Status = await SearchForCompany(ip_searchfield,htmlthis, ip_companynumber);
    if (Status == "SUCCESS") htmlthis.searchstatus = "Select a row and click the update button.";
}
/****************************************************************************************************************************************************************/
function SearchForCompany(ip_searchfield, htmlthis, ip_companynumber) {
    console.log("📘: SearchForCompany", ip_searchfield, ip_companynumber);
    return new Promise(resolve => {

        ip_searchfield = ip_searchfield.replace("&","[XXANDXX]");
        var url = "https://europe-west1-redflag-live.cloudfunctions.net/SF_searchcompanies?keyword=" + ip_searchfield + "&baseurl=temp&companynumber=" + ip_companynumber; 
        var request = new XMLHttpRequest();
        request.open('GET', url, true);
        request.onload = function() { 

            if (this.status == 200) {
                if (this.response == "FAILED") {
                    console.log("📕: FAILED : SearchForCompany");  
                    htmlthis.searchstatus = "Search failed.";
                    resolve("FAILED");
                }
                else {
                    var data = JSON.parse(this.response);
                    if (data.data) {
                        if (data.data.companies.totalCount == 0) {
                            htmlthis.searchstatus = "No companies found.";
                            htmlthis.companydata = null;
                            resolve("NODATA");
                        }
                        else {
                            var Companies_Arr1 = new Array();
                            var Companies_Arr2 = new Array();
                            for (var i=0; i < data.data.companies.items.length; i++) {
                                var valueToPush = {};
                                valueToPush.companynumber = data.data.companies.items[i].company_number;
                                valueToPush.companyname   = toTitleCase(data.data.companies.items[i].company_name);
                                var type    = "n/a";
                                var address = "n/a";

                                var addresses = getaddresses(data.data.companies.items[i].addresses);
                                if (addresses.address) valueToPush.address = addresses.address;
                                else valueToPush.address = address;
                                if (addresses.type) valueToPush.type = addresses.type;
                                else valueToPush.type = type;

                                valueToPush.rating = getratingcode(data.data.companies.items[i].generated_rfa_rating); // 13/06/2023
                                if (data.data.companies.items[i].generated_rfa_rating == "GOLD" || data.data.companies.items[i].generated_rfa_rating == "SILVER" || data.data.companies.items[i].generated_rfa_rating == "BRONZE" || data.data.companies.items[i].generated_rfa_rating == "AMBER" ||
                                    data.data.companies.items[i].generated_rfa_rating == "ONE_RED_FLAG" || data.data.companies.items[i].generated_rfa_rating == "TWO_RED_FLAGS" || data.data.companies.items[i].generated_rfa_rating == "THREE_RED_FLAGS" || data.data.companies.items[i].generated_rfa_rating == "PROVISIONAL_SILVER" || 
                                    data.data.companies.items[i].generated_rfa_rating == "PROVISIONAL_BRONZE" || data.data.companies.items[i].generated_rfa_rating == "NEWLY_INCORPORATED" || data.data.companies.items[i].generated_rfa_rating == "PROVISIONAL_ONE_RED_FLAG" || data.data.companies.items[i].generated_rfa_rating == "PROVISIONAL_TWO_RED_FLAGS") {
                                    Companies_Arr1.push(valueToPush);
                                }
                                else Companies_Arr2.push(valueToPush);
                            }     
                            for (var i=0; i < Companies_Arr2.length; i++) { Companies_Arr1.push(Companies_Arr2[i]); }
                            htmlthis.companydata = Companies_Arr1;
                            htmlthis.searchstatus = "";
                            resolve("SUCCESS");
                        }
                    }
                }
            }
        }
        request.send();
    });
}
/****************************************************************************************************************************************************************/
async function UpdateAccountRecord(htmlthis, ip_refresh) {
    console.log("📘: UpdateAccountRecord");
    var CompanyData = await RFAGetCompanyData(gv_selectedcompanynumber, gv_userdetails.baseurl); 
    if (window.location.origin.includes("charlesdean")) var directors = await RFAGetCompanyDataDirectors(gv_selectedcompanynumber, gv_userdetails.baseurl);
    else var directors = null;
    if (CompanyData.Status == "SUCCESS") {
        if (htmlthis.objectApiName == "Account") var Status = await UpdateRecord(htmlthis, CompanyData, ip_refresh, directors); // 04/08/2022
        else var Status = await UpdateRecord_Lead(htmlthis, CompanyData, ip_refresh);
        htmlthis.companydiv = "flex-container3 companydiv display";
        htmlthis.LookupDiv = "displaynone";
        htmlthis.showModal = false;
    } else console.log("📕:  Function: UpdateAccountRecord: Failed to get company data from RFA API", gv_selectedcompanynumber);
}
/****************************************************************************************************************************************************************/
function UpdateRecord_Lead(htmlthis, ip_CompanyData, ip_refresh) {
    return new Promise(resolve => {
        var fields = fm_buildupdatefields(htmlthis, ip_CompanyData, null, gv_record_fm_fields, gv_namespace, window.location.origin);
        console.log("📘: UpdateRecord Lead : Fields, before update", fields);
        const recordInput = { fields };
        updateRecord(recordInput).then(() => {
            htmlthis.dispatchEvent( new ShowToastEvent({ title : 'Success', message : 'Lead has been updated successfully.', variant : 'success' }) );
            gv_toomanyrows = "";
            if (ip_refresh == "No") UpdateCredits(htmlthis);
        })
        .catch(error => {
            console.log("📕: ERROR : UpdateRecord_Lead", error);
            htmlthis.dispatchEvent( new ShowToastEvent ({ title : 'Error updating record', message : error.body.message, variant : 'error' }) );
        });
        resolve("SUCCESS");
    });
}
/****************************************************************************************************************************************************************/
function UpdateRecord(htmlthis, ip_CompanyData, ip_refresh, ip_directors) {
    return new Promise(resolve => {
        var fields = fm_buildupdatefields(htmlthis, ip_CompanyData, ip_directors, gv_record_fm_fields, gv_namespace, window.location.origin);
        console.log("📘: UpdateRecord Account : Fields, before update", fields);
        const recordInput = { fields };
        updateRecord(recordInput).then(() => {
            htmlthis.dispatchEvent( new ShowToastEvent({ title   : 'Success', message : 'Account has been updated successfully.', variant : 'success' }) );
            gv_toomanyrows = "";
            if (ip_refresh == "No") UpdateCredits(htmlthis);
        })
        .catch(error => {
            console.log("📕: ERROR : updateRecord", error, "error.body.message", error.body.message);
            htmlthis.dispatchEvent( new ShowToastEvent ({ title : 'Error updating record', message : error.body.message, variant : 'error' }) ); 
        });
        resolve("SUCCESS");
    });
}
/****************************************************************************************************************************************************************/
function SearchForContacts(ip_htmlthis) {
    var SearchDomain = ip_htmlthis.Domain_Value;
    ip_htmlthis.contactdata2 = Dummy_Arr; // This forces refresh of ip_htmlthis.contactsearchstatus 
    ip_htmlthis.contactsearchstatus = "Searching for contacts, please wait...";
    ip_htmlthis.contactdata = "";
    ip_htmlthis.contactLookupDiv = "display"; // Show Search Table
    gv_ContactData = new Array();
    var url = "https://europe-west1-redflag-live.cloudfunctions.net/SF_GetContacts?baseurl=" + gv_userdetails.baseurl + "&domain=" + SearchDomain;
    var request = new XMLHttpRequest();
    request.open('GET', url, true);
    request.onload = function() {
        if (this.status == 200) {
            console.log("📘: Function excuted successfully (200)");
            var Data = JSON.parse(this.response);
            if (Data.Status == "Success") {
                if (Data.Contacts.errors) {
                    if (Data.Contacts.errors[0].domain == "Domain is invalid") {
                        var Dummy_Arr = new Array();
                        ip_htmlthis.contactdata2 = Dummy_Arr; 
                        ip_htmlthis.contactsearchstatus = "Domain is invalid.";
                    }
                    else {
                        var Dummy_Arr = new Array();
                        ip_htmlthis.contactdata2 = Dummy_Arr; 
                        ip_htmlthis.contactsearchstatus = "Unknown error, please try again.";
                        console.log("📕: function SearchForContacts Unknown error, please try again.");
                    }
                }
                else if (Data.Contacts.success = true) {
                    var data_arr = Data.Contacts.emails;
                    if (!data_arr) ip_htmlthis.contactsearchstatus = "No contacts records found.";
                    else {
                        UpdateCredits(ip_htmlthis);
                        var Contacts_Arr   = new Array();
                        var Contacts_Arr2  = new Array();
                        var ContactRecords = 0;
                        for (var i=0; i < data_arr.length; i++) {
                            var show_record = "Y";
                            if (typeof data_arr[i].firstName === "undefined") var firstName = "Unknown"; 
                            else var firstName = data_arr[i].firstName; 
                            if (typeof data_arr[i].lastName === "undefined") var lastName = "Unknown";	
                            else var lastName = data_arr[i].lastName; 
                            if (typeof data_arr[i].status === "undefined") var status = "n/a";
                            else var status = data_arr[i].status; 
                            if (typeof data_arr[i].position === "undefined") var position = "n/a";
                            else var position = data_arr[i].position.replace("&amp;", "");
                            if (typeof data_arr[i].type === "undefined") var type = "n/a";
                            else var type = data_arr[i].type; 
                            if (typeof data_arr[i].companyName === "undefined") var companyName = "n/a"; 	
                            else var companyName = data_arr[i].companyName; 
                            if (typeof data_arr[i].sourcePage === "undefined") var sourcePage = "n/a";
                            else var sourcePage = data_arr[i].sourcePage; 
                            if (typeof data_arr[i].firstName === "undefined" || typeof data_arr[i].lastName  === "undefined" || typeof data_arr[i].position  === "undefined" ) {
                                var valueToPush = {};
                                valueToPush.firstName 	= firstName;           valueToPush.lastName   = lastName;
                                valueToPush.email 		= data_arr[i].email;   valueToPush.status 	  = status;
                                valueToPush.position 	= position;            valueToPush.type 	  = type;
                                valueToPush.CompanyName	= companyName; 	       valueToPush.sourcePage = sourcePage; 	
                                Contacts_Arr2.push(valueToPush);
                                ContactRecords += 1;
                                show_record = "N";
                            }
                            if (data_arr[i].status == "verified" && show_record == "Y") {
                                var valueToPush = {};
                                valueToPush.firstName 	= firstName;           valueToPush.lastName   = lastName;
                                valueToPush.email 		= data_arr[i].email;   valueToPush.status 	  = status;
                                valueToPush.position 	= position;            valueToPush.type 	  = type;
                                valueToPush.CompanyName	= companyName;         valueToPush.sourcePage = sourcePage; 	
                                Contacts_Arr.push(valueToPush);
                                ContactRecords += 1;
                            }
                        } // for loop
                        for (var i=0; i < Contacts_Arr2.length; i++) { Contacts_Arr.push(Contacts_Arr2[i]); }
                        if (ContactRecords == 0) {
                            console.log("📘: function SearchForContacts ContactRecords == 0");
                            ip_htmlthis.contactdata2 = Contacts_Arr; // This forces refresh of ip_htmlthis.contactsearchstatus
                            ip_htmlthis.contactsearchstatus = "No contact records found.";
                        }
                        else {
                            ip_htmlthis.contactdata = Contacts_Arr;
                            gv_ContactData = Contacts_Arr;
                            ip_htmlthis.contactsearchstatus = "Found " + ContactRecords + " contact records.";
                        }
                    } //(!data_arr)
                }  // Data.Contacts.success = true
            } //Data.Status == "Success"
        } // (this.status == 200) 
        else console.log("📕: SearchForContacts Function excuted failed", this.status);
    }
    request.send();
}
/****************************************************************************************************************************************************************/
async function CreateLeads(ip_htmlthis) {
    for (var i=0; i < gv_ContactSelectedRows.length; i++) {
        var Email = gv_ContactSelectedRows[i].email;
        for (var x=0; x < gv_ContactData.length; x++) {
            if (Email == gv_ContactData[x].email) {
                var Status = CreateLeadRecord(gv_ContactData[x], ip_htmlthis);    
                await new Promise(resolve => setTimeout(resolve, 1000)); // 1 sec
            }
        }
    }
    await new Promise(resolve => setTimeout(resolve, 1000)); // 1 sec
    var Dummy_Arr = new Array();
    ip_htmlthis.contactdata2 = Dummy_Arr; 
    ip_htmlthis.contactsearchstatus = "Finished creating lead(s) records...";
}
/****************************************************************************************************************************************************************/
function CreateLeadRecord(ip_Lead, ip_htmlthis) {
    var fields = {};
    fields.Company  = ip_htmlthis.rfa_company_name; fields[LEADRFACompanyNumber.fieldApiName] = ip_htmlthis.rfa_company_number; fields.Email = ip_Lead.email; fields.FirstName = ip_Lead.firstName; fields.LastName = ip_Lead.lastName; fields.LeadSource = 'RFA App'; 
    var todaysDate = new Date();
    var RFAUpdateDate = convertDate(todaysDate);
    fields[LEADRFAUPDATEDATE.fieldApiName] = RFAUpdateDate;
    var recordInput = { apiName: leadMainObject.objectApiName, fields };
    createRecord(recordInput).then(leadobj => { 
        console.log("📗: leadobj", leadobj, "New Lead", ip_Lead.email); })
    .catch(error => { 
        console.log("📕: function CreateLeadRecord", error); 
    });
}
/****************************************************************************************************************************************************************/
async function CreateContacts(ip_htmlthis) {
    for (var i=0; i < gv_ContactSelectedRows.length; i++) {
        var Email = gv_ContactSelectedRows[i].email;
        for (var x=0; x < gv_ContactData.length; x++) {
            if (Email == gv_ContactData[x].email) {
                var Status = CreateContact2(gv_ContactData[x], ip_htmlthis);    
                await new Promise(resolve => setTimeout(resolve, 1000)); // 1 sec
            }
        }
    }
    await new Promise(resolve => setTimeout(resolve, 1000)); // 1 sec
    var Dummy_Arr = new Array();
    ip_htmlthis.contactdata2 = Dummy_Arr; 
    ip_htmlthis.contactsearchstatus = "Finished creating contact(s) records...";
}
/****************************************************************************************************************************************************************/
// With out Apex Class - ignore duplicate error
function CreateContact2(ip_Contact, ip_htmlthis) {
    console.log("📘: Contact: NOT FOUND", ip_Contact.email, "Create contact!");
    const fields = {};
    fields[conFirstName.fieldApiName] = ip_Contact.firstName;
    fields[conLastName.fieldApiName]  = ip_Contact.lastName;
    fields[conEmail.fieldApiName]     = ip_Contact.email;
    fields[conAccountId.fieldApiName] = ip_htmlthis.recordId;
    if (ip_Contact.position) { var Position = ip_Contact.position.replace("&amp;", ""); fields[conTitle.fieldApiName] = Position; }
    if (ip_Contact.sourcePage) fields[conSocialMedia.fieldApiName] = ip_Contact.sourcePage;

    // 03/10/2023
    if (window.location.origin.includes("sefirst")) {
        if (ip_htmlthis.rfaphone) {
            console.log("📘: rfaphone", ip_htmlthis.rfaphone);
            fields.Switchboard_Phone__c = ip_htmlthis.rfaphone;
        }
    }
    // 03/10/2023

    const recordInput = { apiName: conMainObject.objectApiName, fields };
    createRecord(recordInput)
    .then(contactobj => { console.log("📗: function CreateContact2: Contact created. " + ip_Contact.email); })
    .catch(error => { console.log("📕: function CreateContact2 Error", error); });
}
/****************************************************************************************************************************************************************/
async function B2B_Searchforrecords(ip_htmlthis) {
    var functionname = "B2B_Searchforrecords";
    ip_htmlthis.searchcriteriastatus = "";
    var havecriteria = "N";
    if (ip_htmlthis.B2B_CampaignName_Value == "") { 
        ip_htmlthis.searchcriteriastatus = "Please enter campaign name.";
        return;
    }
    var Query   = "{ companies ( where: { and: [ ";
    var turnoverfrom = parseInt(ip_htmlthis.B2B_turnoverfrom_Value);
    var turnoverto   = parseInt(ip_htmlthis.B2B_turnoverto_Value);
    if (turnoverfrom > 0 && turnoverto > 0) {
        if (turnoverfrom > turnoverto) {
            ip_htmlthis.searchcriteriastatus = "Turnover minimum must be less than maximum.";
            return;
        }   
    }
    if (turnoverfrom > 0 || turnoverto > 0) {
        if (turnoverfrom > 0) {
            Query += " { or: [";
            Query += "{ turnover: { gt: " + turnoverfrom + " } }";
            if (ip_htmlthis.B2B_estimateturnovertoggle_Value == "ON") Query += "{ estimated_turnover: { gt: " + turnoverfrom + " } }";
            Query += " ] }";
        }
        if (turnoverto > 0) {
            Query += " { or: [";
            Query += "{ turnover: { lt: " + turnoverto + " } }";
            if (ip_htmlthis.B2B_estimateturnovertoggle_Value == "ON") Query += "{ estimated_turnover: { lt: " + turnoverto + " } }";
            Query += " ] }";
        }
    }
    var employeesfrom = parseInt(ip_htmlthis.B2B_employeesfrom_Value);
    var employeesto   = parseInt(ip_htmlthis.B2B_employeesto_Value);
    if (employeesfrom > 0 && employeesto > 0) {
        if (employeesfrom > employeesto) {
            ip_htmlthis.searchcriteriastatus = "Employees minimum must be less than maximum.";
            return;
        }    
    }        
    if (employeesfrom > 0 || employeesto > 0) {
        if (employeesfrom > 0) {
            Query += " { or: [";
            Query += "{ employees: { gt: " + employeesfrom + " } }";   
            if (ip_htmlthis.B2B_estimateemployeestoggle_Value == "ON") Query += "{ estimated_employees: { gt: " + employeesfrom + " } }";
            Query += " ] }";
        }
        if (employeesto > 0) {
            Query += " { or: [";
            Query += "{ employees: { lt: " + employeesto + " } }";   
            if (ip_htmlthis.B2B_estimateemployeestoggle_Value == "ON") Query += "{ estimated_employees: { lt: " + employeesto + " } }";
            Query += " ] }";
        }
    }
    if (ip_htmlthis.B2B_incorporationdatefrom_Value && ip_htmlthis.B2B_incorporationdateto_Value) {
        if (ip_htmlthis.B2B_incorporationdatefrom_Value > ip_htmlthis.B2B_incorporationdateto_Value) {
            ip_htmlthis.searchcriteriastatus = "Incorporation from date must be less than to date.";
            return;
        }        
        Query += '{ incorporation_date: {gt:\\"' + ip_htmlthis.B2B_incorporationdatefrom_Value + '\\", lt:\\"' + ip_htmlthis.B2B_incorporationdateto_Value +'\\"} }, ';
        havecriteria = "Y";
    }
    else {
        if (ip_htmlthis.B2B_incorporationdatefrom_Value) { Query += '{ incorporation_date: {gt:\\"' + ip_htmlthis.B2B_incorporationdatefrom_Value + '\\"} }, '; havecriteria = "Y"; }
        if (ip_htmlthis.B2B_incorporationdateto_Value) { Query += '{ incorporation_date: {lt:\\"' + ip_htmlthis.B2B_incorporationdateto_Value + '\\"} }, '; havecriteria = "Y"; }
    }
    if (ip_htmlthis.B2B_lastupdatefrom_Value && ip_htmlthis.B2B_lastupdateto_Value) {
        if (ip_htmlthis.B2B_lastupdatefrom_Value > ip_htmlthis.B2B_lastupdateto_Value) {
            ip_htmlthis.searchcriteriastatus = "Incorporation from date must be less than to date.";
            return;
        }        
        Query += '{ last_updated: {gt:\\"' + ip_htmlthis.B2B_lastupdatefrom_Value + '\\", lt:\\"' + ip_htmlthis.B2B_lastupdateto_Value +'\\"} }, ';
        havecriteria = "Y";
    }
    else {
        if (ip_htmlthis.B2B_lastupdatefrom_Value) { Query += '{ last_updated: {gt:\\"' + ip_htmlthis.B2B_lastupdatefrom_Value + '\\"} }, '; havecriteria = "Y"; }
        if (ip_htmlthis.B2B_lastupdateto_Value) { Query += '{ last_updated: {lt:\\"' + ip_htmlthis.B2B_lastupdateto_Value + '\\"} }, '; havecriteria = "Y"; }
    }
    var totalassetsfrom = parseInt(ip_htmlthis.B2B_totalassetsfrom_Value);
    var totalassetsto   = parseInt(ip_htmlthis.B2B_totalassetsto_Value);
    if (totalassetsfrom > 0 && totalassetsto > 0) {
        if (totalassetsfrom > totalassetsto) {
            ip_htmlthis.searchcriteriastatus = "Total Assets minimum must be less than maximum.";
            return;
        }        
        Query += "{ total_assets: { gt: " + totalassetsfrom + ", lt: " + totalassetsto +" } }, ";
        havecriteria = "Y";
    }
    else {
        if (totalassetsfrom > 0 ) { Query += "{ total_assets: { gt: " + totalassetsfrom + " } }, "; havecriteria = "Y"; }
        if (totalassetsto > 0 ) { Query += "{ total_assets: { lt: " + totalassetsto + " } }, "; havecriteria = "Y"; }
    }
    var totalcreditlimitfrom = parseInt(ip_htmlthis.B2B_creditlimitfrom_Value);
    var totalcreditlimitto   = parseInt(ip_htmlthis.B2B_creditlimitto_Value);
    if (totalcreditlimitfrom > 0 && totalcreditlimitto > 0) {
        if (totalcreditlimitfrom > totalcreditlimitto) {
            ip_htmlthis.searchcriteriastatus = "Credit Limit minimum must be less than maximum.";
            return;
        }        
        Query += "{ credit_limit: { gt: " + totalcreditlimitfrom + ", lt: " + totalcreditlimitto +" } }, ";
        havecriteria = "Y";
    }
    else {
        if (totalcreditlimitfrom > 0 ) { Query += "{ credit_limit: { gt: " + totalcreditlimitfrom + " } }, "; havecriteria = "Y"; }
        if (totalcreditlimitto > 0 ) { Query += "{ credit_limit: { lt: " + totalcreditlimitto + " } }, "; havecriteria = "Y"; }
    }
    let selections = ip_htmlthis.template.querySelector('c-mutli-select-picklist');
    if (selections == null) console.log("📘: " + functionname, "RFA Rating Multi picklist is null");  
    else {
        var Ratings = "";
        var FirstOne = "Y";
        if (selections.values[0].selected == true) Ratings = "ALL";
        else {
            for (var x=1; x < selections.values.length; x++) {
                if (selections.values[x].selected == true) {
                    if (FirstOne == "Y") { Ratings = selections.values[x].value; FirstOne = "N";
                    } else Ratings += "," + selections.values[x].value;
                }
            }
        }
        if (Ratings !== "ALL") { Query += "{ generated_rfa_rating: { in: [" + Ratings + "] } }, "; havecriteria = "Y"; }
    }
    if (ip_htmlthis.B2B_keywordfilter_Value)        { Query += '{ company_name:{eq:\\"' + ip_htmlthis.B2B_keywordfilter_Value + '\\"} }, '; havecriteria = "Y"; }
    if (ip_htmlthis.hasnamedcontactvalue == "ON")   { Query += "{ has_contact :{ eq:true} }, "; havecriteria = "Y"; }
    if (ip_htmlthis.hasfiledaccountsvalue == "ON")  { Query += "{ has_financials :{ eq:true} }, "; havecriteria = "Y"; }
    if (ip_htmlthis.hasphonecontactvalue == "ON")   { Query += "{ has_phone_contact :{ eq:true} }, "; havecriteria = "Y"; }
    if (ip_htmlthis.excludenontradingvalue == "ON") { Query += "{ is_trading :{ eq:true} }, "; havecriteria = "Y"; }
    if (ip_htmlthis.hasemailcontactvalue == "ON")   { Query += "{ has_email_contact :{ eq:true} }, "; havecriteria = "Y"; }
    if (ip_htmlthis.excludetpsvalue == "ON")        { Query += "{ tps_registered :{ eq:true} }, "; havecriteria = "Y"; }
    if (ip_htmlthis.growthvalue == "veryunlikely")  { Query += "{ growth_score:{gt:0,lt:34 } }, "; havecriteria = "Y"; }
    if (ip_htmlthis.growthvalue == "unlikley")      { Query += "{ growth_score:{gt:34,lt:55 } }, "; havecriteria = "Y"; }
    if (ip_htmlthis.growthvalue == "likely")        { Query += "{ growth_score:{gt:55,lt:65 } }, "; havecriteria = "Y"; }
    if (ip_htmlthis.growthvalue == "verylikely")    { Query += "{ growth_score:{gt:65,lt:100 } }, "; havecriteria = "Y"; }
    if (ip_htmlthis.companytypevalue == "limited")  { Query += '{ company_type:{eq:\\"ltd\\"} }, '; havecriteria = "Y"; }
    if (ip_htmlthis.companytypevalue == "plc")      { Query += '{ company_type:{eq:\\"plc\\"} }, '; havecriteria = "Y"; }
    if (ip_htmlthis.companytypevalue == "llp")      { Query += '{ company_type:{eq:\\"llp\\"} }, '; havecriteria = "Y"; }
    if (ip_htmlthis.companytypevalue == "nonlimited") { Query += '{ company_type:{eq:\\"other\\"} }, '; havecriteria = "Y"; }
    if (ip_htmlthis.sicsectionvalue !== "ALL") { Query += '{ sic07_sections : {some: {eq:\\"' + ip_htmlthis.sicsectionvalue + '\\"}} },'; havecriteria = "Y"; }
    var siccodesquery = "";
    var hassiccodesquery = "N";
    if (ip_htmlthis.siccodesvalue1  == "ON") {siccodesquery += '\\"'  + ip_htmlthis.siccodeslabel1.substring(0,5) + '\\"'; hassiccodesquery = "Y";}
    if (ip_htmlthis.siccodesvalue2  == "ON") {siccodesquery += ',\\"' + ip_htmlthis.siccodeslabel2.substring(0,5) + '\\"'; hassiccodesquery = "Y";}
    if (ip_htmlthis.siccodesvalue3  == "ON") {siccodesquery += ',\\"' + ip_htmlthis.siccodeslabel3.substring(0,5) + '\\"'; hassiccodesquery = "Y";}
    if (ip_htmlthis.siccodesvalue4  == "ON") {siccodesquery += ',\\"' + ip_htmlthis.siccodeslabel4.substring(0,5) + '\\"'; hassiccodesquery = "Y";}
    if (ip_htmlthis.siccodesvalue5  == "ON") {siccodesquery += ',\\"' + ip_htmlthis.siccodeslabel5.substring(0,5) + '\\"'; hassiccodesquery = "Y";}
    if (ip_htmlthis.siccodesvalue6  == "ON") {siccodesquery += ',\\"' + ip_htmlthis.siccodeslabel6.substring(0,5) + '\\"'; hassiccodesquery = "Y";}
    if (ip_htmlthis.siccodesvalue7  == "ON") {siccodesquery += ',\\"' + ip_htmlthis.siccodeslabel7.substring(0,5) + '\\"'; hassiccodesquery = "Y";}
    if (ip_htmlthis.siccodesvalue8  == "ON") {siccodesquery += ',\\"' + ip_htmlthis.siccodeslabel8.substring(0,5) + '\\"'; hassiccodesquery = "Y";}
    if (ip_htmlthis.siccodesvalue9  == "ON") {siccodesquery += ',\\"' + ip_htmlthis.siccodeslabel9.substring(0,5) + '\\"'; hassiccodesquery = "Y";}
    if (ip_htmlthis.siccodesvalue10 == "ON") {siccodesquery += ',\\"' + ip_htmlthis.siccodeslabel10.substring(0,5) + '\\"'; hassiccodesquery = "Y";}
    if (ip_htmlthis.siccodesvalue11 == "ON") {siccodesquery += ',\\"' + ip_htmlthis.siccodeslabel11.substring(0,5) + '\\"'; hassiccodesquery = "Y";}
    if (ip_htmlthis.siccodesvalue12 == "ON") {siccodesquery += ',\\"' + ip_htmlthis.siccodeslabel12.substring(0,5) + '\\"'; hassiccodesquery = "Y";}
    if (hassiccodesquery == "Y") Query += "{ sic07_codes : {some: {in:[" +siccodesquery + "]}} },"; havecriteria = "Y" 
    if ((ip_htmlthis.postcodevalue !== "" && ip_htmlthis.milesvalue == "") || (ip_htmlthis.postcodevalue == "" && ip_htmlthis.milesvalue !== "")) {
        ip_htmlthis.searchcriteriastatus = "Please enter a postcode with radius."; 
        return;
    }
    if (ip_htmlthis.milesvalue !== "" && ip_htmlthis.milesvalue < 1 ) {
        ip_htmlthis.searchcriteriastatus = "Radius must be greater than zero."; 
        return;
    }
    if (ip_htmlthis.postcodevalue !== "" && ip_htmlthis.milesvalue !== "") {
        var Data = await GetCoordinates(ip_htmlthis.postcodevalue);
        if (Data.Status == "SUCCESS") {
            if (ip_htmlthis.locationtypevalue == 'trading') { Query += '{ and: [ {addresses: {some: { type: {eq: \\"trading\\"}}}},' + ' { addresses: { some: { location: { within: { latitude:' + Data.Latitude + ',longitude:' + Data.Longitude + ',distance:\\"' + ip_htmlthis.milesvalue + ' miles\\" }}}}}] }'; }
            else if (ip_htmlthis.locationtypevalue == 'registered') { Query += '{ and: [ {addresses: {some: { type: {eq: \\"registered\\"}}}},' + ' { addresses: { some: { location: { within: { latitude:' + Data.Latitude + ',longitude:' + Data.Longitude + ',distance:\\"' + ip_htmlthis.milesvalue + ' miles\\" }}}}}] }'; }
            else Query += '{ addresses: { some: { location: { within: { latitude:' + Data.Latitude + ',longitude:' + Data.Longitude + ',distance:\\"' + ip_htmlthis.milesvalue + ' miles\\" }}}} }'; 
            havecriteria = "Y";
        }
    }
    if (ip_htmlthis.B2B_auditorvalue) { Query  += '{ auditor :{ eq:\\"' + ip_htmlthis.B2B_auditorvalue + '\\" } }, '; havecriteria = "Y"; }
    console.log("📘: mortgages", ip_htmlthis.B2B_mortgageholder_Value, ip_htmlthis.B2B_mortgagedatefrom_Value, ip_htmlthis.B2B_mortgagedateto_Value, ip_htmlthis.mortgagestatusvalue);

    if ((ip_htmlthis.B2B_mortgageholder_Value) || (ip_htmlthis.B2B_mortgagedatefrom_Value) || (ip_htmlthis.B2B_mortgagedateto_Value) || (ip_htmlthis.mortgagestatusvalue) ) {
        var mortgagequery = ' [{ '+ ' mortgages: ' + '  { '+ 'all: '+ '{ '+ '    and: '+ '    [ ';
        if (ip_htmlthis.B2B_mortgagedatefrom_Value) mortgagequery += ' { date_created: { gt:\\"' + ip_htmlthis.B2B_mortgagedatefrom_Value + '\\"} }';
        if (ip_htmlthis.B2B_mortgagedateto_Value)   mortgagequery += ' { date_created: { lt:\\"' + ip_htmlthis.B2B_mortgagedateto_Value + '\\"} }';
        if (ip_htmlthis.B2B_mortgageholder_Value)   mortgagequery += ' { holder: { contains : \\"' + ip_htmlthis.B2B_mortgageholder_Value + '\\"} }';
        if (ip_htmlthis.mortgagestatusvalue)        mortgagequery += ' { satisfied: { eq : \\"' + ip_htmlthis.mortgagestatusvalue + '\\"} }';
        mortgagequery += ' ] } } } ] ';
        console.log("📘: mortgagequery", mortgagequery);
        Query += mortgagequery;
        havecriteria = "Y"; 
    }
    if (havecriteria == "N") { ip_htmlthis.searchcriteriastatus = "Please enter some search criteria."; return; }
    ip_htmlthis.searchcriteriastatus = "Searching for leads, please wait...";
    var ScheduleQuery = Query + "] }";
    var searchquery = Query + "] } take: " + GRAPHQL_TAKE + ", skip: 0) ";
    console.log("📘: Query" , searchquery);
    var request = new XMLHttpRequest();
    request.open('GET', "https://europe-west1-redflag-live.cloudfunctions.net/SF_B2B_QueryCompanies?query=" + searchquery, true);
    request.onload = function() {
        console.log("📘: " + functionname, "SF_B2B_QueryCompanies - Status" , this.status);
        if (this.status == 200) {
            ip_htmlthis.searchcriteriastatus = "";
            var data = JSON.parse(this.response);
            if (data.data) {
                if (data.data.companies.totalCount > MAXRECORDS) {
                    ip_htmlthis.searchstatus2 = "Found " + formatNumber(data.data.companies.totalCount) + " Leads, the maximum number allowed per schedule is " + formatNumber(MAXRECORDS);
                    ip_htmlthis.B2B_generateleadsdisabled = true; ip_htmlthis.B2B_generateleadsdisabledcon = true; // Disable Generate Leads button
                }
                else if (data.data.companies.totalCount > 0) {
                    if (data.data.companies.totalCount > gv_userdetails.creditcounter) {
                        ip_htmlthis.searchstatus2 = "Found " + formatNumber(data.data.companies.totalCount) + " Leads, but you do not have enough credits, please contact Red Flag Alert Support.";
                        ip_htmlthis.B2B_generateleadsdisabled = true; ip_htmlthis.B2B_generateleadsdisabledcon = true; // Disable Generate Leads button
                    }
                    else {
                        ip_htmlthis.searchstatus2 = "Ready to generate " + formatNumber(data.data.companies.totalCount) + " Leads."; ip_htmlthis.B2B_generateleadsdisabled = false; // Enable Generate Leads button
                        ip_htmlthis.B2B_generateleadsdisabledcon = false; // Enable Generate Leads button
                    }
                }
                else {
                    ip_htmlthis.searchstatus2 = "No lead records found, please change your search critieria."; ip_htmlthis.B2B_generateleadsdisabled = true; // Disable Generate Leads button
                    ip_htmlthis.B2B_generateleadsdisabledcon = true; // Disable Generate Leads button
                }
                var Companies_Arr1 = new Array();
                for (var i=0; i < data.data.companies.items.length; i++) {
                    var valueToPush = {};
                    valueToPush.companynumber = data.data.companies.items[i].company_number;        // 19/12/2022
                    valueToPush.companyname = toTitleCase(data.data.companies.items[i].company_name);
                    var Postcode = "n/a";
                    if (data.data.companies.items[i].addresses) {
                        for (var x=0; x < data.data.companies.items[i].addresses.length; x++) {
                            if (data.data.companies.items[i].addresses[x].type == "trading") {
                                Postcode = data.data.companies.items[i].addresses[x].postcode;
                                break;
                            } else Postcode = data.data.companies.items[i].addresses[x].postcode;                                
                        }
                    }
                    valueToPush.address = Postcode;
                    if (data.data.companies.items[i].company_type) valueToPush.type = toTitleCase(data.data.companies.items[i].company_type);
                    else valueToPush.type = "n/a";
                    valueToPush.rating = getratingcode(data.data.companies.items[i].generated_rfa_rating); // 13/06/2023
                    var Turnover = "n/a";
                    if (data.data.companies.items[i].estimated_turnover) Turnover = formatNumber(data.data.companies.items[i].estimated_turnover) + " (est)";
                    if (data.data.companies.items[i].turnover) Turnover = formatNumber(data.data.companies.items[i].turnover);
                    valueToPush.turnover = Turnover;
                    var SIC = "n/a"
                    if (data.data.companies.items[i].sic07_descriptions) SIC = data.data.companies.items[i].sic07_descriptions[0];    
                    valueToPush.sicdescription = SIC;
                    Companies_Arr1.push(valueToPush);
                }
                ip_htmlthis.companydiv2  = "flex-container3 companydiv display"; ip_htmlthis.LookupDiv2 = "display";
                ip_htmlthis.companydata2 = Companies_Arr1;       ip_htmlthis.ResultsB2BModal  = true; 
                ip_htmlthis.showModal    = false;                ip_htmlthis.ShowContactModal = false;
                gv_B2B_schedulerecord = {};
                gv_B2B_schedulerecord.campaignname = ip_htmlthis.B2B_CampaignName_Value;            gv_B2B_schedulerecord.query        = ScheduleQuery;
                gv_B2B_schedulerecord.totalrecords = data.data.companies.totalCount;                gv_B2B_schedulerecord.jobfilter    = ip_htmlthis.jobfiltervalue;
            }
            else {
                ip_htmlthis.searchstatus2 = "Error, searching for leads, please try again.";
                ip_htmlthis.B2B_generateleadsdisabled = true; // Disable Generate Leads button
                ip_htmlthis.B2B_generateleadsdisabledcon = true; // Disable Generate Leads button
                console.log("📕: " + functionname, "SF_B2B_QueryCompanies failed", data);
            }
        }
    }
    request.send();
}
/****************************************************************************************************************************************************************/
function B2B_CreateSchedule(ip_htmlthis, ip_contacts) {
    console.log("📘: B2B_CreateSchedule Start", gv_B2B_schedulerecord, "Contacts", ip_contacts);
    const fields = {};
    fields[RFAScheduler_Field_SearchName.fieldApiName]     = gv_B2B_schedulerecord.campaignname;     
    fields[RFAScheduler_Field_Query.fieldApiName]          = gv_B2B_schedulerecord.query;
    fields[RFAScheduler_Field_TotalRecords.fieldApiName]   = gv_B2B_schedulerecord.totalrecords;     
    fields[RFAScheduler_Field_Key.fieldApiName]            = gv_userdetails.key;
    fields[RFAScheduler_Field_Status.fieldApiName]         = "Waiting"; // First run will be start! 
    fields[RFAScheduler_Field_NewLeads.fieldApiName]       = "0";     
    fields[RFAScheduler_Field_QuerySkip.fieldApiName]      = "0";
    fields[RFAScheduler_Field_DuplicateLeads.fieldApiName] = "0";
    if (ip_contacts == "Yes") fields[RFAScheduler_With_Contacts.fieldApiName] = "Yes";
    else fields[RFAScheduler_With_Contacts.fieldApiName]   = "No";
    fields[RFAScheduler_Job_Filter.fieldApiName]           = gv_B2B_schedulerecord.jobfilter;
    const recordInput = { apiName: RFAScheduler_conMainObject.objectApiName, fields };
    createRecord(recordInput).then(recordobj => {
        console.log("📗: B2B_CreateSchedule: create RFA Scheduler record successful.");
        ip_htmlthis.dispatchEvent( new ShowToastEvent({ title: 'Success', message: 'Your scheduler for generating new leads has been created successfully.', variant: 'success', }), );
        ip_htmlthis.ShowB2BModal     = true;         ip_htmlthis.showModal        = false;        ip_htmlthis.ShowContactModal = false;        ip_htmlthis.ResultsB2BModal  = false; 
        Apex_B2B_Scheduler({ }).then(result => { console.log("result: " + result); })
        .catch(error => { const event = new ShowToastEvent( { title : 'Error', message : 'Error B2B Scheduler. Please Contact System Admin', variant : 'error' });
            this.dispatchEvent(event); });
    })
    .catch(error => { console.log("📕: B2B_CreateSchedule failed to create RFA Scheduler record.", error);
        ip_htmlthis.dispatchEvent( new ShowToastEvent( { title   : "Error creating RFA Scheduler record.", message : error.body.message, variant : "error", }), );
    });
}
/****************************************************************************************************************************************************************/