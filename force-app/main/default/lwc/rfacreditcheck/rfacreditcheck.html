<template>
    <lightning-card title={apptitle} icon-name="standard:account_info">
        <div class="slds-m-around_medium">
            <div id="companydiv1" class={companydiv1}>
                <div style="margin-bottom:-9px">
                    <div class="first-boxes">
                        <div class={RatingStyleName1} id="rfa-div1" data-id="rfa_div1">
                            <p class="box-rating-title">RFA Rating</p>
                            <p class="box-rating" id="rating1" >{rfarating1}</p>
                        </div>
                        <!--
                        <div class={GrowthscoreStyleName}>
                            <p class="box-rating-title">Growth Score</p>
                            <p class="box-rating" >{growthscore}</p>
                        </div>
                        -->
                        <div class={CreditorDaysStyleName1} id="creditordays-div1">
                            <p class="box-rating-title">Creditor Days</p>
                            <p class="box-rating" id="creditordays1">{rfacreditordays1}</p>
                        </div>
                        <div class={CcjRatingStyleName1} id="ccj-div1">
                            <p class="box-rating-title">CCJs</p>
                            <p class="box-rating" id="ccj1">{rfaccjs1}</p>
                        </div>
                        <div class={creditlimitStyleName1} id="creditlimit-div1">
                            <p class="box-rating-title">Credit Limit</p>
                            <p class="box-rating" id="creditlimit1">{rfacreditlimit1}</p>
                        </div>
                        <div class={cashinbankStyleName1} id="cashinbank-div1">
                            <p class="box-rating-title">Cash In Bank</p>
                            <p class="box-rating" id="cashinbank1" >{rfacashinbank1}</p>
                        </div>
                        <div class={networthStyleName1} id="networth-div1">
                            <p class="box-rating-title">Net Worth</p>
                            <p class="box-rating" id="networth1" >{rfanetworth1}</p>
                        </div>
                    </div>
                    <div class="second-boxes">
                        <div class={mr_class}>
                            <p class="box-rating-title">Red Flag Alert<br>Scheduler</p>
                            <p class="box-rating">{mr_mainstatus}</p>
                        </div>
                        <div class={mr_gazette_class}>
                            <p class="box-rating-title">Gazette<br>Notifications</p>
                            <p class="box-rating">{mr_gazette_mainstatus}</p>
                        </div>
                    </div>
                </div>
            </div>
            <lightning-button variant="success" disabled={mainbuttondisabled} label="Credit Check" title="Credit check the current record." onclick={handleClick} class="slds-m-left_x-small"></lightning-button>
            <lightning-button variant="success" disabled={mainbuttondisabled2} label="View Finances" title="View credit check information." onclick={handleClick} class="slds-m-left_x-small"></lightning-button>
            <lightning-button variant="success" disabled={mainbuttondisabled4} label="Full Company Report" title="View all company details on Red Flag Alert Portal." onclick={handlerfareport} class="slds-m-left_x-small"></lightning-button>
            <lightning-button variant="success" disabled={mainbuttondisabled3} label="Find Leads" title="Find and create new lead records." onclick={handleClick} class="slds-m-left_x-small"></lightning-button>
            <lightning-button variant="success" disabled={button_fieldmapping} label="Field Mapping" title="Map your Salesforce fields to Red Flag Alert data." onclick={handleClick} class="slds-m-left_x-small" style="float:right"></lightning-button>
            <lightning-button variant="success" disabled={button_gazette} label="Accounts/Gazette" title="Refresh account records and Gazette Notifications" onclick={handleClick} class="slds-m-left_x-small" style="float:right"></lightning-button>
            <p style="font-weight:700;margin-top:10px;margin-left:20px;">{appstatus}</p>
        </div>
    </lightning-card>

    <template if:true={showModal}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open slds-modal_large" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1">
            <div class="slds-modal__container">
                <header class="slds-modal__header newrfa">
                    <lightning-button-icon 
                        icon-name        = "utility:close" 
                        variant          = "border-filled"
                        class            = "slds-float_right" 
                        onclick          = {closeModal} 
                        alternative-text = "close">
                    </lightning-button-icon>
                    <div class="slds-clearfix">
                        <div class="slds-float_left">
                            <img 
                            src     = {imageURL} 
                            alt     = "User Image" 
                            height  = "38px" 
                            width   = "193px"
                            style   = "float:left;margin-bottom:10px">
                        </div>
                    </div>
                    <div><p class="newrfa_text" style="margin-top:0px"><span style='float:left;'><strong>Credit Check & B2B Leads</strong> | Subscription <strong>{substatus}</strong> | Version <strong>{appversion}</strong></span><span style='float:right;'>Credits <strong>{credits}</strong> of <strong>{creditlimit}</strong></span></p></div>
                </header>
                <div class="slds-modal__content slds-p-around_medium" id="modal-content-id-1">
                    <div class={InputFieldsClass}>
                        <lightning-input style="width:360px" class="side" value={CompanyName} field-name="CompanyName" placeholder="Company name" onchange={handleCompanyNameChange}></lightning-input>
                        <lightning-input style="margin-left:5px;width:140px" class="side" value={CompanyNumber} placeholder="Company number" field-name="CompanyNumber" onchange={handleCompanyNumberChange}></lightning-input>
                        <lightning-button style="margin-top:19px" disabled={searchdisabled} variant="brand" label="Search" title="Search for company" onclick={handleClick} class="slds-m-left_x-small side"></lightning-button>
                    </div>
                    <p style="font-weight:700;text-align:center;margin-bottom:10px;margin-top:10px;">{searchstatus}</p>
                    <div class={LookupDiv} id="LookupDiv" style="height: 400px;">
                        <lightning-datatable
                            key-field         = "id"
                            data              = {companydata}
                            columns           = {SearchTableColumns}
                            max-row-selection = "1"
                            onrowselection    = {handleRowSelection}>
                        </lightning-datatable>
                    </div>    
                    <div id="companydiv" class={companydiv}>
                        <div style="padding-bottom:12px;">
                            <div class="first-boxes">
                                <div class={RatingStyleName} id="rfa-div" data-id="rfa_div">
                                    <p class="box-rating-title">RFA Rating</p>
                                    <p class="box-rating" id="rating" >{rfarating}</p>
                                </div>
                                <div class={GrowthscoreStyleName}>
                                    <p class="box-rating-title">Growth Score</p>
                                    <p class="box-rating" >{growthscore}</p>
                                </div>
                                <div class={CreditorDaysStyleName} id="creditordays-div">
                                    <p class="box-rating-title">Creditor Days</p>
                                    <p class="box-rating" id="creditordays">{rfacreditordays}</p>
                                </div>
                                <div class={CcjRatingStyleName} id="ccj-div">
                                    <p class="box-rating-title">CCJs</p>
                                    <p class="box-rating" id="ccj">{rfaccjs}</p>
                                </div>
                                <div class={creditlimitStyleName} id="creditlimit-div">
                                    <p class="box-rating-title">Credit Limit</p>
                                    <p class="box-rating" id="creditlimit">{rfacreditlimit}</p>
                                </div>
                                <div class={cashinbankStyleName} id="cashinbank-div">
                                    <p class="box-rating-title">Cash In Bank</p>
                                    <p class="box-rating" id="cashinbank" >{rfacashinbank}</p>
                                </div>
                                <div class={networthStyleName} id="networth-div">
                                    <p class="box-rating-title">Net Worth</p>
                                    <p class="box-rating" id="networth" >{rfanetworth}</p>
                                </div>
                            </div>
                            <div class="second-boxes">
                                <div class="box box-linkedin" id="">
                                    <img class="linkedin-image" src="https://toradigital.co.uk/hubspot/live/linkedin2.png">
                                    <p class="box-rating"><a class="linkedin" href={companylinkedinurl} target="_blank" rel="noopener noreferrer">{companylinkedin}</a></p>
                                </div>
                                <div class="box box-linkedin" id="linkedin-director-div">
                                    <img class="linkedin-image" src="https://toradigital.co.uk/hubspot/live/linkedin2.png">
                                    <p class="box-rating"><a class="linkedin" href={personlinkedinurl} target="_blank" rel="noopener noreferrer">{personlinkedin}</a></p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="column1"><p>Company Name</p></div>
                            <div class="column2"><p class="format_data" id="company_name">{rfa_company_name}</p></div>
                            <div class="column3"><p>Company Number</p></div>
                            <div class="column4"><p class="format_data" id="company_number">{rfa_company_number}</p></div>
                        </div>
                        <div class="row">
                            <div class="column1"><p>Incoporation Date</p></div>
                            <div class="column2"><p class="format_data" id="incorporation_date">{rfaincorporationdate}</p></div>
                            <div class="column3"><p>Director(s)</p></div>
                            <div class="column4"><p class="format_data" id="oldestdirector">{Directors}</p></div>
                        </div>
                        <div class="row">
                            <div class="column1"><p>Company Email</p></div>
                            <div class="column2"><p class="format_data" id="company_email">{rfaemail}</p></div>
                            <div class="column3"><p id="actual_turnover">{turnovertitle}</p></div>
                            <div class="column4"><p class="format_data" id="turnover">{rfaturnover}</p></div>
                        </div>
                        <div class="row">
                            <div class="column1"><p>Company Phone</p></div>
                            <div class="column2"><p class="format_data" id="company_telephone">{rfaphone}</p></div>
                            <div class="column3"><p>VAT Number</p></div>
                            <div class="column4"><p class="format_data" id="creditor_days">{rfavatnumber}</p></div>
                        </div>
                        <div class="row">
                            <div class="column1"><p id="actual_employees">Actual/Estimate Employees</p></div>
                            <div class="column2"><p class="format_data" id="employee_numbers">{employees}</p></div>
                            <div class="column3"><p>Last Company Action</p></div>
                            <div class="column4"><p class="format_data" id="last_action">{rfalastaction}</p></div>
                        </div>
                        <div class="row">
                            <div class="column1"><p>SIC Codes</p></div>
                            <div class="column2"><p class="format_data" id="sic_codes">{rfasiccodes}</p></div>
                            <div class="column3"><p>SIC Group Description</p></div>
                            <div class="column4"><p class="format_data" id="sic_group_description">{sicgroupdesc}</p></div>
                        </div>
                        <div class="row">
                            <div class="column1"><p>SIC Description</p></div>
                            <div class="column2"><p class="format_data" id="sic_description">{rfasicdesc}</p></div>
                            <div class="column3"><p>RFA Rating Description</p></div>
                            <div class="column4"><p class="format_data" id="rfa_description">{rfaratingdesc}</p></div>
                        </div>
                        <div class="row">
                            <div class="column1"><p>Trading Address</p></div>
                            <div class="column2"><p class="format_data" id="registered_address">{rfaaddress}</p></div>
                            <div class="column3"><p>Last Filed Accounts</p></div>
                            <div class="column4"><p class="format_data" id="refreshstatus">{lastfiledaccounts}</p></div>
                        </div>
                        <div class="row">
                            <div class="column1"><p>Website</p></div>
                            <div class="column2"><p class="format_data"><a class="linkedin-director2" href={websiteurl} target="_blank" rel="noopener noreferrer">{website}</a></p></div>
                            <div class="column3"><p>RFA Update Date</p></div>
                            <div class="column4"><p class="format_data" id="status_log">{rfaupdatedate}</p></div>
                        </div>
                        <div class="row">
                            <div class="column1"><p>Parent Company</p></div>
                            <div class="column2"><p class="format_data"><a class="linkedin-director2" href={parentcompanynumberurl} target="_blank" rel="noopener noreferrer">{parentcompanynumber}</a></p></div>
                        </div>
                    </div>
                </div>
                <footer class="slds-modal__footer">
                    <div class="float-left">
                        <lightning-button variant="brand" disabled={updatedisabled} label="Update" title="Update record." onclick={updateModal} class="slds-m-left_x-small"></lightning-button>
                        <lightning-button variant="brand" disabled={finddisabled} label="Find Contacts" title="Find Contacts" onclick={FindModal} class="slds-m-left_x-small"></lightning-button>
                        <lightning-button variant="brand" disabled={refreshdisabled} label="Refresh" title="Refresh RFA data, no credits will be used." onclick={refreshModal} class="slds-m-left_x-small"></lightning-button>
                        <lightning-button variant="brand" label="Close" title="Close" onclick={closeModal} class="slds-m-left_x-small"></lightning-button>
                    </div>
                    <div class="float-right">
                        <lightning-button variant="brand" label="Delete RFA Data" title="Delete's ONLY Red Flag Alert custom fields." onclick={clearrfadata} class="slds-m-left_x-small"></lightning-button>
                    </div>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>

    <template if:true={ShowContactModal}>
        <section role="dialog" tabindex="-1" class=" slds-modal slds-fade-in-open slds-modal_large" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1">
            <div class="slds-modal__container">
                <header class="slds-modal__header newrfa">
                    <lightning-button-icon 
                    icon-name        = "utility:close" 
                    variant          = "border-filled"
                    class            = "slds-float_right" 
                    onclick          = {contactcloseModal} 
                    alternative-text = "close">
                    </lightning-button-icon>
                    <div class="slds-clearfix">
                        <div class="slds-float_left">
                            <img 
                                src     = {imageURL} 
                                alt     = "User Image" 
                                height  = "38px" 
                                width   = "193px"
                                style   = "float:left;margin-bottom:10px">
                        </div>
                    </div>
                    <div>
                        <p class="newrfa_text" style="margin-top:0px"><span style='float:left;'><strong>Find contacts, no credits are used for this feature.</strong></span></p>
                    </div>
                </header>
                <div class="slds-modal__content slds-p-around_medium" id="modal-content-id-12">
                    <div class={InputFieldsClass2}>
                        <lightning-input style="width:500px" class="side" value={Domain} field-name="Domain" onchange={handleDomainChange}></lightning-input>
                        <lightning-button style="margin-top:19px" disabled={searchdisabled} variant="brand" label="Search for contacts" title="Search" onclick={ContactshandleClick} class="slds-m-left_x-small side"></lightning-button>
                    </div>
                    <p style="font-weight:700;text-align:center;margin-bottom:10px;margin-top:10px;">{contactsearchstatus} {textxxx}</p>
                    <div class={contactLookupDiv} id="contactLookupDiv" style="height: 400px;">
                        <lightning-datatable
                            key-field       = "id"
                            data            = {contactdata}
                            columns         = {contactSearchTableColumns}
                            onrowselection  = {contacthandleRowSelection}>
                        </lightning-datatable>
                    </div>    
                    <!-- Dummy for refresh -->
                    <div class={contactLookupDiv2} id="contactLookupDiv2" style="height: 400px;display:none">
                        <lightning-datatable
                            key-field       = "id"
                            data            = {contactdata2}
                            columns         = {contactSearchTableColumns2}
                            onrowselection  = {contacthandleRowSelection2}>
                        </lightning-datatable>
                    </div>    
                </div>
                <footer class="slds-modal__footer">
                    <div class="float-left">
                        <lightning-button variant="brand" disabled={creatleaddisabled} label="Create Lead(s)" title="Create Lead(s)" onclick={handleleadclick} class="slds-m-left_x-small"></lightning-button>
                        <lightning-button variant="brand" disabled={creatcontactsdisabled} label="Create Contact(s)" title="Create Contact(s)" onclick={handleClick} class="slds-m-left_x-small"></lightning-button>
                        <lightning-button variant="brand" label="Close" title="Close" onclick={ContactcloseModal} class="slds-m-left_x-small"></lightning-button>
                    </div>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>

    <template if:true={ShowB2BModal}>
        <section role="dialog" tabindex="-1" class=" slds-modal slds-fade-in-open slds-modal_large" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1">
            <div class="slds-modal__container">
                <header class="slds-modal__header newrfa">
                    <lightning-button-icon 
                        icon-name        = "utility:close" 
                        variant          = "border-filled"
                        class            = "slds-float_right" 
                        onclick          = {B2BcloseModal} 
                        alternative-text = "close">
                    </lightning-button-icon>
                    <div class="slds-clearfix">
                        <div class="slds-float_left">
                            <img 
                            src     = {imageURL} 
                            alt     = "User Image" 
                            height  = "38px" 
                            width   = "193px"
                            style   = "float:left;margin-bottom:10px">
                        </div>
                    </div>
                    <div>
                        <p class="newrfa_text" style="margin-top:0px"><span style='float:left;'><strong>B2B Leads</strong> | Subscription <strong>{substatus}</strong> | Version <strong>{appversion}</strong></span><span style='float:right;'>Credits <strong>{credits}</strong> of <strong>{creditlimit}</strong></span></p>
                    </div>
                </header>
                <div class="slds-modal__content slds-p-around_medium" id="modal-content-id-123">
                    <h2 class="slds-text-heading_small"><strong>New Search</strong></h2>
                    <p class="slds-text-body_regular" style="margin-bottom:20px">Find prospects and companies with our company search.</p>
                    <lightning-tabset active-tab-value="one">
                        <lightning-tab label="Company Information" value="one">
                            <lightning-input style="width:500px;margin-bottom:10px" type="text" label="Campaign Name" value={B2B_CampaignName} field-name="B2B_CampaignName" placeholder="Campaign name" onchange={handleB2B_CampaignName}></lightning-input>                
                            <lightning-input style="width:500px;margin-bottom:10px" type="text" label="Keyword filter" value={B2B_keywordfilter} field-name="B2B_keywordfilter" placeholder="filter company name by keyword" onchange={handleB2B_keywordfilter}></lightning-input>                
                             <lightning-input style="width:500px;margin-bottom:10px" 
                                type        = "text" 
                                label       = "Auditor Keyword Filter" 
                                value       = {B2B_auditorvalue} 
                                field-name  = "B2B_auditor" 
                                placeholder = "filter by auditor" 
                                onchange    = {handleB2B_auditor}>
                            </lightning-input> 
                            <lightning-combobox
                                name        = "companytype"
                                label       = "Company Type"
                                value       = {companytypevalue}
                                placeholder = "Select Type"
                                options     = {companytypeoptions}
                                onchange    = {handlecompanytypechange} 
                                style       = "width:350px;margin-bottom:10px">
                            </lightning-combobox>
                            <lightning-combobox
                                name        = "jobfilter"
                                label       = "Contact Job Title Filter"
                                value       = {jobfiltervalue}
                                placeholder = "Select Type"
                                options     = {jobfilteroptions}
                                onchange    = {handlejobfilterchange} 
                                style       = "width:350px;margin-bottom:200px" >
                            </lightning-combobox>
                        </lightning-tab>
                        <lightning-tab label="Scores" value="two">
                            <div style="display: inline-block;margin-right:10px">
                                <c-mutli-select-picklist picklistlabel="RFA Ratings" values={values}></c-mutli-select-picklist>
                                <lightning-button-icon 
                                    icon-name='action:reset_password' 
                                    alternative-text="reset filters" 
                                    onclick={fetchSelectedValues}
                                    variant="bare">
                                </lightning-button-icon>
                                <lightning-combobox
                                    name        = "progress"
                                    label       = "Growth Score"
                                    value       = {growthvalue}
                                    placeholder = "Growth Score"
                                    options     = {growthoptions}
                                    onchange    = {handlegrowthChange} 
                                    style       = "margin-bottom:200px" >
                                </lightning-combobox>
                            </div>
                        </lightning-tab>
                        <lightning-tab label="SIC Codes" value="three">
                            <lightning-combobox
                                name        = "sicsections"
                                label       = "SIC Sections"
                                value       = {sicsectionvalue}
                                placeholder = "Select"
                                options     = {sicsectionoptions}
                                onchange    = {handlesicsectionChange} 
                                style       = "width:800px;" >
                            </lightning-combobox>
                            <lightning-input style="width:500px" type="text" label="Filter SIC Codes by keyword" value={B2B_searchsiccodes} field-name="B2B_searchisccodes" placeholder="filter by keyword" onchange={handleB2B_searchsiccodes}></lightning-input>                
                            <lightning-layout>
                                <lightning-layout-item class={siccodes1class} size="4">
                                    <lightning-radio-group name="siccodes1"
                                        label   = {siccodeslabel1}
                                        options = {siccodesoptions1}
                                        value   = {siccodesvalue1}
                                        onchange= {siccodeschange1} 
                                        type    = "button">
                                    </lightning-radio-group>
                                </lightning-layout-item>
                                <lightning-layout-item class={siccodes2class} size="4">
                                    <lightning-radio-group name="siccodes2"
                                    label   = {siccodeslabel2}
                                    options = {siccodesoptions2}
                                    value   = {siccodesvalue2}
                                    onchange= {siccodeschange2} 
                                    type    = "button">
                                    </lightning-radio-group>
                                </lightning-layout-item>
                                <lightning-layout-item class={siccodes3class} size="4">
                                    <lightning-radio-group name="siccodes3"
                                        label   = {siccodeslabel3}
                                        options = {siccodesoptions3}
                                        value   = {siccodesvalue3}
                                        onchange= {siccodeschange3} 
                                        type    = "button">
                                    </lightning-radio-group>
                                </lightning-layout-item>
                            </lightning-layout>
                            <lightning-layout>
                                <lightning-layout-item class={siccodes4class} size="4">
                                    <lightning-radio-group name="siccodes4"
                                    label   = {siccodeslabel4}
                                    options = {siccodesoptions4}
                                    value   = {siccodesvalue4}
                                    onchange= {siccodeschange4} 
                                    type    = "button">
                                    </lightning-radio-group>
                                </lightning-layout-item>
                                <lightning-layout-item class={siccodes5class} size="4">
                                    <lightning-radio-group name="siccodes5"
                                        label   = {siccodeslabel5}
                                        options = {siccodesoptions5}
                                        value   = {siccodesvalue5}
                                        onchange= {siccodeschange5} 
                                        type    = "button">
                                    </lightning-radio-group>
                                </lightning-layout-item>
                                <lightning-layout-item class={siccodes6class} size="4">
                                    <lightning-radio-group name="siccodes6"
                                    label   = {siccodeslabel6}
                                    options = {siccodesoptions6}
                                    value   = {siccodesvalue6}
                                    onchange= {siccodeschange6} 
                                    type    = "button">
                                    </lightning-radio-group>
                                </lightning-layout-item>
                            </lightning-layout>
                            <lightning-layout>
                                <lightning-layout-item class={siccodes7class} size="4">
                                    <lightning-radio-group name="siccodes7"
                                        label   = {siccodeslabel7}
                                        options = {siccodesoptions7}
                                        value   = {siccodesvalue7}
                                        onchange= {siccodeschange7} 
                                        type    = "button">
                                    </lightning-radio-group>
                                </lightning-layout-item>
                                <lightning-layout-item class={siccodes8class} size="4">
                                    <lightning-radio-group name="siccodes8"
                                    label   = {siccodeslabel8}
                                    options = {siccodesoptions8}
                                    value   = {siccodesvalue8}
                                    onchange= {siccodeschange8} 
                                    type    = "button">
                                    </lightning-radio-group>
                                </lightning-layout-item>
                                <lightning-layout-item class={siccodes9class} size="4">
                                    <lightning-radio-group name="siccodes9"
                                    label   = {siccodeslabel9}
                                    options = {siccodesoptions9}
                                    value   = {siccodesvalue9}
                                    onchange= {siccodeschange9} 
                                    type    = "button">
                                    </lightning-radio-group>
                                </lightning-layout-item>
                            </lightning-layout>
                            <lightning-layout>
                                <lightning-layout-item class={siccodes10class} size="4">
                                    <lightning-radio-group name="siccodes10"
                                        label   = {siccodeslabel10}
                                        options = {siccodesoptions10}
                                        value   = {siccodesvalue10}
                                        onchange= {siccodeschange10} 
                                        type    = "button">
                                    </lightning-radio-group>
                                </lightning-layout-item>
                                <lightning-layout-item class={siccodes11class} size="4">
                                    <lightning-radio-group name="siccodes11"
                                    label   = {siccodeslabel11}
                                    options = {siccodesoptions11}
                                    value   = {siccodesvalue11}
                                    onchange= {siccodeschange11} 
                                    type    = "button">
                                    </lightning-radio-group>
                                </lightning-layout-item>
                                <lightning-layout-item class={siccodes12class} size="4">
                                    <lightning-radio-group name="siccodes12"
                                    label   = {siccodeslabel12}
                                    options = {siccodesoptions12}
                                    value   = {siccodesvalue12}
                                    onchange= {siccodeschange12} 
                                    type    = "button">
                                    </lightning-radio-group>
                                </lightning-layout-item>
                            </lightning-layout>
                        </lightning-tab>
                        <lightning-tab label="Filters" value="four">
                            <div style="margin-top:20px">
                                <span style="display: inline-block;margin-right:60px">
                                    <lightning-radio-group name="radioGroup1"
                                        label   = "Has Named Contact"
                                        options = {hasnamedcontactoptions}
                                        value   = {hasnamedcontactvalue}
                                        onchange= {hasnamedcontactchange} 
                                        type    = "button">
                                    </lightning-radio-group>
                                </span>
                                <span style="display: inline-block">
                                    <lightning-radio-group name="radioGroup2"
                                        label   = "Has Filed Accounts"
                                        options = {hasfiledaccountsoptions}
                                        value   = {hasfiledaccountsvalue}
                                        onchange= {hasfiledaccountschange} 
                                        type    = "button">
                                    </lightning-radio-group>
                                </span>
                            </div>
                            <div style="margin-top:20px">
                                <span style="display: inline-block;margin-right:60px">
                                    <lightning-radio-group name="radioGroup3"
                                        label   = "Has Phone Contact"
                                        options = {hasphonecontactoptions}
                                        value   = {hasphonecontactvalue}
                                        onchange= {hasphonecontactchange} 
                                        type    = "button">
                                    </lightning-radio-group>
                                </span>
                                <span style="display: inline-block">
                                    <lightning-radio-group name="radioGroup4"
                                        label   = "Exclude Non-Trading"
                                        options = {excludenontradingoptions}
                                        value   = {excludenontradingvalue}
                                        onchange= {excludenontradingchange} 
                                        type    = "button">
                                    </lightning-radio-group>
                                </span>
                            </div>
                            <div style="margin-top:20px">
                                <span style="display: inline-block;margin-right:60px">
                                    <lightning-radio-group name="radioGroup5"
                                        label   = "Has Email Contact"
                                        options = {hasemailcontactoptions}
                                        value   = {hasemailcontactvalue}
                                        onchange= {hasemailcontactchange} 
                                        type    = "button">
                                    </lightning-radio-group>
                                </span>
                                <span style="display: inline-block">
                                    <lightning-radio-group name="radioGroup6"
                                        label   = "Exclude TPS/CTPS/GDPR Registered"
                                        options = {excludetpsgoptions}
                                        value   = {excludetpsvalue}
                                        onchange= {excludetpschange} 
                                        type    = "button">
                                    </lightning-radio-group>
                                </span>
                            </div>
                        </lightning-tab>
                        <lightning-tab label="Dates & Ranges" value="five">
                            <span style="display: inline-block;margin-right:10px">
                                <lightning-input style="width:400px" placeholder="Min" type="number" field-name="B2B_turnoverfrom" label="Turnover" onchange={handleB2B_turnoverfrom}></lightning-input>
                            </span>
                            <span style="display: inline-block">
                                <lightning-input style="width:400px" placeholder="Max" type="number" field-name="B2B_turnoverto" label="" onchange={handleB2B_turnoverto}></lightning-input>
                            </span>
                            <div style="margin-top:10px">
                                <span>
                                <lightning-input type="toggle" label="Include Estimated Turnover" name="B2B_includeturnover" onchange={handleB2B_estimateturnoverestoggle}></lightning-input>
                                </span>
                            </div>
                            <span style="display: inline-block;margin-right:10px">
                                <lightning-input style="width:400px" placeholder="Min" type="number" field-name="B2B_employeesfrom" label="Employees" onchange={handleB2B_employeesfrom}></lightning-input>
                            </span>
                            <span style="display: inline-block">
                                <lightning-input style="width:400px" placeholder="Max" type="number" field-name="B2B_employeesto" label="" onchange={handleB2B_employeesto}></lightning-input>
                            </span>
                            <div style="margin-top:10px">
                                <span style="margin-top:10px">
                                <lightning-input type="toggle" label="Include Estimated employees" name="B2B_includeeemployees" onchange={handleB2B_estimateemployeestoggle} ></lightning-input>
                                </span>
                            </div>
                            <div style="margin-top:10px">
                                <span style="display: inline-block;margin-right:10px">
                                    <lightning-input style="width:400px" placeholder="Min" type="number" field-name="B2B_totalassetsfrom" label="Total Assets" onchange={handleB2B_totalassetsfrom}></lightning-input>
                                </span>
                                <span style="display: inline-block">
                                    <lightning-input style="width:400px" placeholder="Max" type="number" field-name="B2B_totalassetsto" label="" onchange={handleB2B_totalassetsto}></lightning-input>
                                </span>
                            </div>
                            <div style="margin-top:10px">
                                <span style="display: inline-block;margin-right:10px">
                                    <lightning-input style="width:400px" placeholder="Min" type="number" field-name="B2B_creditlimitfrom" label="Credit Limit" onchange={handleB2B_creditlimitfrom}></lightning-input>
                                </span>
                                <span style="display: inline-block">
                                    <lightning-input style="width:400px" placeholder="Max" type="number" field-name="B2B_creditlimitto" label="" onchange={handleB2B_creditlimitto}></lightning-input>
                                </span>
                            </div>
                            <div style="margin-top:10px">
                                <span style="display: inline-block;margin-right:10px">
                                    <lightning-input style="width:400px" placeholder="From" type="date" field-name="B2B_incorporationdatefrom" label="Incorporation Date" onchange={handleB2B_incorporationdatefrom}></lightning-input>
                                </span>
                                <span style="display: inline-block">
                                    <lightning-input style="width:400px" placeholder="To" type="date" field-name="B2B_incorporationdateto" label="" onchange={handleB2B_incorporationdateto}></lightning-input>
                                </span>
                            </div>
                            <div style="margin-top:10px">
                                <span style="display: inline-block;margin-right:10px">
                                    <lightning-input style="width:400px" placeholder="From" type="date" field-name="B2B_lastupdateddatefrom" label="Last Update" onchange={handleB2B_lastupdatedfrom}></lightning-input>
                                </span>
                                <span style="display: inline-block">
                                    <lightning-input style="width:400px" placeholder="To" type="date" field-name="B2B_lastupdateddateto" label="" onchange={handleB2B_lastupdatedto}></lightning-input>
                                </span>
                            </div>
                        </lightning-tab>
                     
                        <lightning-tab label="Location" value="nine">
                            <lightning-radio-group name="locationtypeGroup"
                                label="Choose which address to search"
                                options={locationtypeoptions}
                                value={locationtypevalue}
                                onchange= {locationtypechange} 
                                type="button">
                            </lightning-radio-group>
                            <div style="margin-top:10px">
                                <span style="display: inline-block;margin-right:10px">
                                    <lightning-input style="width:400px" type="text" field-name="B2B_searchaddress" label="Enter a postcode" onchange={handleB2B_searchaddress}></lightning-input>
                                </span>
                            </div>
                            <div style="margin-top:10px">
                                <span style="display: inline-block">
                                    <lightning-input style="width:400px" type="number" field-name="B2B_miles" label="Radius" onchange={handleB2B_miles}></lightning-input>
                                </span>
                            </div>
                        </lightning-tab>

                        <lightning-tab label="Debenture/Mortgages" value="seven">
                            <div style="margin-top:10px">
                                <span style="display: inline-block;margin-right:10px">
                                    <lightning-input 
                                        style="width:400px" 
                                        placeholder="From" 
                                        type="date" 
                                        field-name="B2B_mortgagefrom" 
                                        label="Mortgage Creation Date" 
                                        onchange={handleB2B_mortgagedatefrom}>
                                    </lightning-input>
                                </span>
                                <span style="display: inline-block">
                                    <lightning-input 
                                    style="width:400px" 
                                    placeholder="To" 
                                    type="date" 
                                    field-name="B2B_mortgagedateto" 
                                    label="" 
                                    onchange={handleB2B_mortgagedateto}></lightning-input>
                                </span>
                            </div>
                            <lightning-input style="width:500px;margin-bottom:10px" 
                                type        = "text" 
                                label       = "Mortgage Holder" 
                                value       = {B2B_mortgageholdervalue} 
                                field-name  = "B2B_mortgageholder" 
                                placeholder = "filter by mortgage holder (i.e. Barclays, Santander)" 
                                onchange    = {handleB2B_mortgageholder}>
                            </lightning-input>      
                            <lightning-combobox
                                name        = "mortgagestatus"
                                label       = "Mortgage Status"
                                value       = {mortgagestatusvalue}
                                placeholder = "Select Type"
                                options     = {mortgagestatusoptions}
                                onchange    = {handlemortgagestatuschange} 
                                style       = "width:350px;margin-bottom:10px">
                            </lightning-combobox>
                        </lightning-tab>
                    </lightning-tabset>
                    <p style="font-weight:700;margin-bottom:10px;margin-top:10px;">{searchcriteriastatus}</p>
                </div>
                <footer class="slds-modal__footer">
                    <div class="float-left">
                        <lightning-button variant="brand" label="Search for Leads" title="Search for Leads" onclick={B2B_searchforleads} class="slds-m-left_x-small"></lightning-button>
                        <lightning-button variant="brand" label="Close" title="Close" onclick={B2BcloseModal} class="slds-m-left_x-small"></lightning-button>
                    </div>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>

    <template if:true={ResultsB2BModal}>
        <section role="dialog" tabindex="-1" class=" slds-modal slds-fade-in-open slds-modal_large" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1">
            <div class="slds-modal__container">
                <header class="slds-modal__header newrfa">
                    <lightning-button-icon 
                    icon-name        = "utility:close" 
                    variant          = "border-filled"
                    class            = "slds-float_right" 
                    onclick          = {ResultsB2BModalClose} 
                    alternative-text = "close">
                    </lightning-button-icon>
                    <h4 id="modal-heading-01" class="slds-modal__title slds-hyphenate">Search Results</h4>
                    <div><p class="newrfa_text" style="margin-top:0px"><span style='float:left;'><strong>B2B Leads</strong> | Subscription <strong>{substatus}</strong> | Version <strong>{appversion}</strong></span><span style='float:right;'>Credits <strong>{credits}</strong> of <strong>{creditlimit}</strong></span></p></div>
                </header>
                <div class="slds-modal__content slds-p-around_medium" id="modal-content-id-1233">
                    <p style="font-weight:700;text-align:center;margin-bottom:10px;margin-top:10px;">{searchstatus2}</p>
                    <div class={LookupDiv2} id="LookupDiv2" style="height: 500px;">
                        <div id="companydiv2" class={companydiv2}>
                            <lightning-datatable
                                key-field       = "id"
                                data            = {companydata2}
                                columns         = {B2BSearchTableColumns}
                                onrowselection  = {handleRowSelection1}>
                            </lightning-datatable>
                        </div>    
                    </div>    
                </div>
                <footer class="slds-modal__footer">
                    <div class="float-left">
                        <lightning-button variant="brand" disabled={B2B_generateleadsdisabledcon} label="Generate Leads with Contacts" title="Generate Leads with Contacts" onclick={B2B_GenerateLeadscon} class="slds-m-left_x-small"></lightning-button>
                        <lightning-button variant="brand" disabled={B2B_generateleadsdisabled} label="Generate Leads" title="Generate Leads" onclick={B2B_GenerateLeads} class="slds-m-left_x-small"></lightning-button>
                        <lightning-button variant="brand" label="Close" title="Close" onclick={ResultsB2BModalClose} class="slds-m-left_x-small"></lightning-button>
                    </div>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>

    <template if:true={fieldmapping_modal}>
        <section role="dialog" tabindex="-1" class=" slds-modal slds-fade-in-open slds-modal_large" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1">
            <div class="slds-modal__container">
                <header class="slds-modal__header newrfa">
                    <lightning-button-icon 
                        icon-name        = "utility:close" 
                        variant          = "border-filled"
                        class            = "slds-float_right" 
                        onclick          = {fm_close} 
                        alternative-text = "close">
                    </lightning-button-icon>
                    <div class="slds-clearfix">
                        <div class="slds-float_left">
                            <img 
                            src    = {imageURL} 
                            alt    = "RFA Logo" 
                            height = "38px" 
                            width  = "193px"
                            style  = "float:left;margin-bottom:10px">
                        </div>
                    </div>
                    <div><p class="newrfa_text" style="margin-top:0px"><span style='float:left;'>Map your Salesforce<strong> {fieldmapping_modaltitle_module}</strong> fields ( <strong>{fieldmapping_modaltitle_mapped}</strong> of <strong>{fieldmapping_modaltitle_fields}</strong> )</span></p></div>
                </header>
                <div class="slds-modal__content slds-p-around_medium" id="fm_modal-content-id">
                    <div class={LookupDiv2} id="LookupDiv2x" style="height: 500px;">
                        <lightning-combobox
                            name        = "fmtype"
                            label       = "Choose Module"
                            value       = {fmtypevalue}
                            placeholder = "Select Type"
                            options     = {fmtypeoptions}
                            onchange    = {handlefmtypechange} 
                            style       = "width:350px;margin-bottom:10px">
                        </lightning-combobox>
                        <div id="companydiv2x" class={companydiv2x}>
                            <lightning-datatable
                                key-field       = "id"
                                data            = {fm_data_arr}
                                columns         = {fm_tablecolumns}
                                max-row-selection = "1"
                                onrowselection  = {fm_handleRowSelection}>
                            </lightning-datatable>
                        </div>   
                    </div>    
                </div>
                <footer class="slds-modal__footer">
                    <div class="float-left">
                        <lightning-button variant="brand" style="float:left" label="Update field" title="Update field" onclick={fm_updatefield} class="slds-m-left_x-small"></lightning-button>
                        <lightning-button variant="brand" style="float:left" label="Close" title="Close" onclick={fm_close} class="slds-m-left_x-small"></lightning-button>
                    </div>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>

    <template if:true={fieldmappingsave_modal}>
        <section role="dialog" tabindex="-1" class=" slds-modal slds-fade-in-open" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1">
            <div class="slds-modal__container">
                <header class="slds-modal__header fm_modal">
                    <lightning-button-icon 
                        icon-name        = "utility:close" 
                        variant          = "border-filled"
                        class            = "slds-float_right" 
                        onclick          = {fmsave_close_modal} 
                        alternative-text = "close">
                    </lightning-button-icon>
                    <p id="fieldmapping-02"><strong>{fm_title}</strong></p>
                </header>
                <div class="slds-modal__content slds-p-around_medium fm_modal" id="fm_modal-content-id-12">
                    <div id="fmdiv" class="flex-container3 companydiv display fm_modal">
                        <div class="row">
                            <div class="fm_column1"><p>Field Description</p></div>
                            <div class="column2"><p class="format_data">{fm_field_description}</p></div>
                        </div>
                        <div class="row">
                            <div class="fm_column1"><p>Red Flag Alert Resource</p></div>
                            <div class="column2"><p class="format_data">{fm_resource}</p></div>
                        </div>
                        <div class="row">
                            <div class="fm_column1"><p>Field Type</p></div>
                            <div class="column2"><p class="format_data">{fm_field_type}</p></div>
                        </div>
                        <div class="row">
                            <div class="fm_column1"><p>Current Salesforce Field Name</p></div>
                            <div class="column2"><p class="format_data">{fm_current_field_name}</p></div>
                        </div>
                        <div class="row">
                            <div class="fm_column1"><p>Current Salesforce Field API Name</p></div>
                            <div class="column2"><p class="format_data">{fm_current_field_api_name}</p></div>
                        </div>
                        <div class={sf_field_style}>
                            <lightning-combobox
                                name        = "sf_field"
                                label       = "Choose Salesforce Account API Field"
                                value       = {sf_fieldvalue}
                                placeholder = "Salesforce field name"
                                options     = {sf_fieldoptions}
                                onchange    = {handle_sf_fieldchange} 
                                style       = "width:350px;margin-bottom:200px" >
                            </lightning-combobox>
                        </div>
                        <div class={sf_lead_field_style}>
                            <lightning-combobox
                                name        = "sf_lead_field"
                                label       = "Choose Salesforce Lead API Field"
                                value       = {sf_lead_fieldvalue}
                                placeholder = "Salesforce field name"
                                options     = {sf_lead_fieldoptions}
                                onchange    = {handle_sf_lead_fieldchange} 
                                style       = "width:350px;margin-bottom:200px" >
                            </lightning-combobox>
                        </div>
                        <span style="display: inline-block">
                            <lightning-radio-group name="radioGroup6"
                                label    = "Status"
                                options  = {excludetpsgoptions}
                                value    = {fm_status}
                                onchange = {fm_statuschange} 
                                type     = "button">
                            </lightning-radio-group>
                        </span>
                    </div>
                </div>
                <footer class="slds-modal__footer fm_modal">
                    <div class="float-left">
                        <lightning-button variant="brand" label="Save"   title="Save"   onclick={fmsave_save_modal} class="slds-m-left_x-small"></lightning-button>
                        <lightning-button variant="brand" label="Cancel" title="Cancel" onclick={fmsave_close_modal} class="slds-m-left_x-small"></lightning-button>
                    </div>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>

    <template if:true={accountrefresh_modal}>
        <section role="dialog" tabindex="-1" class=" slds-modal slds-fade-in-open" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1">
            <div class="slds-modal__container">
                <header class="slds-modal__header newrfa">
                    <lightning-button-icon 
                        icon-name        = "utility:close" 
                        variant          = "border-filled"
                        class            = "slds-float_right" 
                        onclick          = {fm_close} 
                        alternative-text = "close">
                    </lightning-button-icon>
                    <div class="slds-clearfix">
                        <div class="slds-float_left">
                            <img 
                            src    = {imageURL} 
                            alt    = "RFA Logo" 
                            height = "38px" 
                            width  = "193px"
                            style  = "float:left;margin-bottom:10px">
                        </div>
                    </div>
                    <div><p class="newrfa_text" style="margin-top:0px"><span style='float:left;'>Refresh all account records on the last day of every month.</span></p></div>
                </header>
                <div class="slds-modal__content slds-p-around_medium">
                    <div class="row">
                        <div class="fm_column1"><p>What the Scheduler does?</p></div>
                        <div class="column2"><p class="format_data">Refresh all account records having company number with Red Flag Alert data. Telephone and Addresses will NOT be updated.</p></div>
                    </div>
                    <div class="row">
                        <div class="fm_column1"><p>Accounts Refresh Scheduler</p></div>
                        <div class="column2"><p class="format_data">{mr_status}</p></div>
                    </div>
                    <br>
                    <br>
                    <div class="row">
                        <div class="fm_column1"><p>Switch on scheduler</p></div>
                        <div class="column2">
                            <lightning-button variant="brand" style="float:left" label="Start" title="Switch on scheduler." onclick={mr_start_button} class="slds-m-left_x-small"></lightning-button>   
                        </div>
                    </div>
                    <div class="row">
                        <div class="fm_column1"><p>Switch off scheduler</p></div>
                        <div class="column2">
                            <lightning-button variant="brand" style="float:left" label="Stop" title="Switch off scheduler." onclick={mr_stop_button} class="slds-m-left_x-small"></lightning-button>
                        </div>
                    </div>
                    <div class="row">
                        <div class="fm_column1"><p>Refresh all Accounts manually</p></div>
                        <div class="column2">
                            <lightning-button variant="brand" style="float:left" label="Run" title="Refresh all Account records with Red Flag Alert data." onclick={mr_now_button} class="slds-m-left_x-small"></lightning-button>
                        </div>
                    </div>
                    <div class="slds-border_bottom rfaspace"></div>
                    <div class="row">
                        <div class="fm_column1"><p>Gazette Notifications</p></div>
                        <div class="column2"><p class="format_data">If any of your account records receive a gazette notice, a task will be created.</p></div>
                    </div>
                    <div class="row">
                        <div class="fm_column1"><p>Gazette notifications</p></div>
                        <div class="column2"><p class="format_data">{mr_gazette_status}</p></div>
                    </div>
                    <br>
                    <br>
                    <div class="row">
                        <div class="fm_column1"><p>Switch on Gazette notifications</p></div>
                        <div class="column2">
                            <lightning-button variant="brand" style="float:left" label="Start" title="Switch on scheduler." onclick={mr_gazette_start_button} class="slds-m-left_x-small"></lightning-button>   
                        </div>
                    </div>
                    <div class="row">
                        <div class="fm_column1"><p>Switch off Gazette notifications</p></div>
                        <div class="column2">
                            <lightning-button variant="brand" style="float:left" label="Stop" title="Switch off scheduler." onclick={mr_gazette_stop_button} class="slds-m-left_x-small"></lightning-button>
                        </div>
                    </div>
                </div>
                <footer class="slds-modal__footer">
                    <div class="float-left">
                        <lightning-button variant="brand" style="float:left" label="Close" title="Close" onclick={fm_close} class="slds-m-left_x-small"></lightning-button>
                    </div>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>
</template>