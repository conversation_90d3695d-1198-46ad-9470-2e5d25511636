function GetCoordinates(ip_address) 
{ 
    return new Promise(resolve => { 
        var request = new XMLHttpRequest();
        request.open('GET', "https://europe-west1-redflag-live.cloudfunctions.net/SF_GetCoordinates?address=" + ip_address, true);
        request.onload = function() { 
            resolve(JSON.parse(this.response)); 
        }
        request.send(); 
    }); 
}
/****************************************************************************************************************************************************************/

function RFAGetCompanyData(ip_companynumber, ip_baseurl) 
{
    console.log("📘: RFAGetCompanyData", ip_companynumber);
    return new Promise(resolve => {
        var url = "https://europe-west1-redflag-live.cloudfunctions.net/SF_GetRFACompany?companynumber=" + ip_companynumber + "&baseurl=" + ip_baseurl;
        var request = new XMLHttpRequest();
        request.open('GET', url, true);
        request.onload = function() {
            var CompanyData = {};
			if (this.response == "NODATA") CompanyData.Status = "NODATA";
			else {
	            var Data = JSON.parse(this.response);
                console.log("SF_GetRFACompany", Data);
                resolve(Data);
            }
        }
        request.send(); 
    });
}
/****************************************************************************************************************************************************************/

function RFAGetCompanyDataDirectors(ip_companynumber, ip_baseurl) 
{
    console.log("📘: RFAGetCompanyDataDirectors", ip_companynumber);
    return new Promise(resolve => {
        var url = "https://europe-west1-redflag-live.cloudfunctions.net/sf_getcompanydirectors?companynumber=" + ip_companynumber + "&baseurl=" + ip_baseurl;
        var request = new XMLHttpRequest();
        request.open('GET', url, true);
        request.onload = function() {
			if (this.response == "NODATA") Data = null;
			else var Data = JSON.parse(this.response);
            resolve(Data);
            return;
        }
        request.send(); 
    });
}
/****************************************************************************************************************************************************************/

function GetUserDetailsFromFireBase(UserDetails) 
{
    console.log("📘: FIREBASE : GetUserDetailsFromFireBase");
    return new Promise(resolve => {
        var url = "https://europe-west1-redflag-live.cloudfunctions.net/SF_UserDetails?baseurl=" + UserDetails.baseurl + "&username=" + UserDetails.username + "&useremail=" + UserDetails.useremail + "&userapps=" + UserDetails.userapps;
        var request = new XMLHttpRequest();
        request.open('GET', url, true);
        request.onload = function() {
            // Returns NEWACCOUNT or EXISTINGACCOUNT or ERROR
            var data = JSON.parse(this.response);
            if (data.Status == "ERROR") console.log("📕: FAILED : Google Function: SF_UserDetails", data);
            else console.log("📗: SUCCESS : Google Function: SF_UserDetails", data.Status);
            resolve(data);
        }
        request.send();
    });
}
/****************************************************************************************************************************************************************/

function fm_getfields(ip_baseurl, ip_type, ip_namespace) 
{
    return new Promise(resolve => {
        var url = "https://europe-west1-redflag-live.cloudfunctions.net/SF_fm_getfields?baseurl=" + ip_baseurl + "&type=" + ip_type + "&namespace=" + ip_namespace;
        var request = new XMLHttpRequest();
        request.open('GET', url, true);
        request.onload = function() {
            var data = JSON.parse(this.response);
            if (data.status == "SUCCESS") {
                console.log("📗: SUCCESS : Google Function: SF_fm_getfields", ip_baseurl, ip_type, ip_namespace);
                resolve(data.data);    
            }
            else {
                console.log("📕: FAILED : Google Function: SF_fm_getfields", ip_baseurl, ip_type, ip_namespace);
                resolve("FAILED");    
            }
        }
        request.send();
    });
}
/****************************************************************************************************************************************************************/

function fb_updatefield(ip_key, ip_api_field_name, ip_field_name, ip_status) 
{
    return new Promise(resolve => {
        ip_api_field_name = ip_api_field_name.replace("redflagalert__", "");   
        var url = "https://europe-west1-redflag-live.cloudfunctions.net/SF_fm_updatefield?dbkey=" + ip_key + "&sf_field_api_name=" + ip_api_field_name + "&sf_field_name=" + ip_field_name + "&sf_status=" + ip_status;
        var request = new XMLHttpRequest();
        request.open('GET', url, true);
        request.onload = function() {
            console.log("📘: SF_fm_updatefield - this.response", this.response);
            resolve(this.response);
        }
        request.send();
    });
}
/****************************************************************************************************************************************************************/

function GetSICCodes() 
{
    console.log("📘: GetSICCodes");
    return new Promise(resolve => {
        var request = new XMLHttpRequest();
        request.open('GET', "https://europe-west1-redflag-live.cloudfunctions.net/SF_GetSICCodes", true);
        request.onload = function() {
            if (this.status == 200) {
                console.log("📗: SUCCESS : Google Function: SF_GetSICCodes");
                var siccodes = JSON.parse(this.response);
                resolve(siccodes);
            }
            else {
                console.log("📕: FAILED : Google Function: SF_GetSICCodes", this.status);
                resolve(null);
            }
        }
        request.send(); 
    });
}
/****************************************************************************************************************************************************************/

function firebase_update_accountrefresh(ip_key) 
{
    console.log("📘: firebase_update_accountrefresh", ip_key);
    var request = new XMLHttpRequest();
    request.open('GET', "https://europe-west1-redflag-live.cloudfunctions.net/sf_firebase_updateuserrefresh?key=" + ip_key, true);        
    request.onload = function() {
        if (this.status == 200) {
            console.log("📗: SUCCESS : Google Function: firebase_update_accountrefresh");
            return;
        }
        else {
            console.log("📕: FAILED : Google Function: firebase_update_accountrefresh", this.status);
            return;
        }
    }
    request.send(); 
  
}
/****************************************************************************************************************************************************************/

function gf_updatecredits(ip_key, ip_creditcounter) 
{
    console.log("📘: gf_updatecredits", ip_key, ip_creditcounter);
    return new Promise(resolve => {
        var request = new XMLHttpRequest();
        request.open('GET', "https://europe-west1-redflag-live.cloudfunctions.net/SF_UpdateUserDetails?key=" + ip_key + "&credits=" + ip_creditcounter, true);
        request.onload = function() { 
            if (this.status == 200) {
                console.log("📗: SUCCESS : Google Function: SF_UpdateUserDetails");    
                resolve("SUCCESS");
            }
            else resolve("FAILED");
        }
        request.send();
    });
}
/****************************************************************************************************************************************************************/

function gf_updateuserschedulerjobs(ip_key) 
{
    console.log("📘: gf_updateuserschedulerjobs", ip_key);
    var url = "https://europe-west1-redflag-live.cloudfunctions.net/SF_UpdateUserDetailsSchedulerJobs?key=" + ip_key
    var request = new XMLHttpRequest();
    request.open('GET', url, true);
    request.onload = function() { 
        if (this.status == 200) {
            console.log("📗: SUCCESS : Google Function: SF_UpdateUserDetailsSchedulerJobs", this.response);    
        }
        else console.log("📕: FAILED : Google Function: SF_UpdateUserDetailsSchedulerJobs", this.status);
    }
    request.send();            
}

export { GetCoordinates, RFAGetCompanyData, RFAGetCompanyDataDirectors, GetUserDetailsFromFireBase, fm_getfields, fb_updatefield, GetSICCodes, firebase_update_accountrefresh, gf_updatecredits, gf_updateuserschedulerjobs };