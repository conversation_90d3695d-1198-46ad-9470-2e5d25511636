<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Mobile_Msg_Notify_Contact_Owner_of_Text_Message_Received</name>
        <label>Mobile Msg- Notify Contact Owner of Text Message Received</label>
        <locationX>176</locationX>
        <locationY>431</locationY>
        <actionName>Mobile_Message__c.Mobile_Msg_Notify_Contact_Owner_of_Text_Message_Received</actionName>
        <actionType>emailAlert</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>SObjectRowId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Mobile_Message__c.Mobile_Msg_Notify_Contact_Owner_of_Text_Message_Received</nameSegment>
    </actionCalls>
    <apiVersion>62.0</apiVersion>
    <environments>Default</environments>
    <interviewLabel>Mobile Messages- Notify Contact Owner New {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Mobile Messages- Notify Contact Owner New</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Update_Contact_s_Email</name>
        <label>Update Contact&apos;s Email</label>
        <locationX>176</locationX>
        <locationY>323</locationY>
        <connector>
            <targetReference>Mobile_Msg_Notify_Contact_Owner_of_Text_Message_Received</targetReference>
        </connector>
        <inputAssignments>
            <field>Contact_s_Email__c</field>
            <value>
                <elementReference>$Record.Contact__r.Owner.Email</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Update_Contact_s_Email</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Received</stringValue>
            </value>
        </filters>
        <filters>
            <field>Contact__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <object>Mobile_Message__c</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
