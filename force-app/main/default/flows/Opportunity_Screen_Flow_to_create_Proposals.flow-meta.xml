<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <apiVersion>60.0</apiVersion>
    <description>Screen Flow to create Proposals from Loan Opportunities.</description>
    <dynamicChoiceSets>
        <name>LoanTerm</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Loan_term__c</picklistField>
        <picklistObject>Opportunity</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Type</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Type</picklistField>
        <picklistObject>Opportunity</picklistObject>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <formulas>
        <name>ClosedDate</name>
        <dataType>Date</dataType>
        <expression> ADDMONTHS( TODAY() ,1)</expression>
    </formulas>
    <formulas>
        <name>Name</name>
        <dataType>String</dataType>
        <expression>{!Lender.recordName} + &apos; £&apos;+  Text({!Proposed_Amount})</expression>
    </formulas>
    <interviewLabel>Opportunity: Screen Flow to create Proposals {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Opportunity: Screen Flow to create Proposals</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Create_Proposal_Record</name>
        <label>Create Proposal Record</label>
        <locationX>176</locationX>
        <locationY>350</locationY>
        <inputAssignments>
            <field>AccountId</field>
            <value>
                <elementReference>Get_Loan_Opportunity.AccountId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>CloseDate</field>
            <value>
                <elementReference>ClosedDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Lender__c</field>
            <value>
                <elementReference>Lender.recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Loan_term__c</field>
            <value>
                <elementReference>Loan_Term</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Name</field>
            <value>
                <elementReference>Name</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Opportunity_Proposals__c</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Proposed_Amount__c</field>
            <value>
                <elementReference>Proposed_Amount</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <stringValue>012P3000000g5ZZIAY</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>StageName</field>
            <value>
                <stringValue>Proposed to Lender</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Type</field>
            <value>
                <elementReference>Agreement_Type</elementReference>
            </value>
        </inputAssignments>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Get_Loan_Opportunity</name>
        <label>Get Loan Opportunity</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>New_proposal</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <screens>
        <name>New_proposal</name>
        <label>New proposal</label>
        <locationX>176</locationX>
        <locationY>242</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Create_Proposal_Record</targetReference>
        </connector>
        <fields>
            <name>Title</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;Create New Proposal&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Lender</name>
            <extensionName>flowruntime:lookup</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>fieldApiName</name>
                <value>
                    <stringValue>Account_Lenders__c</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Lender</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>objectApiName</name>
                <value>
                    <stringValue>Opportunity</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>required</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>Proposed_Amount</name>
            <dataType>Currency</dataType>
            <fieldText>Proposed Amount</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
            <scale>0</scale>
        </fields>
        <fields>
            <name>Agreement_Type</name>
            <choiceReferences>Type</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Agreement Type</fieldText>
            <fieldType>DropdownBox</fieldType>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>Loan_Term</name>
            <choiceReferences>LoanTerm</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Loan Term</fieldText>
            <fieldType>DropdownBox</fieldType>
            <isRequired>false</isRequired>
        </fields>
        <nextOrFinishButtonLabel>Create Proposal</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Loan_Opportunity</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
</Flow>
