<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <decisions>
        <name>Is_the_Task_related_to_a_Lead</name>
        <label>Is the Task related to a Lead?</label>
        <locationX>281</locationX>
        <locationY>431</locationY>
        <defaultConnector>
            <targetReference>Get_Related_Account</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Related_Lead</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Last_Task_Activity_Date_on_related_Task</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_the_Task_related_to_an_Account</name>
        <label>Is the Task related to an Account?</label>
        <locationX>512</locationX>
        <locationY>647</locationY>
        <defaultConnector>
            <targetReference>Get_Related_Opportunity</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yesss</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Related_Account</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Last_Task_Activity_Date_on_related_Account</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_the_Task_related_to_an_Opportunity</name>
        <label>Is the Task related to an Opportunity?</label>
        <locationX>710</locationX>
        <locationY>863</locationY>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yesssss</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Related_Opportunity</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Last_Task_Activity_Date_on_related_Opportunity</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>Task: Update Last Task Activity Date on related object {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Task: Update Last Task Activity Date on related object</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Related_Account</name>
        <label>Get Related Account</label>
        <locationX>512</locationX>
        <locationY>539</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_the_Task_related_to_an_Account</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WhatId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Related_Lead</name>
        <label>Get Related Lead</label>
        <locationX>281</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_the_Task_related_to_a_Lead</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WhoId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Lead</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Related_Opportunity</name>
        <label>Get Related Opportunity</label>
        <locationX>710</locationX>
        <locationY>755</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_the_Task_related_to_an_Opportunity</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WhatId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Last_Task_Activity_Date_on_related_Account</name>
        <label>Update Last Task Activity Date on related Account</label>
        <locationX>314</locationX>
        <locationY>755</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Related_Account.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Last_Task_Activity_Date__c</field>
            <value>
                <elementReference>Get_Related_Account.LastActivityDate</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Last_Task_Activity_Date_on_related_Opportunity</name>
        <label>Update Last Task Activity Date on related Opportunity</label>
        <locationX>578</locationX>
        <locationY>971</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Related_Opportunity.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Last_Task_Activity_Date__c</field>
            <value>
                <elementReference>Get_Related_Opportunity.LastActivityDate</elementReference>
            </value>
        </inputAssignments>
        <object>Opportunity</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Last_Task_Activity_Date_on_related_Task</name>
        <label>Update Last Task Activity Date on related Task</label>
        <locationX>50</locationX>
        <locationY>539</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Related_Lead.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Last_Task_Activity_Date__c</field>
            <value>
                <elementReference>Get_Related_Lead.LastActivityDate</elementReference>
            </value>
        </inputAssignments>
        <object>Lead</object>
    </recordUpdates>
    <start>
        <locationX>155</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Related_Lead</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Pardot_Email__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <object>Task</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
