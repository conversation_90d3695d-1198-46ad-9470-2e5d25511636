<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <decisions>
        <name>Stage_changes_to</name>
        <label>Stage changes to??</label>
        <locationX>446</locationX>
        <locationY>323</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Closed_Won</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.StageName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Closed Won</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Closed_Date</targetReference>
            </connector>
            <label>Closed Won</label>
        </rules>
        <rules>
            <name>Proposal_Approved</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.StageName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Proposal Approved</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Approval_Date__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Approval_Date</targetReference>
            </connector>
            <label>Proposal Approved</label>
        </rules>
        <rules>
            <name>Closed_Lost_Funded_Proposal_Declined</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.StageName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Closed Lost</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.StageName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Proposal Declined</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.StageName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Funded</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_2_of_Update_Closed_Date</targetReference>
            </connector>
            <label>Closed Lost, Funded, Proposal Declined</label>
        </rules>
    </decisions>
    <description>Update Account.Type to Customer when an opportunity is closed won and update Closed Dates on Opportunity.</description>
    <environments>Default</environments>
    <formulas>
        <name>Today</name>
        <dataType>Date</dataType>
        <expression>Today()</expression>
    </formulas>
    <interviewLabel>Opportunity- Update Account Type when Closed Won {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Opportunity- Update Account Type and Dates when Closed Won</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Copy_2_of_Update_Closed_Date</name>
        <label>Update Closed Date</label>
        <locationX>578</locationX>
        <locationY>431</locationY>
        <inputAssignments>
            <field>CloseDate</field>
            <value>
                <elementReference>Today</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Account_Type</name>
        <label>Update Account Type</label>
        <locationX>50</locationX>
        <locationY>539</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Account.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Type</field>
            <value>
                <stringValue>Customer</stringValue>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Approval_Date</name>
        <label>Update Approval Date</label>
        <locationX>314</locationX>
        <locationY>431</locationY>
        <inputAssignments>
            <field>Approval_Date__c</field>
            <value>
                <elementReference>Today</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Closed_Date</name>
        <label>Update Closed Date</label>
        <locationX>50</locationX>
        <locationY>431</locationY>
        <connector>
            <targetReference>Update_Account_Type</targetReference>
        </connector>
        <inputAssignments>
            <field>CloseDate</field>
            <value>
                <elementReference>Today</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>320</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Stage_changes_to</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>StageName</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <object>Opportunity</object>
        <recordTriggerType>Update</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
