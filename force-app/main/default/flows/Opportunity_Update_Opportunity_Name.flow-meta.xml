<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <decisions>
        <name>Record_Type</name>
        <label>Record Type?</label>
        <locationX>182</locationX>
        <locationY>323</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Proposals</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Proposals</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Name</targetReference>
            </connector>
            <label>Proposals</label>
        </rules>
    </decisions>
    <description>Update Opportunity Name when Proposed Amount is updated.</description>
    <environments>Default</environments>
    <formulas>
        <name>Name</name>
        <dataType>String</dataType>
        <expression>{!$Record.Lender__r.Name} + &apos; £&apos;+ Text({!$Record.Proposed_Amount__c})</expression>
    </formulas>
    <interviewLabel>Opportunity: Update Opportunity Name {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Opportunity: Update Opportunity Name</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Update_Name</name>
        <label>Update Name</label>
        <locationX>50</locationX>
        <locationY>431</locationY>
        <inputAssignments>
            <field>Name</field>
            <value>
                <elementReference>Name</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Record_Type</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Proposed_Amount__c</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <object>Opportunity</object>
        <recordTriggerType>Update</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
