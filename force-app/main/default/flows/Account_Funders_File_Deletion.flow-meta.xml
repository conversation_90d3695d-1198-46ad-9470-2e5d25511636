<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <assignments>
        <name>Add_Content_Document</name>
        <label>Add Content Document</label>
        <locationX>264</locationX>
        <locationY>350</locationY>
        <assignmentItems>
            <assignToReference>ContentDocumentIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_over_Content_Document_Links.ContentDocumentId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_over_Content_Document_Links</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Add_Delete_Ids</name>
        <label>Add Delete Ids</label>
        <locationX>264</locationX>
        <locationY>866</locationY>
        <assignmentItems>
            <assignToReference>DeleteContentDocuments</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_over_documents.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_over_documents</targetReference>
        </connector>
    </assignments>
    <environments>Default</environments>
    <interviewLabel>Account {!$Flow.CurrentDateTime}</interviewLabel>
    <isAdditionalPermissionRequiredToRun>true</isAdditionalPermissionRequiredToRun>
    <label>Account Funders File Deletion</label>
    <loops>
        <name>Loop_over_Content_Document_Links</name>
        <label>Loop over Content Document Links</label>
        <locationX>176</locationX>
        <locationY>242</locationY>
        <collectionReference>Content_Document_Link_Records</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Add_Content_Document</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Content_Document_Records</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_over_documents</name>
        <label>Loop over documents</label>
        <locationX>176</locationX>
        <locationY>758</locationY>
        <collectionReference>ContentDocument.outputSelectedRows</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Add_Delete_Ids</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Delete_Content_Document_Records</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordDeletes>
        <name>Delete_Content_Document_Records</name>
        <label>Delete Content Document Records</label>
        <locationX>176</locationX>
        <locationY>1058</locationY>
        <connector>
            <targetReference>Screen2</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>In</operator>
            <value>
                <elementReference>DeleteContentDocuments</elementReference>
            </value>
        </filters>
        <object>ContentDocument</object>
    </recordDeletes>
    <recordLookups>
        <name>Content_Document_Link_Records</name>
        <label>Content Document Link Records</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_over_Content_Document_Links</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>LinkedEntityId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>ContentDocumentLink</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Content_Document_Records</name>
        <label>Content Document Records</label>
        <locationX>176</locationX>
        <locationY>542</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Display_Content_Document</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>In</operator>
            <value>
                <elementReference>ContentDocumentIds</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>ContentDocument</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <runInMode>SystemModeWithoutSharing</runInMode>
    <screens>
        <name>Display_Content_Document</name>
        <label>Display Content Document</label>
        <locationX>176</locationX>
        <locationY>650</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Loop_over_documents</targetReference>
        </connector>
        <fields>
            <name>Text2</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;&lt;u&gt;DELETE FILES&lt;/u&gt;&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>ContentDocument</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>ContentDocument</typeValue>
            </dataTypeMappings>
            <extensionName>c:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>objectName</name>
                <value>
                    <stringValue>ContentDocument</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Content_Document_Records</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>cb_suppressBottomBar</name>
                <value>
                    <stringValue>CB_FALSE</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>cb_navigateNextOnSave</name>
                <value>
                    <stringValue>CB_FALSE</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columnFields</name>
                <value>
                    <stringValue>Title,FileExtension,Description,PublishStatus</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>cb_matchCaseOnFilters</name>
                <value>
                    <stringValue>CB_FALSE</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columnWidths</name>
                <value>
                    <stringValue>Title:365, FileExtension:365, Description:365, PublishStatus:364</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>cb_isDisplayHeader</name>
                <value>
                    <stringValue>CB_FALSE</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <nextOrFinishButtonLabel>Delete Files</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Screen2</name>
        <label>Screen2</label>
        <locationX>176</locationX>
        <locationY>1166</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>Text</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;color: rgb(39, 150, 19); font-size: 14px;&quot;&gt;Records deleted Successfully&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Content_Document_Link_Records</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>ContentDocumentIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>DeleteContentDocuments</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
</Flow>
