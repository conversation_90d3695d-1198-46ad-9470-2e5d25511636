<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>56.0</apiVersion>
    <assignments>
        <name>List</name>
        <label>List</label>
        <locationX>264</locationX>
        <locationY>758</locationY>
        <assignmentItems>
            <assignToReference>ListofOL</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>OpportunityLender</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>LP_Record</name>
        <label>LP Record</label>
        <locationX>264</locationX>
        <locationY>638</locationY>
        <assignmentItems>
            <assignToReference>OpportunityLender.Opportunity__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityLender.Lender_Product__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>List</targetReference>
        </connector>
    </assignments>
    <environments>Default</environments>
    <formulas>
        <name>AggreementType</name>
        <dataType>String</dataType>
        <expression>Text({!Get_Current_Opportunity.Type})</expression>
    </formulas>
    <formulas>
        <name>LoanTerm</name>
        <dataType>Number</dataType>
        <expression>VALUE(Text({!Get_Current_Opportunity.Loan_term__c}))</expression>
        <scale>0</scale>
    </formulas>
    <interviewLabel>Select Financial Products based on Opportunity {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Select Financial Products based on Opportunity</label>
    <loops>
        <name>Loop</name>
        <label>Loop</label>
        <locationX>176</locationX>
        <locationY>518</locationY>
        <collectionReference>FP.outputSelectedRows</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>LP_Record</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Create_Opportunity_Lenders</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Create_Opportunity_Lenders</name>
        <label>Create Opportunity Lenders</label>
        <locationX>176</locationX>
        <locationY>974</locationY>
        <inputReference>ListofOL</inputReference>
    </recordCreates>
    <recordLookups>
        <name>Get_Current_Opportunity</name>
        <label>Get Current Opportunity</label>
        <locationX>176</locationX>
        <locationY>158</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Financial_Products</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Financial_Products</name>
        <label>Get Financial Products</label>
        <locationX>176</locationX>
        <locationY>278</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Screen_1</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Term_Min__c</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>LoanTerm</elementReference>
            </value>
        </filters>
        <filters>
            <field>FPT__c</field>
            <operator>Contains</operator>
            <value>
                <elementReference>AggreementType</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Lender_Product__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <screens>
        <name>Screen_1</name>
        <label>Screen 1</label>
        <locationX>176</locationX>
        <locationY>398</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Loop</targetReference>
        </connector>
        <fields>
            <name>Display</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;&lt;em&gt;&lt;u&gt;Select Financial Products&lt;/u&gt;&lt;/em&gt;&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>FP</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Lender_Product__c</typeValue>
            </dataTypeMappings>
            <extensionName>c:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>objectName</name>
                <value>
                    <stringValue>Lender_Product__c</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Get_Financial_Products</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>cb_suppressBottomBar</name>
                <value>
                    <stringValue>CB_FALSE</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>cb_navigateNextOnSave</name>
                <value>
                    <stringValue>CB_FALSE</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columnFields</name>
                <value>
                    <stringValue>Name,Term_Min__c,Funding_Type_s__c,Asset_s__c</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>cb_matchCaseOnFilters</name>
                <value>
                    <stringValue>CB_FALSE</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columnWidths</name>
                <value>
                    <stringValue>Name:215, Term_Min__c:110, Funding_Type_s__c:250, Asset_s__c:260</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columnAlignments</name>
                <value>
                    <stringValue>Term_Min__c:center</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columnWraps</name>
                <value>
                    <stringValue>Name:false, Term_Min__c:false, Asset_s__c:true, Funding_Type_s__c:true</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <nextOrFinishButtonLabel>Create Lender opportunity</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Current_Opportunity</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>ListofOL</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity_Lender__c</objectType>
    </variables>
    <variables>
        <name>OpportunityLender</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity_Lender__c</objectType>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
</Flow>
