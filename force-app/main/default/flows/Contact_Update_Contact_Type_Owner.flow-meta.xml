<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <description>Update Contact Type = Account Type, Contact Owner= Account Owner when a contact is created.</description>
    <environments>Default</environments>
    <interviewLabel>Contact- Update Contact Type &amp; Owner {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Contact- Update Contact Type &amp; Owner</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Update_Contact_Type</name>
        <label>Update Contact Type</label>
        <locationX>176</locationX>
        <locationY>323</locationY>
        <inputAssignments>
            <field>Contact_Type__c</field>
            <value>
                <elementReference>$Record.Account.Type</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>$Record.Account.OwnerId</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Update_Contact_Type</targetReference>
        </connector>
        <object>Contact</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
