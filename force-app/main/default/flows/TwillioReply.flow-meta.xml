<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>62.0</apiVersion>
    <environments>Default</environments>
    <interviewLabel>TwillioReply {!$Flow.CurrentDateTime}</interviewLabel>
    <label>TwillioReply</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Create_Mobile_Message_Record</name>
        <label>Create Mobile Message Record</label>
        <locationX>176</locationX>
        <locationY>350</locationY>
        <assignRecordIdToReference>NewMobileMessageId</assignRecordIdToReference>
        <connector>
            <targetReference>NavigationtoMobileMessageRecord</targetReference>
        </connector>
        <inputAssignments>
            <field>From_Phone_Number__c</field>
            <value>
                <elementReference>From_Phone_Number</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Message__c</field>
            <value>
                <elementReference>Message</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <elementReference>Status</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>To_Phone_Number__c</field>
            <value>
                <elementReference>To_Phone_Number</elementReference>
            </value>
        </inputAssignments>
        <object>Mobile_Message__c</object>
    </recordCreates>
    <recordLookups>
        <name>GetMobileMessageRecords</name>
        <label>GetMobileMessageRecords</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>DisplayMobileMessageRecord</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Mobile_Message__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <screens>
        <name>DisplayMobileMessageRecord</name>
        <label>Display Mobile Message Record</label>
        <locationX>176</locationX>
        <locationY>242</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Create_Mobile_Message_Record</targetReference>
        </connector>
        <fields>
            <name>From_Phone_Number</name>
            <dataType>String</dataType>
            <defaultValue>
                <elementReference>GetMobileMessageRecords.To_Phone_Number__c</elementReference>
            </defaultValue>
            <fieldText>From Phone Number</fieldText>
            <fieldType>InputField</fieldType>
            <isReadOnly>
                <booleanValue>true</booleanValue>
            </isReadOnly>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>To_Phone_Number</name>
            <dataType>String</dataType>
            <defaultValue>
                <elementReference>GetMobileMessageRecords.From_Phone_Number__c</elementReference>
            </defaultValue>
            <fieldText>To Phone Number</fieldText>
            <fieldType>InputField</fieldType>
            <isReadOnly>
                <booleanValue>true</booleanValue>
            </isReadOnly>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>Status</name>
            <dataType>String</dataType>
            <defaultValue>
                <elementReference>PendingValue</elementReference>
            </defaultValue>
            <fieldText>Status</fieldText>
            <fieldType>InputField</fieldType>
            <isReadOnly>
                <booleanValue>true</booleanValue>
            </isReadOnly>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>Message</name>
            <fieldText>Message</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>NavigationtoMobileMessageRecord</name>
        <label>Navigation to Mobile Message Record</label>
        <locationX>176</locationX>
        <locationY>458</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>Navigate</name>
            <extensionName>c:navigateToRecord</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>recordId</name>
                <value>
                    <elementReference>NewMobileMessageId</elementReference>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetMobileMessageRecords</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>NewMobileMessageId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>PendingValue</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>Pending</stringValue>
        </value>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
