<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Copy_2_of_Log_Progress</name>
        <label>Log Progress</label>
        <locationX>440</locationX>
        <locationY>492</locationY>
        <actionName>FlowLogEntry</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>To_Phone_Set</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>flowName</name>
            <value>
                <stringValue>Twilio_Messaging_Flow</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>loggingLevelName</name>
            <value>
                <stringValue>INFO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>message</name>
            <value>
                <stringValue>Setting Contact Business Phone as Phone Number</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>saveLog</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>FlowLogEntry</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Log_Failed_Send</name>
        <label>Log Failed Send</label>
        <locationX>440</locationX>
        <locationY>900</locationY>
        <actionName>FlowLogEntry</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Update_as_failed</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>faultMessage</name>
            <value>
                <elementReference>Send_SMS_Message.responseMessage</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>flowName</name>
            <value>
                <stringValue>Twilio_Messaging_Flow</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>loggingLevelName</name>
            <value>
                <stringValue>ERROR</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>message</name>
            <value>
                <stringValue>Setting Contact Mobile Phone as Phone Number</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>saveLog</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>FlowLogEntry</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Log_Failed_Send_no_phone_number</name>
        <label>Log Failed Send no phone number</label>
        <locationX>704</locationX>
        <locationY>900</locationY>
        <actionName>FlowLogEntry</actionName>
        <actionType>apex</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>flowName</name>
            <value>
                <stringValue>Twilio_Messaging_Flow</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>loggingLevelName</name>
            <value>
                <stringValue>ERROR</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>message</name>
            <value>
                <stringValue>Unable to send SMS. No Phone Set</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>saveLog</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>FlowLogEntry</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Log_Progress</name>
        <label>Log Progress</label>
        <locationX>176</locationX>
        <locationY>492</locationY>
        <actionName>FlowLogEntry</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>To_Phone_Set</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>flowName</name>
            <value>
                <stringValue>Twilio_Messaging_Flow</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>loggingLevelName</name>
            <value>
                <stringValue>INFO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>message</name>
            <value>
                <stringValue>Setting Contact Mobile Phone as Phone Number</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>saveLog</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>FlowLogEntry</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Log_Success_Send</name>
        <label>Log Success Send</label>
        <locationX>176</locationX>
        <locationY>900</locationY>
        <actionName>FlowLogEntry</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Update_as_sent</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>faultMessage</name>
            <value>
                <elementReference>Send_SMS_Message.responseMessage</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>flowName</name>
            <value>
                <stringValue>Twilio_Messaging_Flow</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>loggingLevelName</name>
            <value>
                <stringValue>INFO</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>message</name>
            <value>
                <stringValue>SMS Message Sent</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>saveLog</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>FlowLogEntry</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Send_SMS_Message</name>
        <label>Send SMS Message</label>
        <locationX>176</locationX>
        <locationY>792</locationY>
        <actionName>TwilioHelper</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Log_Success_Send</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Log_Failed_Send</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>fromPhoneNumber</name>
            <value>
                <elementReference>FromPhoneNumber</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>messageBody</name>
            <value>
                <elementReference>$Record.Message__c</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>toPhoneNumber</name>
            <value>
                <elementReference>To_Phone_Number</elementReference>
            </value>
        </inputParameters>
        <nameSegment>TwilioHelper</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>62.0</apiVersion>
    <assignments>
        <name>Set_Contact_Business_Phone_as_Phone</name>
        <label>Set Contact Business Phone as Phone</label>
        <locationX>440</locationX>
        <locationY>384</locationY>
        <assignmentItems>
            <assignToReference>To_Phone_Number</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.Contact__r.Phone</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Copy_2_of_Log_Progress</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_Contact_Phone_as_Phone</name>
        <label>Set Contact Mobile Phone as Phone</label>
        <locationX>176</locationX>
        <locationY>384</locationY>
        <assignmentItems>
            <assignToReference>To_Phone_Number</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.Contact__r.MobilePhone</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Log_Progress</targetReference>
        </connector>
    </assignments>
    <decisions>
        <description>Checks to see if the text field of To_Phone has been set on the record. If it has it uses that otherwise it checks the contact</description>
        <name>Phone_number_set</name>
        <label>Phone number set?</label>
        <locationX>440</locationX>
        <locationY>276</locationY>
        <defaultConnector>
            <targetReference>To_Phone_Set</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>No_Phone_Set_Use_Contact_Mobile</name>
            <conditionLogic>1 AND 2 AND (3 OR 4 OR 5)</conditionLogic>
            <conditions>
                <leftValueReference>To_Phone_Number</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Contact__r.MobilePhone</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Contact__r.MobilePhone</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>07</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Contact__r.MobilePhone</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>+447</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Contact__r.MobilePhone</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>+4407</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Contact_Phone_as_Phone</targetReference>
            </connector>
            <label>No Phone Use Contact Mobile</label>
        </rules>
        <rules>
            <name>No_Phone_Use_Contact_Business_Phone</name>
            <conditionLogic>1 AND 2 AND (3 OR 4 OR 5)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Contact__r.Phone</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>To_Phone_Number</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Contact__r.Phone</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>07</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Contact__r.Phone</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>+447</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Contact__r.Phone</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>+4407</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Contact_Business_Phone_as_Phone</targetReference>
            </connector>
            <label>No Phone Use Contact Business Phone</label>
        </rules>
    </decisions>
    <decisions>
        <name>To_Phone_Set</name>
        <label>To Phone Set?</label>
        <locationX>440</locationX>
        <locationY>684</locationY>
        <defaultConnector>
            <targetReference>Failed_to_Send</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Is_Set</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>To_Phone_Number</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Send_SMS_Message</targetReference>
            </connector>
            <label>Is Set</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>FromPhoneNumber</name>
        <dataType>String</dataType>
        <expression>Text({!$Record.From_Phone_Number__c})</expression>
    </formulas>
    <interviewLabel>Twilio Messaging Flow {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Twilio Messaging Flow</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Failed_to_Send</name>
        <label>Failed to Send</label>
        <locationX>704</locationX>
        <locationY>792</locationY>
        <connector>
            <targetReference>Log_Failed_Send_no_phone_number</targetReference>
        </connector>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Failed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>To_Phone_Number__c</field>
            <value>
                <elementReference>To_Phone_Number</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_as_failed</name>
        <label>Update as failed</label>
        <locationX>440</locationX>
        <locationY>1008</locationY>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Failed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>To_Phone_Number__c</field>
            <value>
                <elementReference>To_Phone_Number</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_as_sent</name>
        <label>Update as sent</label>
        <locationX>176</locationX>
        <locationY>1008</locationY>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Sent</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>To_Phone_Number__c</field>
            <value>
                <elementReference>To_Phone_Number</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Pending</stringValue>
            </value>
        </filters>
        <object>Mobile_Message__c</object>
        <recordTriggerType>Create</recordTriggerType>
        <scheduledPaths>
            <connector>
                <targetReference>Phone_number_set</targetReference>
            </connector>
            <pathType>AsyncAfterCommit</pathType>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Draft</status>
    <variables>
        <name>To_Phone_Number</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <elementReference>$Record.To_Phone_Number__c</elementReference>
        </value>
    </variables>
</Flow>
