<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Send_Email_To_Leads</name>
        <label>Send Email To Leads</label>
        <locationX>50</locationX>
        <locationY>971</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>senderType</name>
            <value>
                <stringValue>OrgWideEmailAddress</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderAddress</name>
            <value>
                <stringValue><EMAIL></stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientId</name>
            <value>
                <stringValue>{!$Record.Id}</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>logEmailOnSend</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailTemplateId</name>
            <value>
                <elementReference>Template_Id.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
    </actionCalls>
    <apiVersion>62.0</apiVersion>
    <decisions>
        <name>Check_Leads</name>
        <label>Leads/Contacts Check</label>
        <locationX>182</locationX>
        <locationY>755</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>No_Duplicate_Records</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_All_Leads</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_All_Contacts</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Template_Id</targetReference>
            </connector>
            <label>No Duplicate Records</label>
        </rules>
    </decisions>
    <decisions>
        <name>Owner</name>
        <label>Owner</label>
        <locationX>380</locationX>
        <locationY>323</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>ALIAS</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Owner:User.Alias</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>$Label.BSMS_ALIAS</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Lead_Data</targetReference>
            </connector>
            <label>ALIAS</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>BSME {!$Flow.CurrentDateTime}</interviewLabel>
    <label>BSME Email Notification</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_All_Contacts</name>
        <label>Get All Contacts</label>
        <locationX>182</locationX>
        <locationY>647</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Leads</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Email</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Email</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Contact</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_All_Leads</name>
        <label>Get All Leads</label>
        <locationX>182</locationX>
        <locationY>539</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_All_Contacts</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Email</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Email</elementReference>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>NotEqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Lead</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Template_Id</name>
        <label>Get BSME Template Id</label>
        <locationX>50</locationX>
        <locationY>863</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Send_Email_To_Leads</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>BSME Lead Generic Email Response</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>EmailTemplate</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Lead_Data</name>
        <label>Update Lead Data</label>
        <locationX>182</locationX>
        <locationY>431</locationY>
        <connector>
            <targetReference>Get_All_Leads</targetReference>
        </connector>
        <inputAssignments>
            <field>LeadSource</field>
            <value>
                <stringValue>Email Enquiry</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Lead_Source__c</field>
            <value>
                <stringValue>Email Enquiry</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Marketing_Source__c</field>
            <value>
                <stringValue>Business Money</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>254</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Owner</targetReference>
        </connector>
        <object>Lead</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
