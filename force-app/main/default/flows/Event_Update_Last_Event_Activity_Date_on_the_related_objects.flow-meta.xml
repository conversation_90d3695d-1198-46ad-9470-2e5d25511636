<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <decisions>
        <name>Is_the_Event_related_to_a_Lead</name>
        <label>Is the Event related to a Lead?</label>
        <locationX>281</locationX>
        <locationY>431</locationY>
        <defaultConnector>
            <targetReference>Get_Related_Account</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Related_Lead</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Latest_Event_from_Lead</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_the_Event_related_to_Opportunity</name>
        <label>Is the Event related to Opportunity?</label>
        <locationX>710</locationX>
        <locationY>863</locationY>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yesssss</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Related_Opportunity</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Latest_Event_from_Opportunity</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_the_Event_related_to_the_Account</name>
        <label>Is the Event related to the Account?</label>
        <locationX>512</locationX>
        <locationY>647</locationY>
        <defaultConnector>
            <targetReference>Get_Related_Opportunity</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yesss</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Related_Account</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Latest_Event_from_Account</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>Event: Update {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Event: Update  Last Event Activity Date on the related objects</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Latest_Event_from_Account</name>
        <label>Get Latest Event from Account</label>
        <locationX>314</locationX>
        <locationY>755</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Update_Last_Event_Activity_Date_on_related_Account</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>WhatId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Related_Account.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Event</object>
        <sortField>StartDateTime</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Latest_Event_from_Lead</name>
        <label>Get Latest Event from Lead</label>
        <locationX>50</locationX>
        <locationY>539</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Update_Last_Event_Activity_Date_on_related_Lead</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>WhoId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Related_Lead.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Event</object>
        <sortField>StartDateTime</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Latest_Event_from_Opportunity</name>
        <label>Get Latest Event from Opportunity</label>
        <locationX>578</locationX>
        <locationY>971</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Update_Last_Event_Activity_Date_on_related_Opportunity</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>WhatId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Related_Opportunity.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Event</object>
        <sortField>StartDateTime</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Related_Account</name>
        <label>Get Related Account</label>
        <locationX>512</locationX>
        <locationY>539</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_the_Event_related_to_the_Account</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WhatId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Related_Lead</name>
        <label>Get Related Lead</label>
        <locationX>281</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_the_Event_related_to_a_Lead</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WhoId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Lead</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Related_Opportunity</name>
        <label>Get Related Opportunity</label>
        <locationX>710</locationX>
        <locationY>755</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_the_Event_related_to_Opportunity</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WhatId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Last_Event_Activity_Date_on_related_Account</name>
        <label>Update Last Event Activity Date on related Account</label>
        <locationX>314</locationX>
        <locationY>863</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Related_Account.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Last_Event_Activity_Date__c</field>
            <value>
                <elementReference>Get_Latest_Event_from_Account.StartDateTime</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Last_Event_Activity_Date_on_related_Lead</name>
        <label>Update Last Event Activity Date on related Lead</label>
        <locationX>50</locationX>
        <locationY>647</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Related_Lead.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Last_Event_Activity_Date__c</field>
            <value>
                <elementReference>Get_Latest_Event_from_Lead.StartDateTime</elementReference>
            </value>
        </inputAssignments>
        <object>Lead</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Last_Event_Activity_Date_on_related_Opportunity</name>
        <label>Update Last Event Activity Date on related Opportunity</label>
        <locationX>578</locationX>
        <locationY>1079</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Related_Opportunity.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Last_Event_Activity_Date__c</field>
            <value>
                <elementReference>Get_Latest_Event_from_Opportunity.StartDateTime</elementReference>
            </value>
        </inputAssignments>
        <object>Opportunity</object>
    </recordUpdates>
    <start>
        <locationX>155</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Related_Lead</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Pardot_Email__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <object>Event</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
