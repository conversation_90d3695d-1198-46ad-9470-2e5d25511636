<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <actionCalls>
        <name>Log_Error_Create_Opportunity</name>
        <label>Log Error Create Opportunity</label>
        <locationX>842</locationX>
        <locationY>782</locationY>
        <actionName>Logger</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Display_Error2</targetReference>
        </connector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <nameSegment>Logger</nameSegment>
    </actionCalls>
    <actionCalls>
        <name>Log_Error_Only_Contact</name>
        <label>Log Error Only Contact</label>
        <locationX>1106</locationX>
        <locationY>674</locationY>
        <actionName>Logger</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Display_Error</targetReference>
        </connector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <nameSegment>Logger</nameSegment>
    </actionCalls>
    <actionCalls>
        <name>Log_Error_Only_Opportunity</name>
        <label>Log Error Only Opportunity</label>
        <locationX>1634</locationX>
        <locationY>674</locationY>
        <actionName>Logger</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Display_Error3</targetReference>
        </connector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <nameSegment>Logger</nameSegment>
    </actionCalls>
    <apiVersion>60.0</apiVersion>
    <decisions>
        <name>Create_Contact_Opportunity</name>
        <label>Create Contact/Opportunity</label>
        <locationX>1106</locationX>
        <locationY>458</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Error_Screen</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Opportunity_Supplier.recordId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>SurName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue></stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Select_Contact</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Error_NothingSelected</targetReference>
            </connector>
            <label>Error Screen</label>
        </rules>
        <rules>
            <name>Only_Supplier_Selected</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Opportunity_Supplier.recordId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>SurName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue></stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Select_Contact</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_Create_Opportunity</targetReference>
            </connector>
            <label>Only Supplier Selected</label>
        </rules>
        <rules>
            <name>Create_Contact</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>SurName</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue></stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Select_Contact</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Contact1</targetReference>
            </connector>
            <label>Create Contact only</label>
        </rules>
        <rules>
            <name>Create_Opportunity1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Select_Contact</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>SurName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue></stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Opp2</targetReference>
            </connector>
            <label>Create Opportunity only and selected contacts role</label>
        </rules>
        <rules>
            <name>Create_contact_and_contact_role_for_both</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Select_Contact</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>SurName</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue></stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Contact2</targetReference>
            </connector>
            <label>Create contact and contact role for both</label>
        </rules>
    </decisions>
    <dynamicChoiceSets>
        <name>RelatedContacts</name>
        <dataType>String</dataType>
        <displayField>Name</displayField>
        <filterLogic>and</filterLogic>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <object>Contact</object>
        <sortField>Name</sortField>
        <sortOrder>Asc</sortOrder>
        <valueField>Id</valueField>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Stage_Picklist</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>StageName</picklistField>
        <picklistObject>Opportunity</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>Supplier</name>
        <dataType>String</dataType>
        <displayField>Name</displayField>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <object>Account</object>
        <valueField>Name</valueField>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <formulas>
        <name>Closedatevalue</name>
        <dataType>Date</dataType>
        <expression>ADDMONTHS({!$Flow.CurrentDate}, 1)</expression>
    </formulas>
    <formulas>
        <name>OppName</name>
        <dataType>String</dataType>
        <expression>{!Loan_Purpose}+&apos; £&apos;+ TEXT({!Amount_Required})</expression>
    </formulas>
    <interviewLabel>Create Opportunity {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Create Opportunity</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Copy_1_of_Create_Opportunity</name>
        <label>Supplier Opportunity</label>
        <locationX>314</locationX>
        <locationY>566</locationY>
        <assignRecordIdToReference>OppId</assignRecordIdToReference>
        <inputAssignments>
            <field>AccountId</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Amount_Required__c</field>
            <value>
                <elementReference>Amount_Required</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>CloseDate</field>
            <value>
                <elementReference>Close_Date</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Loan_Purpose__c</field>
            <value>
                <elementReference>Loan_Purpose</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Name</field>
            <value>
                <elementReference>OppName</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>Get_Finance_Record_Type.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>StageName</field>
            <value>
                <elementReference>Stage</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Supplier__c</field>
            <value>
                <elementReference>Opportunity_Supplier.recordId</elementReference>
            </value>
        </inputAssignments>
        <object>Opportunity</object>
    </recordCreates>
    <recordCreates>
        <name>Copy_1_of_Create_OpportunityContactRole</name>
        <label>Create OpportunityContactRole_WithSelectContact</label>
        <locationX>1898</locationX>
        <locationY>890</locationY>
        <inputAssignments>
            <field>ContactId</field>
            <value>
                <elementReference>Select_Contact</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OpportunityId</field>
            <value>
                <elementReference>OppId</elementReference>
            </value>
        </inputAssignments>
        <object>OpportunityContactRole</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_Contact1</name>
        <label>Create Contact</label>
        <locationX>578</locationX>
        <locationY>566</locationY>
        <assignRecordIdToReference>ContactId</assignRecordIdToReference>
        <connector>
            <targetReference>Create_Opp</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Log_Error_Only_Contact</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>AccountId</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Email</field>
            <value>
                <elementReference>Email</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>FirstName</field>
            <value>
                <elementReference>First_Name</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>LastName</field>
            <value>
                <elementReference>SurName</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Salutation</field>
            <value>
                <elementReference>Salutation</elementReference>
            </value>
        </inputAssignments>
        <object>Contact</object>
    </recordCreates>
    <recordCreates>
        <name>Create_Contact2</name>
        <label>Create Contact</label>
        <locationX>1898</locationX>
        <locationY>566</locationY>
        <assignRecordIdToReference>ContactId</assignRecordIdToReference>
        <connector>
            <targetReference>Create_Opp3</targetReference>
        </connector>
        <inputAssignments>
            <field>AccountId</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Email</field>
            <value>
                <elementReference>Email</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>FirstName</field>
            <value>
                <elementReference>First_Name</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>LastName</field>
            <value>
                <elementReference>SurName</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Salutation</field>
            <value>
                <elementReference>Salutation</elementReference>
            </value>
        </inputAssignments>
        <object>Contact</object>
    </recordCreates>
    <recordCreates>
        <name>Create_Opp</name>
        <label>Create Opportunity</label>
        <locationX>578</locationX>
        <locationY>674</locationY>
        <assignRecordIdToReference>OppId</assignRecordIdToReference>
        <connector>
            <targetReference>Create_OpportunityContactRole</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Log_Error_Create_Opportunity</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>AccountId</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Amount_Required__c</field>
            <value>
                <elementReference>Amount_Required</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>CloseDate</field>
            <value>
                <elementReference>Close_Date</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Loan_Purpose__c</field>
            <value>
                <elementReference>Loan_Purpose</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Name</field>
            <value>
                <elementReference>OppName</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>Get_Finance_Record_Type.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>StageName</field>
            <value>
                <elementReference>Stage</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Supplier__c</field>
            <value>
                <elementReference>Opportunity_Supplier.recordId</elementReference>
            </value>
        </inputAssignments>
        <object>Opportunity</object>
    </recordCreates>
    <recordCreates>
        <name>Create_Opp2</name>
        <label>Create Only Opportunity</label>
        <locationX>1370</locationX>
        <locationY>566</locationY>
        <assignRecordIdToReference>OppId</assignRecordIdToReference>
        <connector>
            <targetReference>Create_OpportunityContactRole2</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Log_Error_Only_Opportunity</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>AccountId</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Amount_Required__c</field>
            <value>
                <elementReference>Amount_Required</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>CloseDate</field>
            <value>
                <elementReference>Close_Date</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Loan_Purpose__c</field>
            <value>
                <elementReference>Loan_Purpose</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Name</field>
            <value>
                <elementReference>OppName</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>Get_Finance_Record_Type.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>StageName</field>
            <value>
                <elementReference>Stage</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Supplier__c</field>
            <value>
                <elementReference>Opportunity_Supplier.recordId</elementReference>
            </value>
        </inputAssignments>
        <object>Opportunity</object>
    </recordCreates>
    <recordCreates>
        <name>Create_Opp3</name>
        <label>Create Opp</label>
        <locationX>1898</locationX>
        <locationY>674</locationY>
        <assignRecordIdToReference>OppId</assignRecordIdToReference>
        <connector>
            <targetReference>Create_OpportunityContactRole3</targetReference>
        </connector>
        <inputAssignments>
            <field>AccountId</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Amount_Required__c</field>
            <value>
                <elementReference>Amount_Required</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>CloseDate</field>
            <value>
                <elementReference>Close_Date</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Loan_Purpose__c</field>
            <value>
                <elementReference>Loan_Purpose</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Name</field>
            <value>
                <elementReference>OppName</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>Get_Finance_Record_Type.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>StageName</field>
            <value>
                <elementReference>Stage</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Supplier__c</field>
            <value>
                <elementReference>Opportunity_Supplier.recordId</elementReference>
            </value>
        </inputAssignments>
        <object>Opportunity</object>
    </recordCreates>
    <recordCreates>
        <name>Create_OpportunityContactRole</name>
        <label>Create OpportunityContactRole</label>
        <locationX>578</locationX>
        <locationY>782</locationY>
        <inputAssignments>
            <field>ContactId</field>
            <value>
                <elementReference>ContactId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OpportunityId</field>
            <value>
                <elementReference>OppId</elementReference>
            </value>
        </inputAssignments>
        <object>OpportunityContactRole</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_OpportunityContactRole2</name>
        <label>Create OpportunityContactRole</label>
        <locationX>1370</locationX>
        <locationY>674</locationY>
        <inputAssignments>
            <field>ContactId</field>
            <value>
                <elementReference>Select_Contact</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OpportunityId</field>
            <value>
                <elementReference>OppId</elementReference>
            </value>
        </inputAssignments>
        <object>OpportunityContactRole</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_OpportunityContactRole3</name>
        <label>Create OpportunityContactRole</label>
        <locationX>1898</locationX>
        <locationY>782</locationY>
        <connector>
            <targetReference>Copy_1_of_Create_OpportunityContactRole</targetReference>
        </connector>
        <inputAssignments>
            <field>ContactId</field>
            <value>
                <elementReference>ContactId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>OpportunityId</field>
            <value>
                <elementReference>OppId</elementReference>
            </value>
        </inputAssignments>
        <object>OpportunityContactRole</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Get_Finance_Record_Type</name>
        <label>Get Finance Record Type</label>
        <locationX>1106</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Create_Contact_Opportunity</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Opportunity</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Asset_Finance</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <screens>
        <name>Create_Opportunity</name>
        <label>Create Opportunity</label>
        <locationX>1106</locationX>
        <locationY>134</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Select_Create_Contact</targetReference>
        </connector>
        <fields>
            <name>Amount_Required</name>
            <dataType>Currency</dataType>
            <fieldText>Amount Required</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
            <scale>0</scale>
        </fields>
        <fields>
            <name>Loan_Purpose</name>
            <dataType>String</dataType>
            <fieldText>Loan Purpose</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Stage</name>
            <choiceReferences>Stage_Picklist</choiceReferences>
            <dataType>String</dataType>
            <defaultValue>
                <stringValue>Enquiry</stringValue>
            </defaultValue>
            <fieldText>Stage</fieldText>
            <fieldType>DropdownBox</fieldType>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>Close_Date</name>
            <dataType>Date</dataType>
            <defaultValue>
                <elementReference>Closedatevalue</elementReference>
            </defaultValue>
            <fieldText>Close Date</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Display_Error</name>
        <label>Display Error</label>
        <locationX>1106</locationX>
        <locationY>782</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>DisplayError</name>
            <fieldText>&lt;p&gt;{!Log_Error_Only_Contact}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Display_Error2</name>
        <label>Display Error</label>
        <locationX>842</locationX>
        <locationY>890</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>Copy_1_of_DisplayError</name>
            <fieldText>&lt;p&gt;{!Log_Error_Create_Opportunity}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Display_Error3</name>
        <label>Display Error</label>
        <locationX>1634</locationX>
        <locationY>782</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>Copy_2_of_DisplayError</name>
            <fieldText>&lt;p&gt;{!Log_Error_Only_Opportunity}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Error_NothingSelected</name>
        <label>Error_NothingSelected</label>
        <locationX>50</locationX>
        <locationY>566</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>Error_NoContact_SupplierSelected</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;color: rgb(215, 42, 42); font-size: 16px;&quot;&gt;Error: &lt;/strong&gt;&lt;strong style=&quot;color: rgb(11, 11, 11); font-size: 16px;&quot;&gt;You must select or create a  contact if a supplier hasn&apos;t been selected.&lt;/strong&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Select_Create_Contact</name>
        <label>Select/Create Contact</label>
        <locationX>1106</locationX>
        <locationY>242</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Get_Finance_Record_Type</targetReference>
        </connector>
        <fields>
            <name>Select_Create_Contact_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Select_Create_Contact_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Select_Contact</name>
                    <choiceReferences>RelatedContacts</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>Select Contact</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <isRequired>false</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Select_Create_Contact_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Opportunity_Supplier</name>
                    <extensionName>flowruntime:lookup</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>fieldApiName</name>
                        <value>
                            <stringValue>Supplier__c</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>objectApiName</name>
                        <value>
                            <stringValue>Opportunity</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Supplier</stringValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <fields>
            <name>Display_Text</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;and/or create contact:&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Salutation</name>
            <dataType>String</dataType>
            <fieldText>Salutation</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>First_Name</name>
            <dataType>String</dataType>
            <fieldText>First Name</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>SurName</name>
            <dataType>String</dataType>
            <fieldText>Surname</fieldText>
            <fieldType>InputField</fieldType>
            <helpText>&lt;p&gt;Mandatory to create new contact&lt;/p&gt;</helpText>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>Email</name>
            <dataType>String</dataType>
            <fieldText>Email</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>false</isRequired>
        </fields>
        <nextOrFinishButtonLabel>Create</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>980</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Create_Opportunity</targetReference>
        </connector>
    </start>
    <status>Draft</status>
    <variables>
        <name>ContactId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>OppId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
