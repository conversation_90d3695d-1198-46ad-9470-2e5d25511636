<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>62.0</apiVersion>
    <decisions>
        <name>Group_Member_present</name>
        <label>Group Member present?</label>
        <locationX>182</locationX>
        <locationY>647</locationY>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yess</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Assignment_Group_members</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Owner</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_the_assignment_group_present</name>
        <label>Is the assignment group present?</label>
        <locationX>380</locationX>
        <locationY>431</locationY>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Assignment_Group</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Assignment_Group_members</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>Now</name>
        <dataType>DateTime</dataType>
        <expression>Now()</expression>
    </formulas>
    <interviewLabel>Opportunity- Assignment Group Automation {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Opportunity- Assignment Group Automation</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Assignment_Group</name>
        <label>Get Assignment Group</label>
        <locationX>380</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_the_assignment_group_present</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>HCFL Inbound Sales Team member</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Assignment_Group__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Assignment_Group_members</name>
        <label>Get Assignment Group member</label>
        <locationX>182</locationX>
        <locationY>539</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Group_Member_present</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Assignment_Group__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Assignment_Group.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Assignment_Group_Member__c</object>
        <sortField>Last_Assignment_Date__c</sortField>
        <sortOrder>Asc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Date_on_Assignment_Group_Member_s</name>
        <label>Update Date on Assignment Group Member&apos;s</label>
        <locationX>50</locationX>
        <locationY>863</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Assignment_Group_members.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Last_Assignment_Date__c</field>
            <value>
                <elementReference>Now</elementReference>
            </value>
        </inputAssignments>
        <object>Assignment_Group_Member__c</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Owner</name>
        <label>Update Owner</label>
        <locationX>50</locationX>
        <locationY>755</locationY>
        <connector>
            <targetReference>Update_Date_on_Assignment_Group_Member_s</targetReference>
        </connector>
        <inputAssignments>
            <field>OwnerId</field>
            <value>
                <elementReference>Get_Assignment_Group_members.User__r.Id</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>254</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Assignment_Group</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Opportunity_Record_Type__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Asset_Finance</stringValue>
            </value>
        </filters>
        <filters>
            <field>Marketing_Source__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Business Money</stringValue>
            </value>
        </filters>
        <object>Opportunity</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Obsolete</status>
</Flow>
