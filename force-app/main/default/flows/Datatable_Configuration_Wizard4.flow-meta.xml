<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Decode_Cell_Attribute</name>
        <label>Decode Cell Attribute</label>
        <locationX>548</locationX>
        <locationY>292</locationY>
        <actionName>ers_EncodeDecodeURL</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Decode_Type_Attribute</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>inputStr</name>
            <value>
                <elementReference>wiz_columnCellAttribs</elementReference>
            </value>
        </inputParameters>
        <nameSegment>ers_EncodeDecodeURL</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Decode_Other_Attribute</name>
        <label>Decode Other Attribute</label>
        <locationX>213</locationX>
        <locationY>292</locationY>
        <actionName>ers_EncodeDecodeURL</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Set_Quick_Choice_Options</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>inputStr</name>
            <value>
                <elementReference>wiz_columnOtherAttribs</elementReference>
            </value>
        </inputParameters>
        <nameSegment>ers_EncodeDecodeURL</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Decode_Type_Attribute</name>
        <label>Decode Type Attribute</label>
        <locationX>390</locationX>
        <locationY>292</locationY>
        <actionName>ers_EncodeDecodeURL</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Decode_Other_Attribute</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>inputStr</name>
            <value>
                <elementReference>wiz_columnTypeAttribs</elementReference>
            </value>
        </inputParameters>
        <nameSegment>ers_EncodeDecodeURL</nameSegment>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Get_Field_Information_usf</name>
        <label>Get Field Information usf</label>
        <locationX>1263</locationX>
        <locationY>210</locationY>
        <actionName>usf3__GetFieldInformation</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Select_Fields</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>objectName</name>
            <value>
                <elementReference>vSObject</elementReference>
            </value>
        </inputParameters>
        <nameSegment>usf3__GetFieldInformation</nameSegment>
        <outputParameters>
            <assignToReference>apexColFieldDescriptors_usf</assignToReference>
            <name>fields</name>
        </outputParameters>
    </actionCalls>
    <actionCalls>
        <name>Get_First_xx_Records</name>
        <label>Get First xx Records</label>
        <locationX>2132</locationX>
        <locationY>208</locationY>
        <actionName>ers_QueryNRecords</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Get_Records_Result</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>objectApiName</name>
            <value>
                <elementReference>vSObject</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>fieldsToQuery</name>
            <value>
                <elementReference>vFieldList</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>numberOfRecords</name>
            <value>
                <elementReference>vWizRecordCount</elementReference>
            </value>
        </inputParameters>
        <nameSegment>ers_QueryNRecords</nameSegment>
        <outputParameters>
            <assignToReference>vRecordList</assignToReference>
            <name>recordString</name>
        </outputParameters>
    </actionCalls>
    <apiVersion>52.0</apiVersion>
    <assignments>
        <name>Add_Config_Quick_Choice_Option</name>
        <label>Add Config Quick Choice Option</label>
        <locationX>547</locationX>
        <locationY>637</locationY>
        <assignmentItems>
            <assignToReference>colTitles</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>cConfig</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>colDescriptions</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>Load the column configuration from a custom Configuration record</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>colIcons</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>utility:component_customization</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Field_Selection_Method_Screen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Add_Last_Field</name>
        <label>Add Last Field</label>
        <locationX>949</locationX>
        <locationY>760</locationY>
        <assignmentItems>
            <assignToReference>colFieldList</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>vConfigFields</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Previously_Selected_Fields</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Config_Fields</name>
        <label>Assign Config Fields</label>
        <locationX>819</locationX>
        <locationY>764</locationY>
        <assignmentItems>
            <assignToReference>colFieldList</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>vLoadedConfig</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>vFieldList</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ConfigTable_select.outputSelectedRow.Field_API_Names__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>vConfigFields</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ConfigTable_select.outputSelectedRow.Field_API_Names__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>vSelectionMethod</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ConfigTable_select.outputSelectedRow.Selection_Method__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>wiz_columnAlignments</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ConfigTable_select.outputSelectedRow.Alignments__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>wiz_columnEdits</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ConfigTable_select.outputSelectedRow.Edits__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>wiz_columnFields</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ConfigTable_select.outputSelectedRow.Field_API_Names__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>wiz_columnFilters</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ConfigTable_select.outputSelectedRow.Filters__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>wiz_columnIcons</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ConfigTable_select.outputSelectedRow.Icons__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>wiz_columnLabels</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ConfigTable_select.outputSelectedRow.Labels__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>wiz_columnWidths</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ConfigTable_select.outputSelectedRow.Widths__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>wiz_columnWraps</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ConfigTable_select.outputSelectedRow.Wraps__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>wiz_columnCellAttribs</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ConfigTable_select.outputSelectedRow.Cell_Attributes__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>wiz_columnOtherAttribs</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ConfigTable_select.outputSelectedRow.Other_Attributes__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>wiz_columnTypeAttribs</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ConfigTable_select.outputSelectedRow.Type_Attributes__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>objConfig</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ConfigTable_select.outputSelectedRow</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>colPreSelectedConfigs</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ConfigTable_select.outputSelectedRows</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>vConfigToggleStatus</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Last_Field</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_PreSelected</name>
        <label>Assign PreSelected</label>
        <locationX>555</locationX>
        <locationY>893</locationY>
        <assignmentItems>
            <assignToReference>colPreSelectedConfigs</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Available_Config_Records</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Add_Config_Quick_Choice_Option</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Clear_Early_Exit_Flag</name>
        <label>Clear Early Exit Flag</label>
        <locationX>2945</locationX>
        <locationY>206</locationY>
        <assignmentItems>
            <assignToReference>vEarlyExit</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Save_Configuration_Data</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Clear_Record_Id</name>
        <label>Clear Record Id</label>
        <locationX>3521</locationX>
        <locationY>456</locationY>
        <assignmentItems>
            <assignToReference>objConfig.Id</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <connector>
            <targetReference>Create_New_Config_Record</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Get_Config_Record_Count</name>
        <label>Get Config Record Count</label>
        <locationX>259</locationX>
        <locationY>895</locationY>
        <assignmentItems>
            <assignToReference>vConfigCount</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Get_Available_Config_Records</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>How_Many_Records</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Process_Config_Field</name>
        <label>Process Config Field</label>
        <locationX>677</locationX>
        <locationY>855</locationY>
        <assignmentItems>
            <assignToReference>colFieldList</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>fFirstField</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>vConfigFields</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>fRemoveField</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Last_Field</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Save_Configuration_Data</name>
        <label>Save Configuration Data</label>
        <locationX>3087</locationX>
        <locationY>206</locationY>
        <assignmentItems>
            <assignToReference>objConfig.Selection_Method__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>vSelectionMethod</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>objConfig.Object_API_Name__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>vSObject</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>objConfig.Alignments__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>wiz_columnAlignments</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>objConfig.Edits__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>wiz_columnEdits</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>objConfig.Field_API_Names__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>wiz_columnFields</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>objConfig.Filters__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>wiz_columnFilters</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>objConfig.Icons__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>wiz_columnIcons</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>objConfig.Labels__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>wiz_columnLabels</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>objConfig.Widths__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>wiz_columnWidths</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>objConfig.Wraps__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>wiz_columnWraps</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>objConfig.Cell_Attributes__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>wiz_columnCellAttribs</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>objConfig.Type_Attributes__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>wiz_columnTypeAttribs</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>objConfig.Other_Attributes__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>wiz_columnOtherAttribs</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Config_Record</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Save_Selected_Config_Record</name>
        <label>Save Selected Config Record</label>
        <locationX>2441</locationX>
        <locationY>207</locationY>
        <assignmentItems>
            <assignToReference>objConfig</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ConfigTable_saveas.outputSelectedRow</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>vNewConfigName</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Configuration_Record_Name</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>vConfigToggleStatus</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ConfigToggle.value</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Special_Attribute_Changes</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Save_Selection_Method</name>
        <label>Save Selection Method</label>
        <locationX>633</locationX>
        <locationY>427</locationY>
        <assignmentItems>
            <assignToReference>vSelectionMethod</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>FieldSelectionMethod.value</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Selected_Option</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_Error_Flag</name>
        <label>Set Error Flag</label>
        <locationX>3478</locationX>
        <locationY>29</locationY>
        <assignmentItems>
            <assignToReference>vDuplicateNameError</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>vConfigToggleStatus</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Wizard_Screen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_Field_List_Parameter</name>
        <label>Set Field List Parameter</label>
        <locationX>1823</locationX>
        <locationY>209</locationY>
        <assignmentItems>
            <assignToReference>vFieldList</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>List_of_Field_Names</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>wiz_columnFields</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>List_of_Field_Names</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Did_User_Enter_Field_List</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_Field_List_Parameter_0</name>
        <label>Set Field List Parameter</label>
        <locationX>2038</locationX>
        <locationY>583</locationY>
        <assignmentItems>
            <assignToReference>wiz_columnFields</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>List_of_Field_Names</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_First_xx_Records</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_Field_Name</name>
        <label>Set Field Name</label>
        <locationX>1114</locationX>
        <locationY>638</locationY>
        <assignmentItems>
            <assignToReference>apexFieldDescriptor.name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Previously_Selected_Fields</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Selected_Field_Descriptors</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_Metadata_Name_create</name>
        <label>Set Metadata Name</label>
        <locationX>3670</locationX>
        <locationY>328</locationY>
        <assignmentItems>
            <assignToReference>objConfig.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>vNewConfigName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>objConfig.View_Name__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>vNewConfigName</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Clear_Record_Id</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_Quick_Choice_Options</name>
        <label>Set Quick Choice Options</label>
        <locationX>213</locationX>
        <locationY>495</locationY>
        <assignmentItems>
            <assignToReference>colTitles</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>cPick</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>colTitles</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>cType</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>colDescriptions</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>Pick your table columns from a list</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>colDescriptions</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>Manually specify the column fields</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>colIcons</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>standard:picklist_choice</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>colIcons</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>standard:text</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>vEarlyExit</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>wiz_columnFields</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>vFieldList</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>vDebugMode</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>vConfigFields</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>fTrimFieldList</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Available_Config_Records</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_Selected_Field_Descriptors</name>
        <label>Update Selected Field Descriptors</label>
        <locationX>1271</locationX>
        <locationY>638</locationY>
        <assignmentItems>
            <assignToReference>apexColSelectedFieldDescriptors_usf</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>apexFieldDescriptor</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Previously_Selected_Fields</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_Special_Attributes</name>
        <label>Update Special Attributes</label>
        <locationX>2788</locationX>
        <locationY>300</locationY>
        <assignmentItems>
            <assignToReference>wiz_columnCellAttribs</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Special_Cell_Attributes</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>wiz_columnTypeAttribs</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Special_Type_Attributes</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>wiz_columnOtherAttribs</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Special_Other_Attributes</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Clear_Early_Exit_Flag</targetReference>
        </connector>
    </assignments>
    <constants>
        <name>cConfig</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Config</stringValue>
        </value>
    </constants>
    <constants>
        <name>cPick</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Pick</stringValue>
        </value>
    </constants>
    <constants>
        <name>cType</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Type</stringValue>
        </value>
    </constants>
    <decisions>
        <name>Any_Fields_Selected</name>
        <label>Any Fields Selected?</label>
        <locationX>1821</locationX>
        <locationY>584</locationY>
        <defaultConnector>
            <targetReference>Select_Fields</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>NONE</defaultConnectorLabel>
        <rules>
            <name>YES_fs</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>fListLength</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Field_List_Parameter_0</targetReference>
            </connector>
            <label>YES</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Field_Selection_Method</name>
        <label>Check Field Selection Method</label>
        <locationX>1605</locationX>
        <locationY>212</locationY>
        <defaultConnectorLabel>Error</defaultConnectorLabel>
        <rules>
            <name>PICK</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>vSelectionMethod</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>cPick</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Any_Fields_Selected</targetReference>
            </connector>
            <label>PICK</label>
        </rules>
        <rules>
            <name>MANUAL</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>vSelectionMethod</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>cType</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Field_List_Parameter</targetReference>
            </connector>
            <label>MANUAL</label>
        </rules>
    </decisions>
    <decisions>
        <name>Config_Record</name>
        <label>Config Record?</label>
        <locationX>3216</locationX>
        <locationY>208</locationY>
        <defaultConnector>
            <targetReference>Wizard_Finish</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>NO CONFIG RECORD</defaultConnectorLabel>
        <rules>
            <name>SAVE</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>ConfigButtonSave.value</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Save</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>ConfigTable_saveas.numberOfRowsSelected</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Save_Config_Record</targetReference>
            </connector>
            <label>SAVE</label>
        </rules>
        <rules>
            <name>CREATE</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>ConfigButtonCreate.value</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Create</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>vNewConfigName</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_For_Existing_Config_Record</targetReference>
            </connector>
            <label>CREATE</label>
        </rules>
    </decisions>
    <decisions>
        <name>Did_User_Enter_Field_List</name>
        <label>Did User Enter Field List?</label>
        <locationX>1969</locationX>
        <locationY>211</locationY>
        <defaultConnectorLabel>Error</defaultConnectorLabel>
        <rules>
            <name>YES</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>fListLength</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_First_xx_Records</targetReference>
            </connector>
            <label>YES</label>
        </rules>
        <rules>
            <name>NO</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>fListLength</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Any_Fields_Selected</targetReference>
            </connector>
            <label>NO</label>
        </rules>
    </decisions>
    <decisions>
        <name>Found_Config_Records</name>
        <label>Found Config Records?</label>
        <locationX>377</locationX>
        <locationY>637</locationY>
        <defaultConnector>
            <targetReference>Field_Selection_Method_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>NO</defaultConnectorLabel>
        <rules>
            <name>YES_md</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Available_Config_Records</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Config_Record_Count</targetReference>
            </connector>
            <label>YES</label>
        </rules>
    </decisions>
    <decisions>
        <name>Get_Records_Result</name>
        <label>Get Records Result</label>
        <locationX>2273</locationX>
        <locationY>395</locationY>
        <defaultConnector>
            <targetReference>Wizard_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>RECORDS FOUND</defaultConnectorLabel>
        <rules>
            <name>NO_RECORDS</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>vRecordList</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>[]</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Wizard_Exit</targetReference>
            </connector>
            <label>NO RECORDS</label>
        </rules>
    </decisions>
    <decisions>
        <name>How_Many_Records</name>
        <label>How Many Records?</label>
        <locationX>379</locationX>
        <locationY>897</locationY>
        <defaultConnector>
            <targetReference>Add_Config_Quick_Choice_Option</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>MULTIPLE</defaultConnectorLabel>
        <rules>
            <name>JUST_ONE</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>vConfigCount</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_PreSelected</targetReference>
            </connector>
            <label>JUST ONE</label>
        </rules>
    </decisions>
    <decisions>
        <name>Last_Field</name>
        <label>Last Field?</label>
        <locationX>811</locationX>
        <locationY>903</locationY>
        <defaultConnector>
            <targetReference>Process_Config_Field</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>NO</defaultConnectorLabel>
        <rules>
            <name>YES_lf</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>fHasComma</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Add_Last_Field</targetReference>
            </connector>
            <label>YES</label>
        </rules>
    </decisions>
    <decisions>
        <name>Name_Already_Exists</name>
        <label>Name Already Exists?</label>
        <locationX>3528</locationX>
        <locationY>207</locationY>
        <defaultConnector>
            <targetReference>Set_Metadata_Name_create</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>NEW NAME</defaultConnectorLabel>
        <rules>
            <name>NAME_EXISTS</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Check_For_Existing_Config_Record</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Error_Flag</targetReference>
            </connector>
            <label>NAME EXISTS</label>
        </rules>
    </decisions>
    <decisions>
        <name>Selected_Option</name>
        <label>Selected Option?</label>
        <locationX>811</locationX>
        <locationY>429</locationY>
        <defaultConnector>
            <targetReference>Loop_Previously_Selected_Fields</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Pick or Type</defaultConnectorLabel>
        <rules>
            <name>Config</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>vSelectionMethod</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>cConfig</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Select_Config_Record</targetReference>
            </connector>
            <label>Config</label>
        </rules>
    </decisions>
    <decisions>
        <name>Special_Attribute_Changes</name>
        <label>Special Attribute Changes?</label>
        <locationX>2609</locationX>
        <locationY>209</locationY>
        <defaultConnector>
            <targetReference>Clear_Early_Exit_Flag</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>IGNORE</defaultConnectorLabel>
        <rules>
            <name>SAVE_atrib</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>SaveAttributes.value</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Save</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Special_Attributes</targetReference>
            </connector>
            <label>SAVE</label>
        </rules>
    </decisions>
    <description>Flow designed to run inside of the datatable CPE to select and set component attributes by interacting with a sample datatable.</description>
    <formulas>
        <name>fFirstField</name>
        <dataType>String</dataType>
        <expression>TRIM(LEFT({!vConfigFields},FIND(&quot;,&quot;,{!vConfigFields})-1))</expression>
    </formulas>
    <formulas>
        <name>fHasComma</name>
        <dataType>Boolean</dataType>
        <expression>FIND(&quot;,&quot;,{!vConfigFields})&gt;0</expression>
    </formulas>
    <formulas>
        <name>fListLength</name>
        <dataType>Number</dataType>
        <expression>LEN({!vFieldList})</expression>
        <scale>0</scale>
    </formulas>
    <formulas>
        <name>fRemoveField</name>
        <dataType>String</dataType>
        <expression>MID({!vConfigFields},FIND(&quot;,&quot;,{!vConfigFields})+1,999)</expression>
    </formulas>
    <formulas>
        <name>fTrimFieldList</name>
        <dataType>String</dataType>
        <expression>TRIM({!vFieldList})</expression>
    </formulas>
    <interviewLabel>Datatable Configuration Wizard {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Datatable Configuration Wizard</label>
    <loops>
        <name>Loop_Previously_Selected_Fields</name>
        <label>Loop Previously Selected Fields</label>
        <locationX>1263</locationX>
        <locationY>433</locationY>
        <collectionReference>colFieldList</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Set_Field_Name</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Get_Field_Information_usf</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>FREE_FORM_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Create_New_Config_Record</name>
        <label>Create New Config Record</label>
        <locationX>3374</locationX>
        <locationY>456</locationY>
        <connector>
            <targetReference>Wizard_Finish</targetReference>
        </connector>
        <inputReference>objConfig</inputReference>
    </recordCreates>
    <recordLookups>
        <name>Check_For_Existing_Config_Record</name>
        <label>Check For Existing Config Record</label>
        <locationX>3390</locationX>
        <locationY>205</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Name_Already_Exists</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>vNewConfigName</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>FlowTableViewDefinition__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Available_Config_Records</name>
        <label>Get Available Config Records</label>
        <locationX>213</locationX>
        <locationY>635</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Found_Config_Records</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Active__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Object_API_Name__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>vSObject</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>FlowTableViewDefinition__c</object>
        <sortField>Name</sortField>
        <sortOrder>Asc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Save_Config_Record</name>
        <label>Save Config Record</label>
        <locationX>3097</locationX>
        <locationY>454</locationY>
        <connector>
            <targetReference>Wizard_Finish</targetReference>
        </connector>
        <inputReference>objConfig</inputReference>
    </recordUpdates>
    <screens>
        <name>Field_Selection_Method_Screen</name>
        <label>Field Selection Method Screen</label>
        <locationX>465</locationX>
        <locationY>427</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Save_Selection_Method</targetReference>
        </connector>
        <fields>
            <name>qcHeader</name>
            <fieldText>&lt;p&gt;&lt;b style=&quot;font-size: 18px; background-color: rgb(255, 255, 255); color: rgb(62, 62, 60);&quot;&gt;{!vSObject}&lt;/b&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>FieldSelectionMethod</name>
            <extensionName>c:fsc_quickChoiceFSC</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>displayMode</name>
                <value>
                    <stringValue>Visual</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>inputMode</name>
                <value>
                    <stringValue>Dual String Collections</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>numberOfColumns</name>
                <value>
                    <stringValue>1</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>includeIcons</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>navOnSelect</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>choiceIcons</name>
                <value>
                    <elementReference>colIcons</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>choiceLabels</name>
                <value>
                    <elementReference>colTitles</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>choiceValues</name>
                <value>
                    <elementReference>colDescriptions</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>required</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>masterLabel</name>
                <value>
                    <stringValue>How do you want to select your columns?</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>iconSize</name>
                <value>
                    <stringValue>large</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>value</name>
                <value>
                    <elementReference>vSelectionMethod</elementReference>
                </value>
            </inputParameters>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Select_Config_Record</name>
        <label>Select Config Record</label>
        <locationX>819</locationX>
        <locationY>609</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Assign_Config_Fields</targetReference>
        </connector>
        <fields>
            <name>ConfigTable_select</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>FlowTableViewDefinition__c</typeValue>
            </dataTypeMappings>
            <extensionName>c:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>objectName</name>
                <value>
                    <stringValue>FlowTableViewDefinition__c</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>isDisplayHeader</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>cb_isDisplayHeader</name>
                <value>
                    <stringValue>CB_TRUE</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableLabel</name>
                <value>
                    <stringValue>Select a Datatable Configuration</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableIcon</name>
                <value>
                    <stringValue>utility:component_customization</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columnFields</name>
                <value>
                    <stringValue>Name,Object_API_Name__c,Field_API_Names__c,CreatedDate,LastModifiedDate</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>cb_matchCaseOnFilters</name>
                <value>
                    <stringValue>CB_FALSE</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columnLabels</name>
                <value>
                    <stringValue>Name:Configuration Name, Object_API_Name__c:Object, Field_API_Names__c:Columns, CreatedDate:Created, LastModifiedDate:Modified</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columnWidths</name>
                <value>
                    <stringValue>Name:225, Object_API_Name__c:125, Field_API_Names__c:310, CreatedDate:110, LastModifiedDate:110</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columnWraps</name>
                <value>
                    <stringValue>Name:true, Object_API_Name__c:true, Field_API_Names__c:true, Labels__c:true, CreatedDate:true, LastModifiedDate:true</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>hideClearSelectionButton</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>cb_hideClearSelectionButton</name>
                <value>
                    <stringValue>CB_TRUE</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>singleRowSelection</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>cb_singleRowSelection</name>
                <value>
                    <stringValue>CB_TRUE</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columnFilters</name>
                <value>
                    <stringValue>All</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Get_Available_Config_Records</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>preSelectedRows</name>
                <value>
                    <elementReference>colPreSelectedConfigs</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>cb_suppressBottomBar</name>
                <value>
                    <stringValue>CB_FALSE</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>cb_navigateNextOnSave</name>
                <value>
                    <stringValue>CB_FALSE</stringValue>
                </value>
            </inputParameters>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Select_Fields</name>
        <label>Select Fields</label>
        <locationX>1428</locationX>
        <locationY>210</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Check_Field_Selection_Method</targetReference>
        </connector>
        <fields>
            <name>Object</name>
            <fieldText>&lt;p&gt;&lt;b style=&quot;font-size: 18px;&quot;&gt;{!vSObject}&lt;/b&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>SelectFields</name>
            <extensionName>c:fsc_dualListBox3</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>allOptionsStringFormat</name>
                <value>
                    <stringValue>object</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>useWhichObjectKeyForData</name>
                <value>
                    <stringValue>name</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>useWhichObjectKeyForSort</name>
                <value>
                    <stringValue>label</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectedLabel</name>
                <value>
                    <stringValue>Selected</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>min</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>max</name>
                <value>
                    <numberValue>20.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>size</name>
                <value>
                    <numberValue>7.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>allOptionsFieldDescriptorList</name>
                <value>
                    <elementReference>apexColFieldDescriptors_usf</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Select and Order your Datatable Fields</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>sourceLabel</name>
                <value>
                    <stringValue>Available</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectedOptionsFieldDescriptorList</name>
                <value>
                    <elementReference>apexColSelectedFieldDescriptors_usf</elementReference>
                </value>
            </inputParameters>
            <isRequired>true</isRequired>
            <outputParameters>
                <assignToReference>vFieldList</assignToReference>
                <name>selectedOptionsCSV</name>
            </outputParameters>
            <outputParameters>
                <assignToReference>apexColSelectedFieldDescriptors_usf</assignToReference>
                <name>selectedOptionsFieldDescriptorList</name>
            </outputParameters>
            <visibilityRule>
                <conditionLogic>or</conditionLogic>
                <conditions>
                    <leftValueReference>vSelectionMethod</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <elementReference>cPick</elementReference>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>vSelectionMethod</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <elementReference>cConfig</elementReference>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>List_of_Field_Names</name>
            <dataType>String</dataType>
            <defaultValue>
                <elementReference>vFieldList</elementReference>
            </defaultValue>
            <fieldText>Enter a comma separated list of field API names</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>false</isRequired>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>vSelectionMethod</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <elementReference>cType</elementReference>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Wizard_Exit</name>
        <label>Wizard Exit</label>
        <locationX>2282</locationX>
        <locationY>578</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>FinishMessage_0</name>
            <fieldText>&lt;p&gt;&lt;b style=&quot;color: rgb(182, 56, 56); font-size: 24px;&quot;&gt;&lt;i&gt;The ﻿﻿Column Wizard requires that the selected Object ({!vSObject}) has at least one record!&lt;/i&gt;&lt;/b&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Wizard_Finish</name>
        <label>Wizard Finish</label>
        <locationX>3235</locationX>
        <locationY>520</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>FinishMessage</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;b style=&quot;color: rgb(101, 150, 104); font-size: 24px;&quot;&gt;&lt;i&gt;﻿﻿Column Wizard has finished.  Please close this window.&lt;/i&gt;&lt;/b&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Wizard_Screen</name>
        <label>Wizard Screen</label>
        <locationX>2282</locationX>
        <locationY>142</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Save_Selected_Config_Record</targetReference>
        </connector>
        <fields>
            <name>debug</name>
            <fieldText>&lt;p&gt;Debug Mode: {!vDebugMode}&lt;/p&gt;&lt;p&gt;Selected Object: {!vSObject}&lt;/p&gt;&lt;p&gt;Selected Fields: {!vFieldList}&lt;/p&gt;&lt;p&gt;Selected Field Descriptors: {!apexColSelectedFieldDescriptors_usf}&lt;/p&gt;&lt;p&gt;10 Records: {!vRecordList}&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;u&gt;Wizard Attributes&lt;/u&gt;&lt;/p&gt;&lt;p&gt;Alignments: {!wiz_columnAlignments}&lt;/p&gt;&lt;p&gt;Edits: {!wiz_columnEdits}&lt;/p&gt;&lt;p&gt;Filters: {!wiz_columnFilters}&lt;/p&gt;&lt;p&gt;Labels: {!wiz_columnLabels}&lt;/p&gt;&lt;p&gt;Icons: {!wiz_columnIcons}&lt;/p&gt;&lt;p&gt;Widths: {!wiz_columnWidths}&lt;/p&gt;&lt;p&gt;Wraps: {!wiz_columnWraps}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>vDebugMode</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>WizardTable</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>User</typeValue>
            </dataTypeMappings>
            <extensionName>c:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>isConfigMode</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columnFields</name>
                <value>
                    <elementReference>vFieldList</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableDataString</name>
                <value>
                    <elementReference>vRecordList</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>hideCheckboxColumn</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>isUserDefinedObject</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columnAlignments</name>
                <value>
                    <elementReference>wiz_columnAlignments</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columnEdits</name>
                <value>
                    <elementReference>wiz_columnEdits</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columnFilters</name>
                <value>
                    <elementReference>wiz_columnFilters</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columnIcons</name>
                <value>
                    <elementReference>wiz_columnIcons</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columnLabels</name>
                <value>
                    <elementReference>wiz_columnLabels</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columnWidths</name>
                <value>
                    <elementReference>wiz_columnWidths</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columnWraps</name>
                <value>
                    <elementReference>wiz_columnWraps</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>cb_isUserDefinedObject</name>
                <value>
                    <stringValue>CB_TRUE</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columnCellAttribs</name>
                <value>
                    <elementReference>wiz_columnCellAttribs</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columnTypeAttribs</name>
                <value>
                    <elementReference>wiz_columnTypeAttribs</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columnOtherAttribs</name>
                <value>
                    <elementReference>wiz_columnOtherAttribs</elementReference>
                </value>
            </inputParameters>
            <isRequired>true</isRequired>
            <outputParameters>
                <assignToReference>wiz_columnLabels</assignToReference>
                <name>wizColumnLabels</name>
            </outputParameters>
            <outputParameters>
                <assignToReference>wiz_columnFields</assignToReference>
                <name>wizColumnFields</name>
            </outputParameters>
            <outputParameters>
                <assignToReference>wiz_columnAlignments</assignToReference>
                <name>wizColumnAlignments</name>
            </outputParameters>
            <outputParameters>
                <assignToReference>wiz_columnEdits</assignToReference>
                <name>wizColumnEdits</name>
            </outputParameters>
            <outputParameters>
                <assignToReference>wiz_columnFilters</assignToReference>
                <name>wizColumnFilters</name>
            </outputParameters>
            <outputParameters>
                <assignToReference>wiz_columnIcons</assignToReference>
                <name>wizColumnIcons</name>
            </outputParameters>
            <outputParameters>
                <assignToReference>wiz_columnWidths</assignToReference>
                <name>wizColumnWidths</name>
            </outputParameters>
            <outputParameters>
                <assignToReference>wiz_columnWraps</assignToReference>
                <name>wizColumnWraps</name>
            </outputParameters>
            <outputParameters>
                <assignToReference>wiz_objectName</assignToReference>
                <name>wizSObject</name>
            </outputParameters>
        </fields>
        <fields>
            <name>AttributeToggle</name>
            <extensionName>flowruntime:toggle</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Edit Special Attributes</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>messageToggleActive</name>
                <value>
                    <stringValue>Edit</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>messageToggleInactive</name>
                <value>
                    <stringValue>Hide</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>value</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>Special_Cell_Attributes</name>
            <dataType>String</dataType>
            <defaultValue>
                <elementReference>Decode_Cell_Attribute.outputStr</elementReference>
            </defaultValue>
            <fieldText>Special Cell Attributes</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>false</isRequired>
            <visibilityRule>
                <conditionLogic>or</conditionLogic>
                <conditions>
                    <leftValueReference>AttributeToggle.value</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Special_Type_Attributes</name>
            <dataType>String</dataType>
            <defaultValue>
                <elementReference>Decode_Type_Attribute.outputStr</elementReference>
            </defaultValue>
            <fieldText>Special Type Attributes</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>false</isRequired>
            <visibilityRule>
                <conditionLogic>or</conditionLogic>
                <conditions>
                    <leftValueReference>AttributeToggle.value</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Special_Other_Attributes</name>
            <dataType>String</dataType>
            <defaultValue>
                <elementReference>Decode_Other_Attribute.outputStr</elementReference>
            </defaultValue>
            <fieldText>Special Other Attributes</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>false</isRequired>
            <visibilityRule>
                <conditionLogic>or</conditionLogic>
                <conditions>
                    <leftValueReference>AttributeToggle.value</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>SaveAttributes</name>
            <extensionName>c:fsc_flowButtonBar</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>alignment</name>
                <value>
                    <stringValue>left</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>orientation</name>
                <value>
                    <stringValue>horizontal</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>showLines</name>
                <value>
                    <stringValue>below</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>actionMode</name>
                <value>
                    <stringValue>selection</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>buttons</name>
                <value>
                    <stringValue>[{&quot;label&quot;:&quot;Ignore Changes&quot;,&quot;value&quot;:&quot;Ignore&quot;,&quot;iconPosition&quot;:&quot;left&quot;,&quot;variant&quot;:&quot;neutral&quot;,&quot;index&quot;:0},{&quot;label&quot;:&quot;Save Changes&quot;,&quot;value&quot;:&quot;Save&quot;,&quot;iconPosition&quot;:&quot;left&quot;,&quot;variant&quot;:&quot;neutral&quot;,&quot;index&quot;:1}]</stringValue>
                </value>
            </inputParameters>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>AttributeToggle.value</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>ConfigToggle</name>
            <extensionName>flowruntime:toggle</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Show Configuration Record Options</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>messageToggleActive</name>
                <value>
                    <stringValue>Show</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>messageToggleInactive</name>
                <value>
                    <stringValue>Hide</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>value</name>
                <value>
                    <elementReference>vConfigToggleStatus</elementReference>
                </value>
            </inputParameters>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>ConfigTable_saveas</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>FlowTableViewDefinition__c</typeValue>
            </dataTypeMappings>
            <extensionName>c:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>objectName</name>
                <value>
                    <stringValue>FlowTableViewDefinition__c</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Get_Available_Config_Records</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>isDisplayHeader</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>cb_isDisplayHeader</name>
                <value>
                    <stringValue>CB_TRUE</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableLabel</name>
                <value>
                    <stringValue>Select a Configuration Record to Update</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableIcon</name>
                <value>
                    <stringValue>utility:component_customization</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>cb_suppressBottomBar</name>
                <value>
                    <stringValue>CB_FALSE</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>cb_navigateNextOnSave</name>
                <value>
                    <stringValue>CB_FALSE</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columnFields</name>
                <value>
                    <stringValue>Name,Object_API_Name__c,Field_API_Names__c,CreatedDate,LastModifiedDate</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columnFilters</name>
                <value>
                    <stringValue>All</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columnLabels</name>
                <value>
                    <stringValue>Name:Configuration Name, Object_API_Name__c:Object, Field_API_Names__c:Columns, CreatedDate:Created, LastModifiedDate:Modified</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columnWidths</name>
                <value>
                    <stringValue>Name:225, Object_API_Name__c:125, Field_API_Names__c:310, CreatedDate:110, LastModifiedDate:110</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columnWraps</name>
                <value>
                    <stringValue>Name:true, Object_API_Name__c:true, Field_API_Names__c:true, Labels__c:true, CreatedDate:true, LastModifiedDate:true</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>singleRowSelection</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>cb_singleRowSelection</name>
                <value>
                    <stringValue>CB_TRUE</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>preSelectedRows</name>
                <value>
                    <elementReference>colPreSelectedConfigs</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>hideClearSelectionButton</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>cb_hideClearSelectionButton</name>
                <value>
                    <stringValue>CB_TRUE</stringValue>
                </value>
            </inputParameters>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>ConfigToggle.value</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>ConfigButtonSave</name>
            <extensionName>c:fsc_flowButtonBar</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>alignment</name>
                <value>
                    <stringValue>left</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>orientation</name>
                <value>
                    <stringValue>horizontal</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>showLines</name>
                <value>
                    <stringValue>below</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>actionMode</name>
                <value>
                    <stringValue>navigation</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>buttons</name>
                <value>
                    <stringValue>[{&quot;label&quot;:&quot;Save As Selected Configuration Record&quot;,&quot;value&quot;:&quot;Save&quot;,&quot;iconPosition&quot;:&quot;left&quot;,&quot;variant&quot;:&quot;brand-outline&quot;,&quot;iconName&quot;:&quot;utility:save&quot;,&quot;index&quot;:0}]</stringValue>
                </value>
            </inputParameters>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>ConfigToggle.value</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Configuration_Record_Name</name>
            <dataType>String</dataType>
            <defaultValue>
                <elementReference>vNewConfigName</elementReference>
            </defaultValue>
            <fieldText>New Configuration Record Name (Enter a name here if you want to create a new record of this configuration for reuse later)</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>false</isRequired>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>ConfigToggle.value</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>DuplicateNameErrorMessage</name>
            <fieldText>&lt;p&gt;&lt;i style=&quot;color: rgb(255, 0, 0);&quot;&gt;A Configuration Record with the name &lt;/i&gt;&lt;b style=&quot;color: rgb(255, 0, 0);&quot;&gt;&lt;i&gt;{!vNewConfigName}&lt;/i&gt;&lt;/b&gt;&lt;i style=&quot;color: rgb(255, 0, 0);&quot;&gt; already exists. Please provide a unique name for a new record.&lt;/i&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>vDuplicateNameError</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>ConfigButtonCreate</name>
            <extensionName>c:fsc_flowButtonBar</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>alignment</name>
                <value>
                    <stringValue>left</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>orientation</name>
                <value>
                    <stringValue>horizontal</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>showLines</name>
                <value>
                    <stringValue>neither</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>actionMode</name>
                <value>
                    <stringValue>navigation</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>buttons</name>
                <value>
                    <stringValue>[{&quot;label&quot;:&quot;Create New Configuration Record&quot;,&quot;value&quot;:&quot;Create&quot;,&quot;iconPosition&quot;:&quot;left&quot;,&quot;variant&quot;:&quot;brand-outline&quot;,&quot;iconName&quot;:&quot;utility:new&quot;,&quot;index&quot;:0}]</stringValue>
                </value>
            </inputParameters>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>ConfigToggle.value</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>NoLoadConfig</name>
            <extensionName>c:fsc_flowButtonBar</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>alignment</name>
                <value>
                    <stringValue>right</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>orientation</name>
                <value>
                    <stringValue>horizontal</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>showLines</name>
                <value>
                    <stringValue>above</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>actionMode</name>
                <value>
                    <stringValue>navigation</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>buttons</name>
                <value>
                    <stringValue>[{&quot;label&quot;:&quot;Done&quot;,&quot;value&quot;:&quot;Done&quot;,&quot;iconPosition&quot;:&quot;left&quot;,&quot;variant&quot;:&quot;brand&quot;,&quot;iconName&quot;:null,&quot;index&quot;:0}]</stringValue>
                </value>
            </inputParameters>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>237</locationX>
        <locationY>135</locationY>
        <connector>
            <targetReference>Decode_Cell_Attribute</targetReference>
        </connector>
    </start>
    <status>Draft</status>
    <variables>
        <name>apexColFieldDescriptors_usf</name>
        <apexClass>usf3__FieldDescriptor</apexClass>
        <dataType>Apex</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>apexColSelectedFieldDescriptors_usf</name>
        <apexClass>usf3__FieldDescriptor</apexClass>
        <dataType>Apex</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>apexFieldDescriptor</name>
        <apexClass>usf3__FieldDescriptor</apexClass>
        <dataType>Apex</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>colDescriptions</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>colFieldList</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>colIcons</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>colPreSelectedConfigs</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>FlowTableViewDefinition__c</objectType>
    </variables>
    <variables>
        <name>colTitles</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>objConfig</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>FlowTableViewDefinition__c</objectType>
    </variables>
    <variables>
        <name>vConfigCount</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <name>vConfigFields</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>vConfigToggleStatus</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>vDebugMode</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>vDuplicateNameError</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>vEarlyExit</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>vFieldList</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>vLoadedConfig</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>vNewConfigName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue></stringValue>
        </value>
    </variables>
    <variables>
        <name>vRecordList</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>vSelectionMethod</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>vSObject</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>vWizRecordCount</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>10.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>wiz_columnAlignments</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>wiz_columnCellAttribs</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>wiz_columnEdits</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>wiz_columnFields</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>wiz_columnFilters</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>wiz_columnIcons</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>wiz_columnLabels</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>wiz_columnOtherAttribs</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>wiz_columnTypeAttribs</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>wiz_columnWidths</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>wiz_columnWraps</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>wiz_objectName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
