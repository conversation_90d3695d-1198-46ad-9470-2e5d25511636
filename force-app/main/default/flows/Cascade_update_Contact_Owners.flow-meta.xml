<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <assignments>
        <name>Assigning_Contacts</name>
        <label>Assigning Contacts</label>
        <locationX>374</locationX>
        <locationY>600</locationY>
        <assignmentItems>
            <assignToReference>con.Id</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>con.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Contact_List</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Contact_List</name>
        <label>Contact List</label>
        <locationX>374</locationX>
        <locationY>708</locationY>
        <assignmentItems>
            <assignToReference>conList</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>con</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Contacts_present</name>
        <label>Contacts present?</label>
        <locationX>418</locationX>
        <locationY>384</locationY>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Related_Contacts</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <description>Cascade update Contact Owners with the Account Owner for all the contacts related to the account when Account Owner is changed.</description>
    <environments>Default</environments>
    <interviewLabel>Cascade update Contact Owners {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Account- Cascade update Contact Owners</label>
    <loops>
        <name>Loop</name>
        <label>Loop</label>
        <locationX>286</locationX>
        <locationY>492</locationY>
        <collectionReference>Get_Related_Contacts</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assigning_Contacts</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_all_Related_Contacts</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Related_Contacts</name>
        <label>Get Related Contacts</label>
        <locationX>418</locationX>
        <locationY>276</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Contacts_present</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Contact</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_all_Related_Contacts</name>
        <label>Update all Related Contacts</label>
        <locationX>286</locationX>
        <locationY>900</locationY>
        <inputReference>conList</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>OwnerId</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <object>Account</object>
        <recordTriggerType>Update</recordTriggerType>
        <scheduledPaths>
            <connector>
                <targetReference>Get_Related_Contacts</targetReference>
            </connector>
            <pathType>AsyncAfterCommit</pathType>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>InvalidDraft</status>
    <variables>
        <name>con</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Contact</objectType>
    </variables>
    <variables>
        <name>conList</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Contact</objectType>
    </variables>
</Flow>
