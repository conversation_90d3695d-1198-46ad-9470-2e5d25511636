<?xml version="1.0" encoding="UTF-8"?>
<PermissionSet xmlns="http://soap.sforce.com/2006/04/metadata">
    <applicationVisibilities>
        <application>LoggerConsole</application>
        <visible>true</visible>
    </applicationVisibilities>
    <classAccesses>
        <apexClass>ComponentLogger</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>FlowCollectionLogEntry</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>FlowLogEntry</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>FlowLogger</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>FlowRecordLogEntry</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>LogBatchPurgeController</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>LogBatchPurgeScheduler</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>LogBatchPurger</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>LogEntryEventBuilder</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>LogEntryEventStreamController</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>LogEntryMetadataViewerController</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>LogMassDeleteExtension</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>LogMessage</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>LogViewerController</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>Logger</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>LoggerHomeHeaderController</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>LoggerParameter</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>LoggerSObjectMetadata</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>LoggerSettingsController</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>RelatedLogEntriesController</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <customMetadataTypeAccesses>
        <enabled>true</enabled>
        <name>LogEntryDataMaskRule__mdt</name>
    </customMetadataTypeAccesses>
    <customMetadataTypeAccesses>
        <enabled>true</enabled>
        <name>LogEntryTagRule__mdt</name>
    </customMetadataTypeAccesses>
    <customMetadataTypeAccesses>
        <enabled>true</enabled>
        <name>LogStatus__mdt</name>
    </customMetadataTypeAccesses>
    <customMetadataTypeAccesses>
        <enabled>true</enabled>
        <name>LoggerParameter__mdt</name>
    </customMetadataTypeAccesses>
    <customMetadataTypeAccesses>
        <enabled>true</enabled>
        <name>LoggerPlugin__mdt</name>
    </customMetadataTypeAccesses>
    <customPermissions>
        <enabled>true</enabled>
        <name>CanExecuteLogBatchPurger</name>
    </customPermissions>
    <customPermissions>
        <enabled>true</enabled>
        <name>CanModifyLoggerSettings</name>
    </customPermissions>
    <customPermissions>
        <enabled>true</enabled>
        <name>CanViewLogEntryMetadata</name>
    </customPermissions>
    <customSettingAccesses>
        <enabled>true</enabled>
        <name>LoggerSettings__c</name>
    </customSettingAccesses>
    <description>Provides full control of Nebula Logger&apos;s data &amp; custom features</description>
    <fieldPermissions>
        <editable>true</editable>
        <field>LogEntryTag__c.UniqueId__c</field>
        <readable>true</readable>
    </fieldPermissions>
    <fieldPermissions>
        <editable>true</editable>
        <field>LogEntry__c.EntryScenarioName__c</field>
        <readable>true</readable>
    </fieldPermissions>
    <fieldPermissions>
        <editable>true</editable>
        <field>LogEntry__c.EntryScenario__c</field>
        <readable>true</readable>
    </fieldPermissions>
    <fieldPermissions>
        <editable>true</editable>
        <field>Log__c.Comments__c</field>
        <readable>true</readable>
    </fieldPermissions>
    <fieldPermissions>
        <editable>true</editable>
        <field>Log__c.Issue__c</field>
        <readable>true</readable>
    </fieldPermissions>
    <fieldPermissions>
        <editable>true</editable>
        <field>Log__c.LogPurgeAction__c</field>
        <readable>true</readable>
    </fieldPermissions>
    <fieldPermissions>
        <editable>true</editable>
        <field>Log__c.LogRetentionDate__c</field>
        <readable>true</readable>
    </fieldPermissions>
    <fieldPermissions>
        <editable>true</editable>
        <field>Log__c.Priority__c</field>
        <readable>true</readable>
    </fieldPermissions>
    <fieldPermissions>
        <editable>true</editable>
        <field>Log__c.Scenario__c</field>
        <readable>true</readable>
    </fieldPermissions>
    <fieldPermissions>
        <editable>true</editable>
        <field>Log__c.Status__c</field>
        <readable>true</readable>
    </fieldPermissions>
    <fieldPermissions>
        <editable>true</editable>
        <field>Log__c.TransactionScenarioName__c</field>
        <readable>true</readable>
    </fieldPermissions>
    <fieldPermissions>
        <editable>true</editable>
        <field>Log__c.TransactionScenarioText__c</field>
        <readable>true</readable>
    </fieldPermissions>
    <fieldPermissions>
        <editable>true</editable>
        <field>LoggerTag__c.UniqueId__c</field>
        <readable>true</readable>
    </fieldPermissions>
    <hasActivationRequired>false</hasActivationRequired>
    <label>Nebula Logger: Admin</label>
    <objectPermissions>
        <allowCreate>false</allowCreate>
        <allowDelete>false</allowDelete>
        <allowEdit>false</allowEdit>
        <allowRead>true</allowRead>
        <modifyAllRecords>false</modifyAllRecords>
        <object>LogEntryEvent__e</object>
        <viewAllRecords>false</viewAllRecords>
    </objectPermissions>
    <objectPermissions>
        <allowCreate>true</allowCreate>
        <allowDelete>true</allowDelete>
        <allowEdit>true</allowEdit>
        <allowRead>true</allowRead>
        <modifyAllRecords>true</modifyAllRecords>
        <object>LogEntryTag__c</object>
        <viewAllRecords>true</viewAllRecords>
    </objectPermissions>
    <objectPermissions>
        <allowCreate>false</allowCreate>
        <allowDelete>false</allowDelete>
        <allowEdit>false</allowEdit>
        <allowRead>true</allowRead>
        <modifyAllRecords>false</modifyAllRecords>
        <object>LogEntry__c</object>
        <viewAllRecords>true</viewAllRecords>
    </objectPermissions>
    <objectPermissions>
        <allowCreate>false</allowCreate>
        <allowDelete>true</allowDelete>
        <allowEdit>true</allowEdit>
        <allowRead>true</allowRead>
        <modifyAllRecords>true</modifyAllRecords>
        <object>Log__c</object>
        <viewAllRecords>true</viewAllRecords>
    </objectPermissions>
    <objectPermissions>
        <allowCreate>true</allowCreate>
        <allowDelete>true</allowDelete>
        <allowEdit>true</allowEdit>
        <allowRead>true</allowRead>
        <modifyAllRecords>true</modifyAllRecords>
        <object>LoggerScenario__c</object>
        <viewAllRecords>true</viewAllRecords>
    </objectPermissions>
    <objectPermissions>
        <allowCreate>true</allowCreate>
        <allowDelete>true</allowDelete>
        <allowEdit>true</allowEdit>
        <allowRead>true</allowRead>
        <modifyAllRecords>true</modifyAllRecords>
        <object>LoggerTag__c</object>
        <viewAllRecords>true</viewAllRecords>
    </objectPermissions>
    <pageAccesses>
        <apexPage>LogMassDelete</apexPage>
        <enabled>true</enabled>
    </pageAccesses>
    <tabSettings>
        <tab>LogBatchPurge</tab>
        <visibility>Visible</visibility>
    </tabSettings>
    <tabSettings>
        <tab>LogEntryEventStream</tab>
        <visibility>Visible</visibility>
    </tabSettings>
    <tabSettings>
        <tab>LogEntryTag__c</tab>
        <visibility>Visible</visibility>
    </tabSettings>
    <tabSettings>
        <tab>LogEntry__c</tab>
        <visibility>Visible</visibility>
    </tabSettings>
    <tabSettings>
        <tab>Log__c</tab>
        <visibility>Visible</visibility>
    </tabSettings>
    <tabSettings>
        <tab>LoggerScenario__c</tab>
        <visibility>Visible</visibility>
    </tabSettings>
    <tabSettings>
        <tab>LoggerSettings</tab>
        <visibility>Visible</visibility>
    </tabSettings>
    <tabSettings>
        <tab>LoggerTag__c</tab>
        <visibility>Visible</visibility>
    </tabSettings>
</PermissionSet>
