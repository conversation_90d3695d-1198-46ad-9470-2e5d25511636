<?xml version="1.0" encoding="UTF-8"?>
<PermissionSet xmlns="http://soap.sforce.com/2006/04/metadata">
    <classAccesses>
        <apexClass>ComponentLogger</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>FlowCollectionLogEntry</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>FlowLogEntry</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>FlowRecordLogEntry</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>LogMessage</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <classAccesses>
        <apexClass>Logger</apexClass>
        <enabled>true</enabled>
    </classAccesses>
    <description>Provides explicit access to metadata needed to generate (but not access) logging data. This typically is not needed - but in some situations it&apos;s required, such as logging in lightning components within an Experience Cloud site.</description>
    <hasActivationRequired>false</hasActivationRequired>
    <label>Nebula Logger: Log Creator</label>
    <objectPermissions>
        <allowCreate>true</allowCreate>
        <allowDelete>false</allowDelete>
        <allowEdit>false</allowEdit>
        <allowRead>true</allowRead>
        <modifyAllRecords>false</modifyAllRecords>
        <object>LogEntryEvent__e</object>
        <viewAllRecords>false</viewAllRecords>
    </objectPermissions>
</PermissionSet>
