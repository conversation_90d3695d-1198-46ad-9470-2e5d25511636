<?xml version="1.0" encoding="UTF-8"?>
<StandardValueSet xmlns="http://soap.sforce.com/2006/04/metadata">
    <sorted>false</sorted>
    <standardValue>
        <fullName>Existing Business</fullName>
        <default>false</default>
        <label>Existing Business</label>
    </standardValue>
    <standardValue>
        <fullName>New Business</fullName>
        <default>false</default>
        <label>New Business</label>
    </standardValue>
    <standardValue>
        <fullName>Short Term Loan &lt; 24M</fullName>
        <default>false</default>
        <label>Short Term Loan &lt; 24M</label>
    </standardValue>
    <standardValue>
        <fullName>Term Loan &gt; 24M</fullName>
        <default>false</default>
        <label>Term Loan &gt; 24M</label>
    </standardValue>
    <standardValue>
        <fullName>Bridging Loan</fullName>
        <default>false</default>
        <label>Bridging Loan</label>
    </standardValue>
    <standardValue>
        <fullName>Minimum term lease</fullName>
        <default>false</default>
        <label>Minimum term lease</label>
    </standardValue>
    <standardValue>
        <fullName>Fixed term lease</fullName>
        <default>false</default>
        <label>Fixed term lease</label>
    </standardValue>
    <standardValue>
        <fullName>Hire purchase</fullName>
        <default>false</default>
        <label>Hire purchase</label>
    </standardValue>
    <standardValue>
        <fullName>Hire purchase balloon</fullName>
        <default>false</default>
        <label>Hire purchase balloon</label>
    </standardValue>
    <standardValue>
        <fullName>Hire purchase (non-regulated)</fullName>
        <default>false</default>
        <label>Hire purchase (non-regulated)</label>
    </standardValue>
    <standardValue>
        <fullName>Conditional Sale (Regulated HP)</fullName>
        <default>false</default>
        <label>Conditional Sale (Regulated HP)</label>
    </standardValue>
    <standardValue>
        <fullName>Finance Lease</fullName>
        <default>false</default>
        <label>Finance Lease</label>
    </standardValue>
    <standardValue>
        <fullName>Sales &amp; Leaseback</fullName>
        <default>false</default>
        <label>Sales &amp; Leaseback</label>
    </standardValue>
    <standardValue>
        <fullName>Sales &amp; HP back</fullName>
        <default>false</default>
        <label>Sales &amp; HP back</label>
    </standardValue>
    <standardValue>
        <fullName>Re-Finance</fullName>
        <default>false</default>
        <label>Re-Finance</label>
    </standardValue>
    <standardValue>
        <fullName>Sale &amp; Leaseback</fullName>
        <default>false</default>
        <label>Sale &amp; Leaseback</label>
    </standardValue>
    <standardValue>
        <fullName>Sale &amp; HP back</fullName>
        <default>false</default>
        <label>Sale &amp; HP back</label>
    </standardValue>
    <standardValue>
        <fullName>VAT Loan</fullName>
        <default>false</default>
        <label>VAT Loan</label>
    </standardValue>
    <standardValue>
        <fullName>Buy to Let</fullName>
        <default>false</default>
        <label>Buy to Let</label>
    </standardValue>
    <standardValue>
        <fullName>Foreign Exchange</fullName>
        <default>false</default>
        <label>Foreign Exchange</label>
    </standardValue>
    <standardValue>
        <fullName>Introduction Fee</fullName>
        <default>false</default>
        <label>Introduction Fee</label>
    </standardValue>
    <standardValue>
        <fullName>Invoice Finance</fullName>
        <default>false</default>
        <label>Invoice Finance</label>
    </standardValue>
    <standardValue>
        <fullName>Mortgage</fullName>
        <default>false</default>
        <label>Mortgage</label>
    </standardValue>
    <standardValue>
        <fullName>Flexipay</fullName>
        <default>false</default>
        <label>Flexipay</label>
    </standardValue>
    <standardValue>
        <fullName>Cashback Credit Card</fullName>
        <default>false</default>
        <label>Cashback Credit Card</label>
    </standardValue>
</StandardValueSet>
