<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DetailAuthorizationFormText</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FullAuthorizationFormUrl</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SummaryAuthFormText</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ContentDocumentId</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>AuthorizationFormId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Locale</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <miniLayout>
        <fields>DetailAuthorizationFormText</fields>
    </miniLayout>
    <relatedLists>
        <fields>Name</fields>
        <fields>ConsentGiver</fields>
        <fields>Status</fields>
        <relatedList>AuthorizationFormConsentRelatedList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedEntityHistoryList</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
