<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>Event Details</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>SessionHijackingEventNumber</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>EventIdentifier</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Summary</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>EventDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Score</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>Event Data</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CurrentIp</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CurrentPlatform</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CurrentScreen</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CurrentWindow</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CurrentUserAgent</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SecurityEventData</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PreviousIp</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PreviousPlatform</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PreviousScreen</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PreviousWindow</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PreviousUserAgent</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>UserId</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SourceIp</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <relatedLists>
        <fields>Name</fields>
        <fields>Response</fields>
        <relatedList>RelatedFeedback</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
