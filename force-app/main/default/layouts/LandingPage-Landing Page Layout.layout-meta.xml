<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>CampaignId</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Engagement Metrics</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalViews</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>UniqueViews</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalFormSubmissions</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>UniqueFormSubmissions</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FormSubmissionRate</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalTrackedLinkClicks</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>UniqueTrackedLinkClicks</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalFormErrors</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>UniqueFormErrors</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FormErrorRate</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Publication Details</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Status</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ContentLastSavedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LastPublishedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsHideFromSearchEngineIndex</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FallbackUrl</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>PublicLink</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ContentLastSaved</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastPublished</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>VanityUrl</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <relatedLists>
        <fields>TASK.SUBJECT</fields>
        <fields>TASK.WHO_NAME</fields>
        <fields>ACTIVITY.TASK</fields>
        <fields>TASK.DUE_DATE</fields>
        <fields>TASK.STATUS</fields>
        <fields>TASK.PRIORITY</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <relatedList>RelatedActivityList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>TASK.SUBJECT</fields>
        <fields>TASK.WHO_NAME</fields>
        <fields>ACTIVITY.TASK</fields>
        <fields>TASK.DUE_DATE</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <fields>TASK.LAST_UPDATE</fields>
        <relatedList>RelatedHistoryList</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
