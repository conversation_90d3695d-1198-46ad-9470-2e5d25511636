<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>DataDotComAccountInsights</excludeButtons>
    <excludeButtons>DataDotCom<PERSON>lean</excludeButtons>
    <excludeButtons>DataDotComCompanyHierarchy</excludeButtons>
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Fields</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AnnualRevenue</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Rating</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Type</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Phone</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Turnover__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SIC_Code__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Customer_Reference__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ParentId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Industry</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>NumberOfEmployees</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Website</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Last_Task_Activity_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Last_Event_Activity_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Last_Non_Marketing_Activity_Date__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Lender Details</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FCA_Authorisation_Number__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>comp_house__Company_Number__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>comp_house__Company_Creation_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>VAT_Number__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns/>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Breadwinner</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Bread_Winner__BW_Account_Status__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Bread_Winner__Total_Amount_Overdue__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Bread_Winner__Total_Amount_Due__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Bread_Winner__Total_Amount_Invoiced__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Bread_Winner__Total_Amount_Paid__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Bread_Winner__Total_Amount_Credit__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Bread_Winner__Total_Unallocated_Credit__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Bread_Winner__Total_Draft_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Breadwinner Charts</label>
        <layoutColumns>
            <layoutItems>
                <height>200</height>
                <page>Bread_Winner__BreadwinnerAccountCharts</page>
                <showLabel>false</showLabel>
                <showScrollbars>false</showScrollbars>
                <width>100%</width>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Address Details</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BillingAddress</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ShippingAddress</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Description</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Lead_Creation_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
        <platformActionListItems>
            <actionName>Account.comp_house__Company_House</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>0</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Account.New_Opportunity</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>1</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.TextPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>2</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Email</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>3</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.ContentPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>4</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewEvent</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>5</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewTask</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>6</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>LogACall</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>7</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewContact</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>8</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewOpportunity</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>9</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewLead</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>10</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.ContentNote</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>11</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.PollPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>12</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>CreateCallList</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>13</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>RecordShareHierarchy</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>14</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>CreateSurveyInvitation</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>15</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>CreateSalesSummaries</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>16</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>SendSurveyInvitation</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>17</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>JigsawSearch</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>18</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>CreateAccountChannel</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>19</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Delete</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>20</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>AddToActionableList</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>21</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>IncludeOffline</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>22</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Edit</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>23</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>AccountHierarchy</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>24</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>PartnerScorecard</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>25</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>AccountAddToCampaign</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>26</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>XClean</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>27</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Submit</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>28</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Share</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>29</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Entitlements</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>30</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>ChangeOwnerOne</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>31</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>ChangeRecordType</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>32</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>BrowseCatalogs</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>33</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>CreatePartnerChannelQuickAction</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>34</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>CallHighlightAction</actionName>
            <actionType>ProductivityAction</actionType>
            <sortOrder>35</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>SmsHighlightAction</actionName>
            <actionType>ProductivityAction</actionType>
            <sortOrder>36</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>EmailHighlightAction</actionName>
            <actionType>ProductivityAction</actionType>
            <sortOrder>37</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>WebsiteHighlightAction</actionName>
            <actionType>ProductivityAction</actionType>
            <sortOrder>38</sortOrder>
        </platformActionListItems>
    </platformActionList>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Bread_Winner__Status_Flag__c</fields>
        <fields>Bread_Winner__Invoice_Date__c</fields>
        <fields>Bread_Winner__Due_Date__c</fields>
        <fields>Bread_Winner__Total__c</fields>
        <fields>Bread_Winner__Amount_Overdue__c</fields>
        <fields>Bread_Winner__Amount_Due__c</fields>
        <fields>Bread_Winner__Amount_Credit__c</fields>
        <fields>Bread_Winner__Amount_Paid__c</fields>
        <fields>Bread_Winner__Status__c</fields>
        <relatedList>Bread_Winner__Invoice__c.Bread_Winner__Account__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>OPPORTUNITY.NAME</fields>
        <fields>OPPORTUNITY.STAGE_NAME</fields>
        <fields>OPPORTUNITY.AMOUNT</fields>
        <fields>OPPORTUNITY.CLOSE_DATE</fields>
        <fields>OPPORTUNITY.CREATED_DATE</fields>
        <relatedList>RelatedOpportunityList</relatedList>
        <sortField>OPPORTUNITY.CREATED_DATE</sortField>
        <sortOrder>Desc</sortOrder>
    </relatedLists>
    <relatedLists>
        <fields>FULL_NAME</fields>
        <fields>CONTACT.TITLE</fields>
        <fields>CONTACT.EMAIL</fields>
        <fields>CONTACT.PHONE1</fields>
        <relatedList>RelatedContactList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedContentNoteList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedFileList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>FULL_NAME</fields>
        <fields>LEAD.COMPANY</fields>
        <fields>LEAD.PHONE</fields>
        <relatedList>Lead.Account__c</relatedList>
    </relatedLists>
    <relatedLists>
        <excludeButtons>New</excludeButtons>
        <fields>NAME</fields>
        <fields>Bread_Winner__CompanyName__c</fields>
        <fields>Bread_Winner__Status__c</fields>
        <fields>Bread_Winner__FirstName__c</fields>
        <fields>Bread_Winner__LastName__c</fields>
        <fields>Bread_Winner__EmailAddress__c</fields>
        <fields>Bread_Winner__AttentionTo__c</fields>
        <fields>Bread_Winner__DefaultCurrency__c</fields>
        <fields>Bread_Winner__Xero_Org_Name__c</fields>
        <relatedList>Bread_Winner__Breadwinner_Account_Connection__c.Bread_Winner__Salesforce_Account__c</relatedList>
    </relatedLists>
    <relatedObjects>ParentId</relatedObjects>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00h4K000004hhY3</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
