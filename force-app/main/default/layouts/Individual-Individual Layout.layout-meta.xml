<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BirthDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>HasOptedOutProcessing</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>HasOptedOutSolicit</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SendIndividualData</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CanStorePiiElsewhere</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>HasOptedOutGeoTracking</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>HasOptedOutProfiling</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>HasOptedOutTracking</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ShouldForget</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IndividualsAge</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedDate</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedDate</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <relatedLists>
        <relatedList>RelatedEntityHistoryList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>FULL_NAME</fields>
        <fields>CONTACT.TITLE</fields>
        <fields>CONTACT.EMAIL</fields>
        <fields>CONTACT.PHONE1</fields>
        <relatedList>RelatedContactListIndividual</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>FULL_NAME</fields>
        <fields>LEAD.TITLE</fields>
        <fields>LEAD.EMAIL</fields>
        <fields>LEAD.PHONE</fields>
        <relatedList>RelatedLeadListIndividual</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>EmailAddress</fields>
        <fields>EmailMailBox</fields>
        <fields>EmailDomain</fields>
        <relatedList>ContactPointEmails</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>TelephoneNumber</fields>
        <fields>PhoneType</fields>
        <fields>FormattedNationalPhoneNumber</fields>
        <fields>IsPersonalPhone</fields>
        <relatedList>ContactPointPhones</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
