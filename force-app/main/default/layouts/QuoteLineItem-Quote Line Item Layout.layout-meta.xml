<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>QuoteLineItem Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LineNumber</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Product2Id</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>QuoteId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ListPrice</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>UnitPrice</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Quantity</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Subtotal</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Discount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalPrice</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <multilineLayoutFields>Product2Id</multilineLayoutFields>
    <multilineLayoutFields>ListPrice</multilineLayoutFields>
    <multilineLayoutFields>UnitPrice</multilineLayoutFields>
    <multilineLayoutFields>Quantity</multilineLayoutFields>
    <multilineLayoutFields>Subtotal</multilineLayoutFields>
    <multilineLayoutFields>Discount</multilineLayoutFields>
    <multilineLayoutFields>TotalPrice</multilineLayoutFields>
    <multilineLayoutFields>QuoteId</multilineLayoutFields>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
