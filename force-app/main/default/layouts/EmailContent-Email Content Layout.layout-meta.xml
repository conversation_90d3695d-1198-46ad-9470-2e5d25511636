<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TemplateId</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Description</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Message Content</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Subject</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>HtmlBody</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TextBody</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Engagement Metrics</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>DeliveryRate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalSent</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalDelivered</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>OpenRate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ClickToOpenRatio</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>UniqueOpens</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>UniqueTrackedLinkClicks</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalOpens</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalTrackedLinkClicks</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ClickThroughRate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>UniqueClickThroughRate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>OptOutRate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>SpamComplaintRate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>UniqueOptOuts</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalSpamComplaints</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalSoftBounced</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalHardBounced</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <relatedLists>
        <fields>Name</fields>
        <fields>TotalSent</fields>
        <fields>UniqueClickThroughRate</fields>
        <fields>Campaign</fields>
        <relatedList>Sends</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
