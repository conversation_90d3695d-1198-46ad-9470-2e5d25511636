<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>FlowLabel</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AssociatedRecordId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CreatedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FlowCategory</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>ApiName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>FlowType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CreatedDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LastModifiedDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FlowSubcategory</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <relatedLists>
        <fields>VersionNumber</fields>
        <fields>ProgressStatus</fields>
        <fields>LastModifiedDate</fields>
        <fields>Entries</fields>
        <fields>Exits</fields>
        <fields>Errors</fields>
        <fields>Name</fields>
        <fields>CreatedDate</fields>
        <fields>RunInMode</fields>
        <fields>ApiVersion</fields>
        <fields>CapacityCategory</fields>
        <fields>AreMetricsLoggedToDataCloud</fields>
        <relatedList>FlowRecordVersions</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>Name</fields>
        <fields>FlowRecordVersion</fields>
        <fields>ScheduledDate</fields>
        <fields>Enqueued</fields>
        <fields>Entries</fields>
        <fields>Exits</fields>
        <fields>Errors</fields>
        <fields>ProgressStatus</fields>
        <fields>ErrorDetail</fields>
        <relatedList>FlowRecordVersionOccurrences</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
