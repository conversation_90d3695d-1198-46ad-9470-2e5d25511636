<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <feedLayout>
        <autocollapsePublisher>false</autocollapsePublisher>
        <compactFeed>false</compactFeed>
        <feedFilterPosition>LeftFixed</feedFilterPosition>
        <feedFilters>
            <feedFilterType>AllUpdates</feedFilterType>
        </feedFilters>
        <feedFilters>
            <feedFilterType>FeedItemType</feedFilterType>
            <feedItemType>EmailMessageEvent</feedItemType>
        </feedFilters>
        <feedFilters>
            <feedFilterType>FeedItemType</feedFilterType>
            <feedItemType>CallLogPost</feedItemType>
        </feedFilters>
        <feedFilters>
            <feedFilterType>FeedItemType</feedFilterType>
            <feedItemType>TextPost</feedItemType>
        </feedFilters>
        <feedFilters>
            <feedFilterType>FeedItemType</feedFilterType>
            <feedItemType>ChangeStatusPost</feedItemType>
        </feedFilters>
        <feedFilters>
            <feedFilterType>FeedItemType</feedFilterType>
            <feedItemType>ActivityEvent</feedItemType>
        </feedFilters>
        <fullWidthFeed>false</fullWidthFeed>
        <hideSidebar>false</hideSidebar>
        <highlightExternalFeedItems>false</highlightExternalFeedItems>
        <rightComponents>
            <componentType>HelpAndToolLinks</componentType>
        </rightComponents>
        <rightComponents>
            <componentType>CustomButtons</componentType>
        </rightComponents>
        <rightComponents>
            <componentType>Following</componentType>
        </rightComponents>
        <rightComponents>
            <componentType>Followers</componentType>
        </rightComponents>
        <rightComponents>
            <componentType>CustomLinks</componentType>
        </rightComponents>
        <useInlineFiltersInConsole>false</useInlineFiltersInConsole>
    </feedLayout>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ContactId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AccountId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Status</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Priority</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ContactPhone</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ContactEmail</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Origin</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Subject</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SuppliedEmail</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns/>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <miniLayout>
        <fields>OwnerId</fields>
        <fields>ContactId</fields>
        <fields>AccountId</fields>
        <fields>Status</fields>
        <fields>Priority</fields>
        <fields>ContactPhone</fields>
        <fields>ContactEmail</fields>
        <fields>Origin</fields>
        <fields>Subject</fields>
        <fields>Description</fields>
        <fields>SuppliedEmail</fields>
    </miniLayout>
    <relatedObjects>ContactId</relatedObjects>
    <relatedObjects>AccountId</relatedObjects>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00ha0000004ydtW</masterLabel>
        <sizeX>3</sizeX>
        <sizeY>4</sizeY>
        <summaryLayoutItems>
            <field>ContactId</field>
            <posX>0</posX>
            <posY>0</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>AccountId</field>
            <posX>0</posX>
            <posY>1</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>ContactPhone</field>
            <posX>0</posX>
            <posY>2</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>CaseNumber</field>
            <posX>1</posX>
            <posY>0</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>CreatedDate</field>
            <posX>1</posX>
            <posY>1</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>Subject</field>
            <posX>1</posX>
            <posY>2</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>Description</field>
            <posX>1</posX>
            <posY>3</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>Status</field>
            <posX>2</posX>
            <posY>0</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>Priority</field>
            <posX>2</posX>
            <posY>1</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>OwnerId</field>
            <posX>2</posX>
            <posY>2</posY>
        </summaryLayoutItems>
        <summaryLayoutStyle>CaseInteraction</summaryLayoutStyle>
    </summaryLayout>
</Layout>
