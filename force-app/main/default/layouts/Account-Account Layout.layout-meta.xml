<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>DataDotComCompanyHierarchy</excludeButtons>
    <excludeButtons>DisableCustomerPortalAccount</excludeButtons>
    <excludeButtons>DisablePartnerPortalAccount</excludeButtons>
    <excludeButtons>IncludeOffline</excludeButtons>
    <excludeButtons>OpenSlackRecordChannel</excludeButtons>
    <excludeButtons>PrintableView</excludeButtons>
    <excludeButtons>Share</excludeButtons>
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Account Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Registration_Number__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Website</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Trading_Since__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BillingAddress</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Turnover__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Marketing_Source__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Lead_Source__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Networth__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Debtors__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Company_Legal_Entity__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Relation_to_Address__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Customer_Reference__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Industry</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>NumberOfEmployees</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>VAT_Number__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Source__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Last_Task_Activity_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Last_Event_Activity_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Last_Non_Marketing_Activity_Date__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Contact Details</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Title__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Forename__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Surname__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Email__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Job_Title__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Home_Owner__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Outstanding_Mortgage__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ShippingAddress</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Tenant__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Phone</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Mobile__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Property_Value__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Additional_Contact_Title__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Additional_Contact_Name__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Additional_Contact_Phone__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Additional_Contact_Mobile__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Additional_Contact_Email__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Breadwinner</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Bread_Winner__BW_Account_Status__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Bread_Winner__Total_Amount_Overdue__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Bread_Winner__Total_Amount_Due__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Bread_Winner__Total_Amount_Invoiced__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Bread_Winner__Total_Amount_Paid__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Bread_Winner__Total_Amount_Credit__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Bread_Winner__Total_Unallocated_Credit__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Bread_Winner__Total_Draft_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Breadwinner Charts</label>
        <layoutColumns>
            <layoutItems>
                <height>200</height>
                <page>Bread_Winner__BreadwinnerAccountCharts</page>
                <showLabel>false</showLabel>
                <showScrollbars>false</showScrollbars>
                <width>100%</width>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Lead_Creation_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ParentId</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
        <platformActionListItems>
            <actionName>Account.comp_house__Company_House</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>0</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Account.New_Opportunity</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>1</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Edit</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>2</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Delete</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>3</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>LogACall</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>4</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewTask</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>5</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewEvent</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>6</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.ContentNote</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>7</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.TextPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>8</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.ContentPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>9</sortOrder>
        </platformActionListItems>
    </platformActionList>
    <quickActionList>
        <quickActionListItems>
            <quickActionName>FeedItem.TextPost</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>FeedItem.ContentPost</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>LogACall</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>NewTask</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>NewEvent</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>FeedItem.LinkPost</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>FeedItem.PollPost</quickActionName>
        </quickActionListItems>
    </quickActionList>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Bread_Winner__Status_Flag__c</fields>
        <fields>Bread_Winner__Invoice_Date__c</fields>
        <fields>Bread_Winner__Due_Date__c</fields>
        <fields>Bread_Winner__Total__c</fields>
        <fields>Bread_Winner__Amount_Overdue__c</fields>
        <fields>Bread_Winner__Amount_Due__c</fields>
        <fields>Bread_Winner__Amount_Credit__c</fields>
        <fields>Bread_Winner__Amount_Paid__c</fields>
        <fields>Bread_Winner__Status__c</fields>
        <relatedList>Bread_Winner__Invoice__c.Bread_Winner__Account__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>OPPORTUNITY.NAME</fields>
        <fields>OPPORTUNITY.STAGE_NAME</fields>
        <fields>OPPORTUNITY.AMOUNT</fields>
        <fields>OPPORTUNITY.CLOSE_DATE</fields>
        <fields>OPPORTUNITY.CREATED_DATE</fields>
        <relatedList>RelatedOpportunityList</relatedList>
        <sortField>OPPORTUNITY.CREATED_DATE</sortField>
        <sortOrder>Desc</sortOrder>
    </relatedLists>
    <relatedLists>
        <fields>FULL_NAME</fields>
        <fields>CONTACT.TITLE</fields>
        <fields>CONTACT.EMAIL</fields>
        <fields>CONTACT.PHONE1</fields>
        <relatedList>RelatedContactList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedFileList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedContentNoteList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedNoteList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>TASK.SUBJECT</fields>
        <fields>TASK.WHO_NAME</fields>
        <fields>Notes__c</fields>
        <fields>TASK.DUE_DATE</fields>
        <fields>TASK.STATUS</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <fields>TASK.WHAT_NAME</fields>
        <relatedList>RelatedActivityList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>TASK.SUBJECT</fields>
        <fields>TASK.WHO_NAME</fields>
        <fields>ACTIVITY.TASK</fields>
        <fields>TASK.DUE_DATE</fields>
        <fields>EVENT.LOCATION</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <fields>TASK.WHAT_NAME</fields>
        <relatedList>RelatedHistoryList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>ACCOUNT.NAME</fields>
        <fields>ACCOUNT.ADDRESS1_CITY</fields>
        <fields>ACCOUNT.PHONE1</fields>
        <relatedList>Account.Source__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>FULL_NAME</fields>
        <fields>LEAD.COMPANY</fields>
        <fields>LEAD.PHONE</fields>
        <relatedList>Lead.Account__c</relatedList>
    </relatedLists>
    <relatedLists>
        <excludeButtons>New</excludeButtons>
        <fields>NAME</fields>
        <fields>Bread_Winner__CompanyName__c</fields>
        <fields>Bread_Winner__Status__c</fields>
        <fields>Bread_Winner__FirstName__c</fields>
        <fields>Bread_Winner__LastName__c</fields>
        <fields>Bread_Winner__EmailAddress__c</fields>
        <fields>Bread_Winner__AttentionTo__c</fields>
        <fields>Bread_Winner__DefaultCurrency__c</fields>
        <fields>Bread_Winner__Xero_Org_Name__c</fields>
        <relatedList>Bread_Winner__Breadwinner_Account_Connection__c.Bread_Winner__Salesforce_Account__c</relatedList>
    </relatedLists>
    <relatedObjects>ParentId</relatedObjects>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00h4K000001XnXJ</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
