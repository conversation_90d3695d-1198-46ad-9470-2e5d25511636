<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>OrchestrationLabel</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CreatedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>ApiName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>OrchestrationType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CreatedDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LastModifiedDate</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <relatedLists>
        <fields>VersionNumber</fields>
        <fields>Status</fields>
        <fields>LastModifiedDate</fields>
        <fields>Name</fields>
        <relatedList>FlowOrchestrationVersions</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>Name</fields>
        <fields>Status</fields>
        <fields>CreatedDate</fields>
        <fields>Duration</fields>
        <fields>CurrentStage</fields>
        <fields>TriggeringRecord</fields>
        <relatedList>FlowOrchestrationRuns</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
