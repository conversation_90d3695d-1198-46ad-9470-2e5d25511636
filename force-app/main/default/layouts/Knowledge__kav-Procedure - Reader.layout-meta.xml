<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <field>Title</field>
            </layoutItems>
            <layoutItems>
                <field>UrlName</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <field>Summary</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Article Details</label>
        <layoutColumns>
            <layoutItems>
                <field>Procedure_Audience__c</field>
            </layoutItems>
            <layoutItems>
                <field>Procedure_Purpose__c</field>
            </layoutItems>
            <layoutItems>
                <field>Procedure_Warnings__c</field>
            </layoutItems>
            <layoutItems>
                <field>Procedure_Steps__c</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Properties</label>
        <layoutColumns>
            <layoutItems>
                <field>ArticleCreatedDate</field>
            </layoutItems>
            <layoutItems>
                <field>CreatedById</field>
            </layoutItems>
            <layoutItems>
                <field>LastModifiedById</field>
            </layoutItems>
            <layoutItems>
                <field>LastPublishedDate</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <field>IsVisibleInApp</field>
            </layoutItems>
            <layoutItems>
                <field>IsVisibleInCsp</field>
            </layoutItems>
            <layoutItems>
                <field>IsVisibleInPrm</field>
            </layoutItems>
            <layoutItems>
                <field>IsVisibleInPkb</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
        <platformActionListItems>
            <actionName>FeedItem.TextPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>0</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.PollPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>1</sortOrder>
        </platformActionListItems>
    </platformActionList>
</Layout>
