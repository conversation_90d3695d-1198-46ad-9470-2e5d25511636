<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>Share</excludeButtons>
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Campaign Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsActive</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ParentId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Type</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Status</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>StartDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EndDate</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Description Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Planning</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>NumberSent</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExpectedResponse</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BudgetedCost</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ActualCost</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExpectedRevenue</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Campaign Statistics</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>NumberOfResponses</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>NumberOfLeads</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>NumberOfConvertedLeads</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>NumberOfContacts</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>NumberOfOpportunities</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>NumberOfWonOpportunities</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>AmountAllOpportunities</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>AmountWonOpportunities</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns>
            <layoutItems>
                <customLink>ViewAllCampaignMembers</customLink>
            </layoutItems>
            <layoutItems>
                <customLink>ViewCampaignInfluenceReport</customLink>
            </layoutItems>
        </layoutColumns>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Additional Information</label>
        <layoutColumns/>
        <layoutColumns/>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Other Information</label>
        <layoutColumns/>
        <layoutColumns/>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
        <platformActionListItems>
            <actionName>Edit</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>0</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Delete</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>1</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Clone</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>2</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Campaign.Child_Campaign</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>3</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>LogACall</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>4</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewTask</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>5</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewEvent</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>6</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.TextPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>7</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.ContentPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>8</sortOrder>
        </platformActionListItems>
    </platformActionList>
    <quickActionList>
        <quickActionListItems>
            <quickActionName>FeedItem.TextPost</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>FeedItem.ContentPost</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>FeedItem.LinkPost</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>FeedItem.PollPost</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Campaign.Child_Campaign</quickActionName>
        </quickActionListItems>
    </quickActionList>
    <relatedContent>
        <relatedContentItems>
            <layoutItem>
                <behavior>Readonly</behavior>
                <field>ParentId</field>
            </layoutItem>
        </relatedContentItems>
    </relatedContent>
    <relatedLists>
        <fields>MEMBER_TYPE</fields>
        <fields>STATUS</fields>
        <fields>FULL_NAME</fields>
        <fields>TITLE</fields>
        <fields>FIRST_NAME</fields>
        <fields>LAST_NAME</fields>
        <fields>COMPANY</fields>
        <relatedList>RelatedCampaignMemberList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>CAMPAIGN.NAME</fields>
        <fields>CAMPAIGN.TOTAL_NUM_LEADS</fields>
        <fields>CAMPAIGN.TOT_CONVERTED_LEADS</fields>
        <fields>CAMPAIGN.TOT_CONTACTS</fields>
        <fields>CAMPAIGN.TOT_OPPORTUNITIES</fields>
        <fields>CAMPAIGN.TOT_WON_OPPORTUNITIES</fields>
        <fields>TOT_AMOUNT_WON</fields>
        <fields>TOT_AMOUNT_ALL</fields>
        <fields>CAMPAIGN.TOT_NUM_RESPONSES</fields>
        <fields>TOT_NUM_SENT</fields>
        <fields>TOT_EXP_REVENUE</fields>
        <fields>TOT_BUDGETED_COST</fields>
        <fields>TOT_ACTUAL_COST</fields>
        <relatedList>RelatedCampaignHierarchyList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedFileList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedGroupsListOnRecord</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
