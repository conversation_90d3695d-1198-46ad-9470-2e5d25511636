<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <emailDefault>false</emailDefault>
    <feedLayout>
        <autocollapsePublisher>true</autocollapsePublisher>
        <compactFeed>true</compactFeed>
        <feedFilterPosition>LeftFixed</feedFilterPosition>
        <feedFilters>
            <feedFilterType>AllUpdates</feedFilterType>
        </feedFilters>
        <feedFilters>
            <feedFilterType>FeedItemType</feedFilterType>
            <feedItemType>EmailMessageEvent</feedItemType>
        </feedFilters>
        <feedFilters>
            <feedFilterType>FeedItemType</feedFilterType>
            <feedItemType>CallLogPost</feedItemType>
        </feedFilters>
        <feedFilters>
            <feedFilterType>FeedItemType</feedFilterType>
            <feedItemType>TextPost</feedItemType>
        </feedFilters>
        <feedFilters>
            <feedFilterType>FeedItemType</feedFilterType>
            <feedItemType>ChangeStatusPost</feedItemType>
        </feedFilters>
        <feedFilters>
            <feedFilterType>FeedItemType</feedFilterType>
            <feedItemType>ActivityEvent</feedItemType>
        </feedFilters>
        <fullWidthFeed>true</fullWidthFeed>
        <hideSidebar>true</hideSidebar>
        <highlightExternalFeedItems>true</highlightExternalFeedItems>
        <rightComponents>
            <componentType>HelpAndToolLinks</componentType>
        </rightComponents>
        <rightComponents>
            <componentType>Following</componentType>
        </rightComponents>
        <rightComponents>
            <componentType>Followers</componentType>
        </rightComponents>
        <rightComponents>
            <componentType>Topics</componentType>
        </rightComponents>
        <rightComponents>
            <componentType>CustomLinks</componentType>
        </rightComponents>
        <rightComponents>
            <componentType>CustomButtons</componentType>
        </rightComponents>
        <useInlineFiltersInConsole>true</useInlineFiltersInConsole>
    </feedLayout>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Case Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CaseNumber</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ContactId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>AccountId</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ContactPhone</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ContactEmail</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Additional Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Status</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Origin</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Priority</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Type</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Reason</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Description Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Subject</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Comments</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Web Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>SuppliedEmail</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>SuppliedName</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ClosedDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>SuppliedCompany</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>SuppliedPhone</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
        <platformActionListItems>
            <actionName>FeedItem.TextPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>0</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Case.SendEmail</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>1</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>LogACall</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>2</sortOrder>
        </platformActionListItems>
    </platformActionList>
    <quickActionList>
        <quickActionListItems>
            <quickActionName>FeedItem.TextPost</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Case.SendEmail</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>LogACall</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Case.Close_the_Case</quickActionName>
        </quickActionListItems>
    </quickActionList>
    <relatedContent>
        <relatedContentItems>
            <layoutItem>
                <behavior>Readonly</behavior>
                <field>ContactId</field>
            </layoutItem>
        </relatedContentItems>
        <relatedContentItems>
            <layoutItem>
                <behavior>Readonly</behavior>
                <field>AccountId</field>
            </layoutItem>
        </relatedContentItems>
    </relatedContent>
    <relatedLists>
        <relatedList>RelatedCommentsList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedEntityHistoryList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>SOLUTION.ISSUE</fields>
        <fields>SOLUTION.SOLUTION_NUMBER</fields>
        <fields>SOLUTION.STATUS</fields>
        <fields>CORE.USERS.ALIAS</fields>
        <relatedList>RelatedSolutionList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedFileList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>TASK.SUBJECT</fields>
        <fields>TASK.WHO_NAME</fields>
        <fields>ACTIVITY.TASK</fields>
        <fields>TASK.DUE_DATE</fields>
        <fields>TASK.STATUS</fields>
        <fields>TASK.PRIORITY</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <relatedList>RelatedActivityList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>TASK.SUBJECT</fields>
        <fields>TASK.WHO_NAME</fields>
        <fields>ACTIVITY.TASK</fields>
        <fields>TASK.DUE_DATE</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <fields>TASK.LAST_UPDATE</fields>
        <relatedList>RelatedHistoryList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedAttachmentList</relatedList>
    </relatedLists>
    <relatedObjects>ContactId</relatedObjects>
    <relatedObjects>AccountId</relatedObjects>
    <runAssignmentRulesDefault>false</runAssignmentRulesDefault>
    <showEmailCheckbox>true</showEmailCheckbox>
    <showHighlightsPanel>true</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showKnowledgeComponent>true</showKnowledgeComponent>
    <showRunAssignmentRulesCheckbox>true</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>true</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00hR0000000H2Eq</masterLabel>
        <sizeX>3</sizeX>
        <sizeY>4</sizeY>
        <summaryLayoutItems>
            <field>ContactId</field>
            <posX>0</posX>
            <posY>0</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>AccountId</field>
            <posX>0</posX>
            <posY>1</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>ContactPhone</field>
            <posX>0</posX>
            <posY>2</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>CaseNumber</field>
            <posX>1</posX>
            <posY>0</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>CreatedDate</field>
            <posX>1</posX>
            <posY>1</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>Subject</field>
            <posX>1</posX>
            <posY>2</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>Description</field>
            <posX>1</posX>
            <posY>3</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>Status</field>
            <posX>2</posX>
            <posY>0</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>Priority</field>
            <posX>2</posX>
            <posY>1</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>OwnerId</field>
            <posX>2</posX>
            <posY>2</posY>
        </summaryLayoutItems>
        <summaryLayoutStyle>CaseInteraction</summaryLayoutStyle>
    </summaryLayout>
</Layout>
