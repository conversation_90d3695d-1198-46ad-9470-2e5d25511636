<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>OpenSlackRecordChannel</excludeButtons>
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Business Details</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AccountId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Last_Task_Activity_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Last_Event_Activity_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Last_Non_Marketing_Activity_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Tier__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Opportunity_Reference__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Marketing_Source__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Lead_Source__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Allocation_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>StageName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Loss_Reason__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Breadwinner</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Bread_Winner__Total_Amount_Overdue__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Bread_Winner__Total_Amount_Due__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Bread_Winner__Total_Amount_Invoiced__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Bread_Winner__Total_Amount_Paid__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Bread_Winner__Total_Draft_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Financial Requirements</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Type</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Loan_Purpose__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Amount_Required__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Loan_term__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Notes__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Summary</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Capital_Cost__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Amount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>CloseDate</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Supplier and Introducer</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Supplier__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Introducer__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Equipment</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Equipment_Description__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Equipment_Type__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Option_to_purchase__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Checklist</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Bank_statements__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Consent_AML__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Proposal_write_up__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Latest_accounts_MI__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Asset_Liability_Statement__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Accounts__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Business_Plan__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Clearance_letter_settlement__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Supplier_approved__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ID_PoA__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Consent_Form_PG__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Consent_Form_3rd_Party_guarantee__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Managements_Accounts__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Financial_Projections__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Customer Transcript</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Customer_Transcript__c</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LeadSource</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Probability</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
        <platformActionListItems>
            <actionName>Opportunity.New_Proposal</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>0</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.TextPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>1</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Opportunity.Create_Opportunity_Lender</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>2</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.ContentPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>3</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewEvent</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>4</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewTask</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>5</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.PollPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>6</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewContact</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>7</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewOpportunity</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>8</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewLead</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>9</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.ContentNote</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>10</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>LogACall</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>11</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>ChangeRecordType</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>12</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Submit</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>13</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>SendSurveyInvitation</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>14</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>ChangeOwnerOne</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>15</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>DeepClone</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>16</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Edit</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>17</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>RecordShareHierarchy</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>18</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>CreateSalesChannel</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>19</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>OpportunityMultiAddToCadence</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>20</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Delete</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>21</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>StartOutboundConversation</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>22</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Clone</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>23</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Share</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>24</sortOrder>
        </platformActionListItems>
    </platformActionList>
    <relatedContent>
        <relatedContentItems>
            <layoutItem>
                <behavior>Readonly</behavior>
                <field>AccountId</field>
            </layoutItem>
        </relatedContentItems>
        <relatedContentItems>
            <layoutItem>
                <behavior>Readonly</behavior>
                <field>OwnerId</field>
            </layoutItem>
        </relatedContentItems>
    </relatedContent>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Bread_Winner__Status_Flag__c</fields>
        <fields>Bread_Winner__Invoice_Date__c</fields>
        <fields>Bread_Winner__Due_Date__c</fields>
        <fields>Bread_Winner__Total__c</fields>
        <fields>Bread_Winner__Amount_Overdue__c</fields>
        <fields>Bread_Winner__Amount_Due__c</fields>
        <fields>Bread_Winner__Amount_Credit__c</fields>
        <fields>Bread_Winner__Amount_Paid__c</fields>
        <fields>Bread_Winner__Status__c</fields>
        <relatedList>Bread_Winner__Invoice__c.Bread_Winner__Opportunity__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>TASK.SUBJECT</fields>
        <fields>TASK.WHO_NAME</fields>
        <fields>ACTIVITY.TASK</fields>
        <fields>TASK.DUE_DATE</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <fields>TASK.LAST_UPDATE</fields>
        <relatedList>RelatedHistoryList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Lender_Product__c.Lender__c</fields>
        <fields>Lending_Stage__c</fields>
        <fields>Lender_Product__c.Commission__c</fields>
        <relatedList>Opportunity_Lender__c.Opportunity__c</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedFileList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedContentNoteList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>MemberName</fields>
        <fields>OppAccessLevel</fields>
        <fields>TeamMemberRole</fields>
        <relatedList>RelatedOpportunitySalesTeam</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>SplitOwner</fields>
        <fields>SplitType</fields>
        <fields>SplitPercentage</fields>
        <fields>SplitAmount</fields>
        <fields>SplitNote</fields>
        <relatedList>RelatedOpportunitySplits</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>OPPORTUNITY.NAME</fields>
        <fields>ACCOUNT.NAME</fields>
        <fields>OPPORTUNITY.CLOSE_DATE</fields>
        <relatedList>Opportunity.Opportunity_Prop__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>ContactName</fields>
        <fields>Role</fields>
        <fields>ContactTitle</fields>
        <fields>IsPrimary</fields>
        <fields>ContactPhone</fields>
        <fields>ContactEmail</fields>
        <fields>ContactAccountName</fields>
        <relatedList>RelatedContactRoleList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>OPPORTUNITY.NAME</fields>
        <fields>ACCOUNT.NAME</fields>
        <fields>OPPORTUNITY.CREATED_DATE</fields>
        <fields>OPPORTUNITY.CLOSE_DATE</fields>
        <relatedList>Opportunity.Opportunity_Proposals__c</relatedList>
        <sortField>OPPORTUNITY.CREATED_DATE</sortField>
        <sortOrder>Desc</sortOrder>
    </relatedLists>
    <relatedObjects>AccountId</relatedObjects>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00hP3000000U9fN</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
