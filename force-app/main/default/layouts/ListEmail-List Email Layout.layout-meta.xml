<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ScheduledDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Type</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CampaignId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>RelatedToId</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Status</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalSent</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ProgramName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>EmailContentId</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Address Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FromName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FromAddress</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ReplyToAddress</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Message Content</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Subject</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>HtmlBody</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>TextBody</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Engagement Metrics</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>DeliveryRate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>OpenRate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ClickToOpenRatio</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalDelivered</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>UniqueOpens</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>UniqueTrackedLinkClicks</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalOpens</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalTrackedLinkClicks</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ClickThroughRate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>UniqueClickThroughRate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>OptOutRate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>SpamComplaintRate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>UniqueOptOuts</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalSpamComplaints</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalSoftBounced</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalHardBounced</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <relatedLists>
        <relatedList>RelatedAttachmentList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>EMAIL_ADDRESS</fields>
        <fields>RESULT</fields>
        <relatedList>RelatedListEmailRecipientsList</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
