<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Product Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>OrderId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Product2Id</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ProductCode</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ListPrice</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>UnitPrice</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Quantity</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalPrice</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Description Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Additional Information</label>
        <layoutColumns/>
        <layoutColumns/>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <multilineLayoutFields>Product2Id</multilineLayoutFields>
    <multilineLayoutFields>Quantity</multilineLayoutFields>
    <multilineLayoutFields>UnitPrice</multilineLayoutFields>
    <multilineLayoutFields>ListPrice</multilineLayoutFields>
    <multilineLayoutFields>Description</multilineLayoutFields>
    <multilineLayoutFields>OrderId</multilineLayoutFields>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
