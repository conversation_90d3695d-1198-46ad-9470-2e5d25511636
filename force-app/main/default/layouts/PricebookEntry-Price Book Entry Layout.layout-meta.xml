<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Product2Id</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Pricebook2Id</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>UnitPrice</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsActive</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ProductCode</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>UseStandardPrice</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>StandardPrice</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <multilineLayoutFields>Product2Id</multilineLayoutFields>
    <multilineLayoutFields>Pricebook2Id</multilineLayoutFields>
    <multilineLayoutFields>IsActive</multilineLayoutFields>
    <multilineLayoutFields>UnitPrice</multilineLayoutFields>
    <multilineLayoutFields>ProductCode</multilineLayoutFields>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
