@isTest
public class OpportunityTriggerHandler_test {
    
    @isTest
    static void testProposalReferenceNumber() {
        Id recordTypeId =Schema.SObjectType.Opportunity.getRecordTypeInfosByName().get('Proposals').getRecordTypeId();
        
        // Create a Proposal_Reference_Setting__c custom setting
        Proposal_Reference_Setting__c setting = new Proposal_Reference_Setting__c(Proposal_Reference__c = 'P-00000');
        insert setting;
        
        // Create a test opportunity with the Proposals record type
        Opportunity opp = new Opportunity(Name = 'Test Proposal', StageName = 'Prospecting',Closedate=System.today(), RecordTypeId = recordTypeId);
        insert opp;
        
        Test.startTest();
        OpportunityTriggerHandler.ProposalReferenceNumber(new List<Opportunity>{opp});
        Test.stopTest();
        
        List<Proposal_Reference_Setting__c> updatedproposalSettings = [SELECT Proposal_Reference__c FROM Proposal_Reference_Setting__c LIMIT 1];
       // System.assertEquals('P-00001', setting.Proposal_Reference__c, 'Next proposal reference should be incremented');
        
        opp = [SELECT Proposal_Reference__c FROM Opportunity WHERE Id = :opp.Id];
        System.assertEquals('P-00001', opp.Proposal_Reference__c, 'Opportunity should also have the correct proposal reference');
    }
}