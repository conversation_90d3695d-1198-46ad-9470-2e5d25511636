@isTest
private class LeadSenseServiceTest {

    @isTest
    static void testCreateLead_Success() {
        // Prepare fake input JSON with valid picklist values
        String inputJson = JSON.serialize(new Map<String, Object>{
            'loan_amount' => '15000',
            'loan_term' => '24',
            'business_type' => 'Limited',
            'company_name' => 'Test Ltd',
            'postcode_uk' => 'W1D 4AA',
            'first_name' => 'Test',
            'last_name' => 'User',
            'phone_cell' => '***********',
            'email' => '<EMAIL>',
            'source' => 'Self Gen',
            'loan' => 'Loan',
            'marketing_source' => 'Lead Sense',
            'lead_id' => 'TEST5',
            'borrowing_reason' => 'Expansion',
            'business_profitable' => true,
            'income_via_card_terminal' => false,
            'estimate_per_month' => '3000',
            'homeowner' => 'Owned - Mortgaged',
            'turnover' => '£250k - £500k'
        });

        // Set up the request context
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        req.requestUri = '/services/apexrest/Leadsense';
        req.httpMethod = 'POST';
        req.requestBody = Blob.valueOf(inputJson);
        RestContext.request = req;
        RestContext.response = res;

        // Call the method
        String result = LeadSenseService.createLead();

        // Assert response
        System.assert(result.contains('Lead created with ID'), 'Expected success message');

        // Verify the lead was created with all expected fields
        List<Lead> leads = [
            SELECT Id, Email, Third_Party_Reference__c, What_Are_You_Borrowing_Money_For__c,
                   Business_Profitable__c, Income_via_Card_Terminal__c, Estimate_Per_Month__c,
                   Homeowner__c, Turnover__c, FirstName, LastName
            FROM Lead
            WHERE Email = '<EMAIL>'
        ];

        System.assertEquals(1, leads.size(), 'One lead should be inserted');
        System.assertEquals('TEST5', leads[0].Third_Party_Reference__c);
        System.assertEquals('Expansion', leads[0].What_Are_You_Borrowing_Money_For__c);
        System.assertEquals(true, leads[0].Business_Profitable__c);
        System.assertEquals(false, leads[0].Income_via_Card_Terminal__c);
        System.assertEquals('3000', leads[0].Estimate_Per_Month__c);
        System.assertEquals('Owned - Mortgaged', leads[0].Homeowner__c);
        System.assertEquals('£250k - £500k', leads[0].Turnover__c);
        System.assertEquals('Test', leads[0].FirstName);
        System.assertEquals('User', leads[0].LastName);
    }
}