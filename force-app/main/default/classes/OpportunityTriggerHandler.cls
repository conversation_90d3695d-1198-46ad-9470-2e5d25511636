/**
* <AUTHOR>
* @date 29/04/2024
*
* @description: This Apex class generates a Proposal Unique Number using a initial Custom Setting value and again updates the custom setting value to the latest one
                 and displays on opportunity record page for Proposals recordtype.
* @test Class OpportunityTriggerHandler_test
*/
public class OpportunityTriggerHandler {
        /**
* <AUTHOR>
* @date 29/04/2024
*
* @description: This method takes the list of Opportunities from the Before insert trigger and generates a Proposal Unique Number using a initial Custom Setting value
                and again updates the custom setting value to the latest one also insert that same value on opportunity page on "Proposal Reference" field.
*/
    
    public static void ProposalReferenceNumber(List<Opportunity>OppList){
        Id recordTypeId =Schema.SObjectType.Opportunity.getRecordTypeInfosByName().get('Proposals').getRecordTypeId();
        try {
            List<Proposal_Reference_Setting__c> proposalSettings = [SELECT Proposal_Reference__c FROM Proposal_Reference_Setting__c LIMIT 1 FOR UPDATE];
            for (Opportunity opp : OppList) {
                if (opp.RecordTypeId == recordTypeId && !proposalSettings.isEmpty()) {
                    Proposal_Reference_Setting__c proposalSetting = proposalSettings[0];
                    
                    // Increment the ProposalReference__c field value by one
                    Integer nextProposalNumber = Integer.valueOf(proposalSetting.Proposal_Reference__c.substring(2)) + 1;
                    String nextProposalReference = 'P-' + padWithZeros(nextProposalNumber, 5);
                    opp.Proposal_Reference__c	 = nextProposalReference;
                    proposalSetting.Proposal_Reference__c = nextProposalReference;
                }
            }
            
            update proposalSettings;
        }
        catch (Exception e) {
            Logger.finest('Add log entry using Nebula Logger with logging level == FINEST');
            Logger.saveLog();
            System.debug('An error occurred: ' + e.getMessage());
        }
    }
    
        /**
* <AUTHOR> Rana
* @date 29/04/2024
*
* @description: This method's function to pad a number with leading zeros
*/
    private static String padWithZeros(Integer numbers, Integer length) {
        String numberString = String.valueOf(numbers);
        Integer paddingLength = Math.max(length - numberString.length(), 0);
        String padding = '0'.repeat(paddingLength);
        return padding + numberString;
    }
}