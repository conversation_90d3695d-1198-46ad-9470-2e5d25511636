// ----------------------------
// LeadSenseService.cls
// ----------------------------
// https://hcfl--int.sandbox.salesforce.com/v63/services/apexrest/Leadsense
@RestResource(urlMapping='/Leadsense/*')
global with sharing class LeadSenseService {

    global class LeadInput {
        public String loan_amount;
        public String loan_term;
        public String business_type;
        public String company_name;
        public String postcode_uk;
        public String first_name;
        public String last_name;
        public String phone_cell;
        public String email;
        public String source;
        public String loan;
        public String marketing_source;
        public String lead_id;
        public String borrowing_reason;
        public Boolean business_profitable;
        public Boolean income_via_card_terminal;
        public String estimate_per_month;
        public String homeowner;
        public String turnover;
    }

    @HttpPost
    global static String createLead() {
        RestRequest req = RestContext.request;
        RestResponse res = RestContext.response;

        try {
            LeadInput input = (LeadInput) JSON.deserialize(req.requestBody.toString(), LeadInput.class);
            
            Lead newLead = new Lead();
            newLead.Loan_Amount_Required__c = Decimal.valueOf(input.loan_amount);
            newLead.Term__c = input.loan_term;
            newLead.Company_Legal_Entity__c = input.business_type;
            newLead.Company = input.company_name;
            newLead.PostalCode = input.postcode_uk;
            newLead.FirstName = input.first_name;
            newLead.LastName = input.last_name;
            newLead.Phone = input.phone_cell;
            newLead.Email = input.email;
            newLead.Lead_Source__c  = input.source;
            newLead.Loan_Type__c = input.loan;
            newLead.Marketing_Source__c = input.marketing_source;
            newLead.Third_Party_Reference__c = input.lead_id;
            newLead.What_Are_You_Borrowing_Money_For__c = input.borrowing_reason;
            newLead.Business_Profitable__c = input.business_profitable;
            newLead.Income_via_Card_Terminal__c = input.income_via_card_terminal;
            newLead.Estimate_Per_Month__c = input.estimate_per_month;
            newLead.Homeowner__c = input.homeowner;
            newLead.Turnover__c = input.turnover;

            insert newLead;
            res.statusCode = 201;
            Logger.Info('LeadSense Created a Lead' + newLead.Id);
            Logger.saveLog();
            return 'Lead created with ID: ' + newLead.Id;
           

        } catch (Exception e) {
            Logger.Error('Unable to create Lead based on LeadSense inbound call ' + e.getMessage());
            Logger.saveLog();
            res.statusCode = 400;
            return 'Error creating lead: ' + e.getMessage();
            
        }
    }
}