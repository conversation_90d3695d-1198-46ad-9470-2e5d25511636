public class RFA_batch_monthly_stop {

    @AuraEnabled // This is used if called by js code
    public static void RFA_batch_monthly_stop() 
    {
        Id detailId = [SELECT Id FROM CronJobDetail WHERE Name='RFA Accounts Refresh'][0].Id;
		if (detailId != null) {
            Id jobId = [SELECT Id from CronTrigger WHERE CronJobDetailId = :detailId][0].Id;
            System.abortJob(jobId);
    	}
    }
}