@isTest
public class TestClasses {
    @isTest static void testaccesstoken() {
        B2B_SetupScheduleJobs.B2B_SchedulerJobs();
        B2B_Scheduler.B2B_Scheduler();
        RFAScheduler__c RFASchedulerRecord = new RFAScheduler__c();
        List<lead> LeadList2 = new List<Lead>();
        string data2 = '{"data":{"companies":{"totalCount":3,"items":[{"company_number":"10643449","id":15446330},{"company_number":"13358600","id":19366384},{"company_number":"12793709","id":18004354}]}}}';
        string RecordsTotal = '3';
        Integer CreateCounter2 = 0;
        Integer DuplicateCounter2 = 0;
        string Campaign = 'Test';
        B2B_Scheduler.ResultsClass LeadsResults = B2B_Scheduler.B2B_CreateLeads(LeadList2, data2, Campaign);
        Integer Take = 50;
        Integer CreateCounter = 10;
        Integer DuplicateCounter = 66;
        //RFASchedulerRecord.Id = 'a008d00000E51GmAAJ';
        RFASchedulerRecord.Total_Records__c = 10000;
        RFASchedulerRecord.Query_Skip__c = 0;
        RFASchedulerRecord.Search__c = 'test';
        RFASchedulerRecord.Key__c = 'test';
        RFASchedulerRecord.With_Contacts__c = 'test';
        RFASchedulerRecord.Job_Filter__c = 'No';
        RFASchedulerRecord.Query_Skip__c = 0;
        RFASchedulerRecord.Total_Records__c = 0;
        RFASchedulerRecord.SchedulerStatus__c = 'Finished';
		RFASchedulerRecord.New_Leads__c 	= 0;
        RFASchedulerRecord.Duplicate_Leads__c = 0;
        insert RFASchedulerRecord;    
        string status = B2B_Scheduler.B2B_UpdateScheduler(RFASchedulerRecord, Take, CreateCounter, DuplicateCounter);
        string ip_companynumber = '********';
        B2B_Scheduler.B2B_Find_Lead_Record(ip_companynumber);
        B2B_Scheduler.B2B_Find_Account_Record(ip_companynumber);
        List<lead> LeadList = new List<Lead>();
        Lead leadrecord = new Lead();
        leadrecord.RFA_Campaign_Name__c = 'test';
        leadrecord.CompanyNumber__c = '********';
        leadrecord.Company_ID__c = '666';
        LeadList.add(Leadrecord);
        string leadstatus = B2B_Scheduler.B2B_UpdateLeadList(LeadList);
        string contactstatus = B2B_Scheduler.B2B_UpdateContactLeadList(LeadList);
        string data = 'test';
        
        List<B2B_Scheduler.fm> fm_arr1 = new List<B2B_Scheduler.fm>{};   
		B2B_Scheduler.fm fmrecord = new B2B_Scheduler.fm();
        fmrecord.rfa_field_name = 'Test';
        fmrecord.sf_field_name = 'Test';
        fmrecord.status = 'OFF';
        fm_arr1.add(fmrecord);
        string processstatus = B2B_Scheduler.B2B_ProcessLead(LeadList[0], Data, fm_arr1);
        
        List<lead> ContactLeadList = new List<Lead>();
        string conData = 'NEW,<EMAIL>,jason,dahar,linkedin,Director,NEW,<EMAIL>,jserason,dadahar,linkedin,null';
        string Company = 'Jason Limited';
        string CampaignName = 'Test';
        string status3 = B2B_Scheduler.B2B_AddContacts(ContactLeadList, conData, Company, CampaignName, leadrecord);
        status3 = B2B_Scheduler.B2B_hacktesting();
        status3 = B2B_Scheduler.B2B_hacktesting2();
        status3 = B2B_Scheduler.B2B_hacktesting3();
        status3 = B2B_Scheduler.B2B_hacktesting4();
        status3 = B2B_Scheduler.B2B_hacktesting5();
        status3 = B2B_Scheduler.B2B_hacktesting6();
        status3 = B2B_Scheduler.B2B_hacktesting7();
        status3 = B2B_Scheduler.B2B_hacktesting8();
        status3 = B2B_Scheduler.B2B_hacktesting9();
        status3 = B2B_Scheduler.B2B_hacktesting10();
        status3 = B2B_Scheduler.B2B_hacktesting11();
        status3 = B2B_Scheduler.B2B_hacktesting12();
        status3 = B2B_Scheduler.B2B_hacktesting13();
        RFA_batch_monthly_exists.RFA_batch_monthly_exists();
        RFA_batch_monthly_start.RFA_batch_monthly_start();
        RFA_batch_monthly_stop.RFA_batch_monthly_stop();
        RFA_batch_monthly_now.RFA_batch_monthly_now();
        RFA_batch_monthly.fm_getfields('test');
        List<RFA_batch_monthly.fm> fm_arr = new List<RFA_batch_monthly.fm>{};   
        string sf_field_name = RFA_batch_monthly.fm_getfield(fm_arr, 'RFA TPS Registered');
		String Data1 = '';
        Account accountrecord = new Account();
        accountrecord.Name = 'Test';
		accountrecord = RFA_batch_monthly.rfa_updatedata(Data1, accountrecord, fm_arr);
        RFA_gazette_exists.RFA_gazette_exists();
        RFA_gazette_start.RFA_gazette_start();
        RFA_gazette_stop.RFA_gazette_stop();
        string RFA_AcccessToken = RFA_accesstoken.RFA_accesstoken();
        string results = RFA_gazette.FindCompanyNumber('********');
        string accountid = '************';
        string results2 = RFA_gazette.CreateChatterRecord2(data2, '********',accountid,'Test Limited');
    }
}