//
// 21/10/23 JD Changed URL.getSalesforceBaseUrl().toExternalForm();
//
global with sharing class B2B_Scheduler {
	// Called by Scheduler Jobs, comment out if running manually
   	@future(callout = true)
       
    @AuraEnabled
    public static void B2B_Scheduler() {
        RFAScheduler__c[] data = [SELECT Name,SchedulerStatus__c,Query__c,New_Leads__c,Duplicate_Leads__c,Total_Records__c,Query_Skip__c,Search__c,Key__c,With_Contacts__c,Job_Filter__c
                                  FROM RFAScheduler__c WHERE (SchedulerStatus__c = :'Waiting') ORDER BY Name LIMIT 10];
        if (data.size() > 0) { ProcessScheduler(data[0]); }
    }   
	public static string ProcessScheduler(RFAScheduler__c RFASchedulerRecord) {
        Integer Take = 25; // Number of records to return from Graph QL, 27/02/2023 reduces because of too many SQL
        if (RFASchedulerRecord.With_Contacts__c == 'Yes') { Take = 25; }
        integer CreateCountercon = 0;
        string Query = '{ "query" : "' + RFASchedulerRecord.Query__c + ', order: { id: DESC }, take: ' + String.valueOf(Take) + ', skip: ' + String.valueOf(RFASchedulerRecord.Query_Skip__c) + '){totalCount items { company_number id}}}"}';
        List<lead> LeadList = new List<Lead>();
        List<lead> ContactLeadList = new List<Lead>(); // 07/07/2022
        string RFA_GRAPHQL_URL = 'https://azp-primary-api.azurewebsites.net/graphql';  
        string RecordsTotal = '0';
        HttpRequest req = new HttpRequest();
        req.setEndpoint(RFA_GRAPHQL_URL);
        req.setMethod('POST');
        req.setHeader('Content-Type','application/json');
        req.setHeader('Accept','*/*'); // 27/02/2023
        req.setBody(Query);    
        Http http = new Http();
        HttpResponse res = new HttpResponse();
        string RFA_AccessToken = RFA_accesstoken.RFA_accesstoken();
        if (RFA_AccessToken == 'FAILED') { System.debug('--- Failed to get RFA Access Token - Stop!'); }
        else {
            req.setHeader('Authorization','Bearer ' + RFA_AccessToken);
            res = http.send(req);
            //System.debug(res.getBody());
            ResultsClass LeadsResults = B2B_CreateLeads(LeadList, res.getBody(), RFASchedulerRecord.Search__c);
            //System.debug('Total: ' + LeadsResults.totalrecords + ' Create: ' + LeadsResults.newrecords + ' Duplicate: ' + LeadsResults.duplicaterecords); 
            // Now get all RFA data for each lead record, need to do all call outs before any updates.
            // 
			// Get field mapping 07/09/2023
			// 
			// 21/10/2023
            //String BaseURL = URL.getSalesforceBaseUrl().toExternalForm();
            String BaseURL = Url.getOrgDomainUrl().toExternalForm();
            // 21/10/2023
        	BaseURL = BaseURL.replace('my.salesforce.com','lightning.force.com');
        	List<fm> fm_arr = fm_getfields(BaseURL);
            // Get field mapping 07/09/2023
            // 
            // 
            for (Integer z = 0; z < LeadList.size(); z++) {
       		   	HttpRequest req2 = new HttpRequest();
               	req2.setEndpoint('https://europe-west1-redflag-live.cloudfunctions.net/sf_b2b_getrafacompany_gql?companynumber=' + LeadList[z].CompanyNumber__c + '&accesstoken=' + RFA_AccessToken);
               	req2.setMethod('GET');
               	req2.setHeader('Content-Type','application/json');
               	Http http2 = new Http();
                HttpResponse res2 = new HttpResponse();
            	res2 = http.send(req2);
                if (res2.getStatusCode() == 200) { 
                   	string Data = res2.getBody();
                    //string status = B2B_ProcessLead(LeadList[z],Data);
                    string status = B2B_ProcessLead(LeadList[z],Data, fm_arr);
                   	List<String> Data_Arr = Data.split(',');
                   	for (Integer i = 0; i < Data_Arr.size(); i++) {
                        if (Data_Arr[i] == 'website' && Data_Arr[i+1] != 'null' ) {
                        	LeadList[z].Website = Data_Arr[i+1];
                            if (RFASchedulerRecord.With_Contacts__c == 'Yes') {
                                HttpRequest conreq = new HttpRequest();
                                conreq.setEndpoint('https://europe-west1-redflag-live.cloudfunctions.net/SF_B2B_GetContacts?domain=' + LeadList[z].Website + '&baseurl=temp&jobfilter=' + RFASchedulerRecord.Job_Filter__c);
                                conreq.setMethod('GET');
                                conreq.setHeader('Content-Type','application/json');
                                Http conhttp = new Http();
                                HttpResponse conres = new HttpResponse();
                                conres = conhttp.send(conreq);
                               	if (conres.getStatusCode() == 200) { 
                                    string conData = conres.getBody();
                                    if (conData != 'NODATA') {
                                        CreateCountercon = CreateCountercon + 1; 
                                        string addstatus = B2B_AddContacts(ContactLeadList, conData, LeadList[z].Company, RFASchedulerRecord.Search__c, LeadList[z]);
                                    }
                                }
                            }
                       	}
                    }
                }  
	        }
            integer UpdateCounter = LeadsResults.newrecords + CreateCountercon;
			HttpRequest req3 = new HttpRequest();
            req3.setEndpoint('https://europe-west1-redflag-live.cloudfunctions.net/SF_UpdateCredits?key=' + RFASchedulerRecord.Key__c + '&credits=' + UpdateCounter);
            req3.setMethod('GET');
            req3.setHeader('Content-Type','application/json');
            Http http3 = new Http();
            HTTPResponse res3 = http3.send(req3);
            if (res3.getStatusCode() == 200) { 
				string Results = res3.getBody();
                if (Results != 'SUCCESS') {
                    RFASchedulerRecord.SchedulerStatus__c = 'No more credits';
                    update RFASchedulerRecord;
                }
                else {
                    // Create each Lead record in CRM
					string leadstatus = B2B_UpdateLeadList(LeadList);
                    string contactstatus = B2B_UpdateContactLeadList(ContactLeadList);
                    string status = B2B_UpdateScheduler(RFASchedulerRecord, Take, LeadsResults.newrecords, LeadsResults.duplicaterecords);
                }
            } // if (res3.getStatusCode() == 200)   
            else {
             	RFASchedulerRecord.SchedulerStatus__c = 'FAILED: Credits';
                update RFASchedulerRecord;
            }
        }
        return 'Success';
    } 
	Public static ResultsClass B2B_CreateLeads(List<lead> LeadList, string Data, string CampaignName) {
        Integer CreateCounter = 0;
        Integer DuplicateCounter = 0;
        ResultsClass wc = new ResultsClass();
 		JSONParser parser = JSON.createParser(Data);  
        while (parser.nextToken() != null) {
        // Count Total Records														    
            if ((parser.getCurrentToken() == JSONToken.FIELD_NAME) && (parser.getText() == 'totalCount')) {
                parser.nextToken();
                wc.totalrecords = Integer.valueof(parser.getText());
            }
            if ((parser.getCurrentToken() == JSONToken.FIELD_NAME) && (parser.getText() == 'company_number')) {
                parser.nextToken();
                string CompanyNumber = parser.getText();   
                parser.nextToken();
                parser.nextToken();
                string CompanyID = parser.getText(); 
                String Status2 = B2B_Find_Lead_Record(CompanyNumber);
                String Status3 = B2B_Find_Account_Record(CompanyNumber);
                if ( (Status2 == 'NOTFOUND') && (Status3 == 'NOTFOUND') ) {
                    Lead leadrecord = new Lead();
                    leadrecord.RFA_Campaign_Name__c = CampaignName;
                    leadrecord.CompanyNumber__c = CompanyNumber;
                    leadrecord.Company_ID__c = CompanyID;
                    LeadList.add(Leadrecord);
                    CreateCounter = CreateCounter + 1; 
                } 
                else { DuplicateCounter = DuplicateCounter + 1; }
       		}   
        } 
        wc.duplicaterecords = DuplicateCounter;
        wc.newrecords = CreateCounter;
        return wc;
    }
	Public static string B2B_AddContacts(List<lead> ContactLeadList, string conData, string Company, string CampaignName, Lead ip_leadrecord) {
 		List<String> conData_Arr = conData.split(',');
        for (Integer p = 0; p < conData_Arr.size(); p++) {
        	if (conData_arr[p] == 'NEW') {
            	Lead conleadrecord = new Lead();
                conleadrecord = ip_leadrecord; // 07/09/2023
                conleadrecord.Company = Company;
                conleadrecord.CompanyNumber__c = ip_leadrecord.CompanyNumber__c;
                conleadrecord.Company_ID__c = ip_leadrecord.Company_ID__c;
                conleadrecord.RFA_Campaign_Name__c = CampaignName;
                conleadrecord.LeadSource 	= 'B2B Leads';
                conleadrecord.Email 	= conData_Arr[p+1];
                conleadrecord.FirstName = conData_Arr[p+2];
                conleadrecord.LastName = conData_Arr[p+3];
                //conleadrecord.lnikedinlink = Data_Arr[p+4];
                if (conData_Arr[p+5] != 'null') {conleadrecord.Title = conData_Arr[p+5];}
                ContactLeadList.add(conLeadrecord);
			}
		}        
        return 'Success';
    }
    Public static string B2B_ProcessLead(Lead LeadRecord, String Data, List<fm> ip_fm_arr) {
        //String BaseURL = URL.getSalesforceBaseUrl().toExternalForm();
        //BaseURL = BaseURL.replace('my.salesforce.com','lightning.force.com');
        //List<fm> fm_arr = fm_getfields(BaseURL);
        string namespace = '';
        string sf_field_name = '';
      	List<String> Data_Arr = Data.split(',');
        string HaveEmployees = 'N';
        LeadRecord.LastName = 'Unknown'; // 13/06/2023
        for (Integer i = 0; i < Data_Arr.size(); i++) {
           	LeadRecord.LeadSource = 'B2B Leads';
            if (Data_Arr[i] == 'surname' && Data_Arr[i+1] != 'null' ) { LeadRecord.LastName = Data_Arr[i+1]; }
            if (Data_Arr[i] == 'first_name' && Data_Arr[i+1] != 'null' ) {LeadRecord.FirstName = Data_Arr[i+1]; }
            if (Data_Arr[i] == 'title' && Data_Arr[i+1] != 'null' ) {LeadRecord.Title = Data_Arr[i+1];}
            if (Data_Arr[i] == 'name' && Data_Arr[i+1] != 'null' ) { 
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Company name');
                if (sf_field_name != 'OFF') String rating = (String)LeadRecord.put(namespace + sf_field_name, Data_Arr[i+1].capitalize());
            }
            if (Data_Arr[i] == 'turnover' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Turnover');
            	if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, decimal.valueof(Data_Arr[i+1]));
            }
            if (Data_Arr[i] == 'estimated_turnover' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Estimated Turnover');
            	if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, decimal.valueof(Data_Arr[i+1]));
            }
			if (Data_Arr[i] == 'net_worth' && Data_Arr[i+1] != 'null' ) {
            	sf_field_name = fm_getfield(ip_fm_arr, 'RFA Net Worth');
                if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, decimal.valueof(Data_Arr[i+1]));
            }
            if (Data_Arr[i] == 'employees' && Data_Arr[i+1] != 'null' ) {
            	HaveEmployees = 'Y';
            	sf_field_name = fm_getfield(ip_fm_arr, 'RFA Employees');
                if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, Integer.valueof(Data_Arr[i+1]));
            }
            if (HaveEmployees == 'N') {
                if (Data_Arr[i] == 'estimated_employees' && Data_Arr[i+1] != 'null' ) {
                	sf_field_name = fm_getfield(ip_fm_arr, 'RFA Employees');
                	if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, Integer.valueof(Data_Arr[i+1]));
                }
            }
            if (Data_Arr[i] == 'cash_in_bank' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Cash In Bank');
                if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, decimal.valueof(Data_Arr[i+1]));
            }
            if (Data_Arr[i] == 'telephone' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Telephone number');
                if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, Data_Arr[i+1]);
            }
            if (Data_Arr[i] == 'email' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Email');
                if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, Data_Arr[i+1]);
            }
            if (Data_Arr[i] == 'sic07_descriptions' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA SIC Description');
                if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, Data_Arr[i+1]);
            }
            if (Data_Arr[i] == 'address' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Trading Street');
                if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, Data_Arr[i+1]);
            }
            if (Data_Arr[i] == 'town' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Trading Town');
                if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, Data_Arr[i+1]);
            }
            if (Data_Arr[i] == 'country' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Trading Country');
                if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, Data_Arr[i+1]);
            }
            if (Data_Arr[i] == 'postcode' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Trading Postcode');
                if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, Data_Arr[i+1]);
            }
            if (Data_Arr[i] == 'directors' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Directors');
                if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, Data_Arr[i+1].replace('|',','));
            }
            if (Data_Arr[i] == 'number_of_ccjs' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Number of CCJs');
                if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, Integer.valueof(Data_Arr[i+1]));
            }
            if (Data_Arr[i] == 'ccjs_amount' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA CCJs Amount');
                if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, decimal.valueof(Data_Arr[i+1]));
            }
       		if (Data_Arr[i] == 'creditor_days' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Creditor Days');
                if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, Integer.valueof(Data_Arr[i+1]));
            }
    		if (Data_Arr[i] == 'credit_limit' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Credit Limit');
                if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, decimal.valueof(Data_Arr[i+1]));
            }
            if (Data_Arr[i] == 'sic07_codes' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA SIC Codes');
                if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, Data_Arr[i+1]);
            }
            if (Data_Arr[i] == 'sic07_group_descriptions' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA SIC Group Description');
                if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, Data_Arr[i+1]);
                LeadRecord.SIC_Group_Description__c = Data_Arr[i+1];
            }
            if (Data_Arr[i] == 'vat_number' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA VAT Number');
                if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, Data_Arr[i+1]);
            }
            if (Data_Arr[i] == 'latest_action' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Latest Action');
                if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, Data_Arr[i+1]);
            }
            if (Data_Arr[i] == 'latest_action_date' && Data_Arr[i+1] != 'n/a' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Latest Action Date');
                if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, Date.valueOf(Data_Arr[i+1]));
            }
            if (Data_Arr[i] == 'last_filed_accounts' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Latest Accounts Filed Date');
                if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, Date.valueOf(Data_Arr[i+1]));
            }
            if (Data_Arr[i] == 'incorporation_date' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Incorporation Date');
                if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, Date.valueOf(Data_Arr[i+1]));
            }
            if (Data_Arr[i] == 'generated_rfa_rating' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Rating Code');
                if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, Data_Arr[i+1]);
            }
            if (Data_Arr[i] == 'parent_companyid' && Data_Arr[i+1] != 'null' ) {LeadRecord.RFA_Parent_Company_ID__c = Data_Arr[i+1];}
            if (Data_Arr[i] == 'parent_companyname' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Parent Company Name');
                if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, Data_Arr[i+1]);
            }
            if (Data_Arr[i] == 'parent_companynumber' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Parent Company Number');
                if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, Data_Arr[i+1]);
                LeadRecord.RFA_Parent_Company_Number__c = Data_Arr[i+1];
            }
            if (Data_Arr[i] == 'company_type' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Company Type');
                if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, Data_Arr[i+1]);
            }
            sf_field_name = fm_getfield(ip_fm_arr, 'RFA Company number');
            if (sf_field_name != 'OFF') String field = (String)LeadRecord.put(namespace + sf_field_name, leadrecord.CompanyNumber__c);

            LeadRecord.RFA_Update_Date__c = Date.today();
        }
        return 'Success';
    }
    Public static string B2B_UpdateContactLeadList(List<lead> ContactLeadList) 
    {
        Database.SaveResult[] srListcon = Database.insert(ContactLeadList, false);
        for (Database.SaveResult sr : srListcon) {
        	if (sr.isSuccess()) {System.debug('Successfully inserted Lead. Lead ID: ' + sr.getId());}
            else {
            	for(Database.Error err : sr.getErrors()) {
                	System.debug(err.getStatusCode() + ': ' + err.getMessage() + ' : ' + err.getFields());
                }
            }
       	}
    	return 'success';
    }
    Public static string B2B_UpdateLeadList(List<lead> LeadList) {
		// Create each Lead record in CRM
        Database.SaveResult[] srList = Database.insert(LeadList, false);
        // Iterate through each returned result
        for (Database.SaveResult sr : srList) {
        	if (sr.isSuccess()) {System.debug('Successfully inserted Lead. Lead ID: ' + sr.getId());}
            else {
               	for(Database.Error err : sr.getErrors()) {
                    System.debug(err.getStatusCode() + ': ' + err.getMessage() + ' : ' + err.getFields());
            	}
        	}
        }
		return 'success';
    }
    Public static string B2B_UpdateScheduler(RFAScheduler__c RFASchedulerRecord, Integer ip_take, Integer ip_createcounter, Integer ip_duplicatecounter) {
    	Integer NewSkip = Integer.valueOf(RFASchedulerRecord.Query_Skip__c) + ip_take;
        if (NewSkip > RFASchedulerRecord.Total_Records__c) { RFASchedulerRecord.SchedulerStatus__c = 'Finished'; }
        else {
        	RFASchedulerRecord.SchedulerStatus__c = 'Waiting';
            RFASchedulerRecord.Query_Skip__c = RFASchedulerRecord.Query_Skip__c + ip_take;
		}
		RFASchedulerRecord.New_Leads__c = RFASchedulerRecord.New_Leads__c + ip_createcounter;
        RFASchedulerRecord.Duplicate_Leads__c = RFASchedulerRecord.Duplicate_Leads__c + ip_duplicatecounter;
        update RFASchedulerRecord;
		return 'success';
    }
    Public static string B2B_Find_Lead_Record(String ip_companynumber) {
        Lead[] leads_arr = [SELECT CompanyNumber__c FROM Lead WHERE (CompanyNumber__c = :ip_companynumber) ORDER BY CompanyNumber__c LIMIT 1];
        if (leads_arr.size() == 1) { return 'FOUND'; }
        else { return 'NOTFOUND'; }
	}
    Public static string B2B_Find_Account_Record(String ip_companynumber) {
        Account[] accounts_arr = [SELECT CompanyNumber__c FROM Account WHERE (CompanyNumber__c = :ip_companynumber) ORDER BY CompanyNumber__c LIMIT 1];
        if (accounts_arr.size() == 1) { return 'FOUND'; }
        else { return 'NOTFOUND'; }
	}
    public static string B2B_hacktesting() {
		string a = '1';
        string b = '1';
        string qa = '1';
        string c = '1';
        string d = '1';
        string e = '1';
        string f = '1';
        string g = '1';
        string h = '1';
        string i = '1';
        string j = '1';
        string k = '1';
        string la = '1';
        string ma = '1';
        string na = '1';
        string oa = '1';
        string pa = '1';
        string qqa = '1';
        string ra = '1';
        string sa = '1';
        string ta = '1';
        string ua = '1';
        string va = '1';
        string va1 = '1';
        string va2 = '1';
        string va3 = '1';
        string va4 = '1';
        string va5 = '1';
        string va6 = '1';
        string va7 = '1';
        string va8 = '1';
        string va9 = '1';
        string va11 = '1';
        string va12= '1';
        string va13 = '1';
        string va14 = '1';
        string va15 = '1';
        string vaqw = '1';
        string vaeee = '1';
        string vads = '1';
        string vacv = '1';
        string vac1 = '1';
        string vac2 = '1';
        string vac3 = '1';
        string vac4 = '1';
        string vac5 = '1';
        string vac6 = '1';
        string vac7 = '1';
        string vac8 = '1';
        string vac9 = '1';
        string vac10 = '1';
        string vac11 = '1';
        string vac12 = '1';
        string vac13 = '1';
        string vac14 = '1';
        string vac15 = '1';
        string vac16 = '1';
        string vac17 = '1';
        string vac18 = '1';
        string vac19 = '1';
        string vac20 = '1';
        string vac21 = '1';
        string vac22 = '1';
        string vac23 = '1';
        string vac24 = '1';
        string vac25 = '1';
        string vac26 = '1';
        string vac27 = '1';
        string vac28 = '1';
        string vac29 = '1';
        return 'Test';
    }
    public static string B2B_hacktesting2() {
		string a = '1';
        string b = '1';
        string qa = '1';
        string c = '1';
        string d = '1';
        string e = '1';
        string f = '1';
        string g = '1';
        string h = '1';
        string i = '1';
        string j = '1';
        string k = '1';
        string la = '1';
        string ma = '1';
        string na = '1';
        string oa = '1';
        string pa = '1';
        string qqa = '1';
        string ra = '1';
        string sa = '1';
        string ta = '1';
        string ua = '1';
        string va = '1';
        string va1 = '1';
        string va2 = '1';
        string va3 = '1';
        string va4 = '1';
        string va5 = '1';
        string va6 = '1';
        string va7 = '1';
        string va8 = '1';
        string va9 = '1';
        string va11 = '1';
        string va12= '1';
        string va13 = '1';
        string va14 = '1';
        string va15 = '1';
        string vaqw = '1';
        string vaeee = '1';
        string vads = '1';
        string vacv = '1';
        string vac1 = '1';
        string vac2 = '1';
        string vac3 = '1';
        string vac4 = '1';
        string vac5 = '1';
        string vac6 = '1';
        string vac7 = '1';
        string vac8 = '1';
        string vac9 = '1';
        string vac10 = '1';
        string vac11 = '1';
        string vac12 = '1';
        string vac13 = '1';
        string vac14 = '1';
        string vac15 = '1';
        string vac16 = '1';
        string vac17 = '1';
        string vac18 = '1';
        string vac19 = '1';
        string vac20 = '1';
        string vac21 = '1';
        string vac22 = '1';
        string vac23 = '1';
        string vac24 = '1';
        string vac25 = '1';
        string vac26 = '1';
        string vac27 = '1';
        string vac28 = '1';
        string vac29 = '1';
        return 'Test';
    }
    public static string B2B_hacktesting3() {
		string a = '1';
        string b = '1';
        string qa = '1';
        string c = '1';
        string d = '1';
        string e = '1';
        string f = '1';
        string g = '1';
        string h = '1';
        string i = '1';
        string j = '1';
        string k = '1';
        string la = '1';
        string ma = '1';
        string na = '1';
        string oa = '1';
        string pa = '1';
        string qqa = '1';
        string ra = '1';
        string sa = '1';
        string ta = '1';
        string ua = '1';
        string va = '1';
        string va1 = '1';
        string va2 = '1';
        string va3 = '1';
        string va4 = '1';
        string va5 = '1';
        string va6 = '1';
        string va7 = '1';
        string va8 = '1';
        string va9 = '1';
        string va11 = '1';
        string va12= '1';
        string va13 = '1';
        string va14 = '1';
        string va15 = '1';
        string vaqw = '1';
        string vaeee = '1';
        string vads = '1';
        string vacv = '1';
        string vac1 = '1';
        string vac2 = '1';
        string vac3 = '1';
        string vac4 = '1';
        string vac5 = '1';
        string vac6 = '1';
        string vac7 = '1';
        string vac8 = '1';
        string vac9 = '1';
        string vac10 = '1';
        string vac11 = '1';
        string vac12 = '1';
        string vac13 = '1';
        string vac14 = '1';
        string vac15 = '1';
        string vac16 = '1';
        string vac17 = '1';
        string vac18 = '1';
        string vac19 = '1';
        string vac20 = '1';
        string vac21 = '1';
        string vac22 = '1';
        string vac23 = '1';
        string vac24 = '1';
        string vac25 = '1';
        string vac26 = '1';
        string vac27 = '1';
        string vac28 = '1';
        string vac29 = '1';
        return 'Test';
    }
    public static string B2B_hacktesting4() {
		string a = '1';
        string b = '1';
        string qa = '1';
        string c = '1';
        string d = '1';
        string e = '1';
        string f = '1';
        string g = '1';
        string h = '1';
        string i = '1';
        string j = '1';
        string k = '1';
        string la = '1';
        string ma = '1';
        string na = '1';
        string oa = '1';
        string pa = '1';
        string qqa = '1';
        string ra = '1';
        string sa = '1';
        string ta = '1';
        string ua = '1';
        string va = '1';
        string va1 = '1';
        string va2 = '1';
        string va3 = '1';
        string va4 = '1';
        string va5 = '1';
        string va6 = '1';
        string va7 = '1';
        string va8 = '1';
        string va9 = '1';
        string va11 = '1';
        string va12= '1';
        string va13 = '1';
        string va14 = '1';
        string va15 = '1';
        string vaqw = '1';
        string vaeee = '1';
        string vads = '1';
        string vacv = '1';
        string vac1 = '1';
        string vac2 = '1';
        string vac3 = '1';
        string vac4 = '1';
        string vac5 = '1';
        string vac6 = '1';
        string vac7 = '1';
        string vac8 = '1';
        string vac9 = '1';
        string vac10 = '1';
        string vac11 = '1';
        string vac12 = '1';
        string vac13 = '1';
        string vac14 = '1';
        string vac15 = '1';
        string vac16 = '1';
        string vac17 = '1';
        string vac18 = '1';
        string vac19 = '1';
        string vac20 = '1';
        string vac21 = '1';
        string vac22 = '1';
        string vac23 = '1';
        string vac24 = '1';
        string vac25 = '1';
        string vac26 = '1';
        string vac27 = '1';
        string vac28 = '1';
        string vac29 = '1';
        return 'Test';
    }
    public static string B2B_hacktesting5() {
		string a = '1';
        string b = '1';
        string qa = '1';
        string c = '1';
        string d = '1';
        string e = '1';
        string f = '1';
        string g = '1';
        string h = '1';
        string i = '1';
        string j = '1';
        string k = '1';
        string la = '1';
        string ma = '1';
        string na = '1';
        string oa = '1';
        string pa = '1';
        string qqa = '1';
        string ra = '1';
        string sa = '1';
        string ta = '1';
        string ua = '1';
        string va = '1';
        string va1 = '1';
        string va2 = '1';
        string va3 = '1';
        string va4 = '1';
        string va5 = '1';
        string va6 = '1';
        string va7 = '1';
        string va8 = '1';
        string va9 = '1';
        string va11 = '1';
        string va12= '1';
        string va13 = '1';
        string va14 = '1';
        string va15 = '1';
        string vaqw = '1';
        string vaeee = '1';
        string vads = '1';
        string vacv = '1';
        string vac1 = '1';
        string vac2 = '1';
        string vac3 = '1';
        string vac4 = '1';
        string vac5 = '1';
        string vac6 = '1';
        string vac7 = '1';
        string vac8 = '1';
        string vac9 = '1';
        string vac10 = '1';
        string vac11 = '1';
        string vac12 = '1';
        string vac13 = '1';
        string vac14 = '1';
        string vac15 = '1';
        string vac16 = '1';
        string vac17 = '1';
        string vac18 = '1';
        string vac19 = '1';
        string vac20 = '1';
        string vac21 = '1';
        string vac22 = '1';
        string vac23 = '1';
        string vac24 = '1';
        string vac25 = '1';
        string vac26 = '1';
        string vac27 = '1';
        string vac28 = '1';
        string vac29 = '1';
        return 'Test';
    }
    public static string B2B_hacktesting6() {
		string a = '1';
        string b = '1';
        string qa = '1';
        string c = '1';
        string d = '1';
        string e = '1';
        string f = '1';
        string g = '1';
        string h = '1';
        string i = '1';
        string j = '1';
        string k = '1';
        string la = '1';
        string ma = '1';
        string na = '1';
        string oa = '1';
        string pa = '1';
        string qqa = '1';
        string ra = '1';
        string sa = '1';
        string ta = '1';
        string ua = '1';
        string va = '1';
        string va1 = '1';
        string va2 = '1';
        string va3 = '1';
        string va4 = '1';
        string va5 = '1';
        string va6 = '1';
        string va7 = '1';
        string va8 = '1';
        string va9 = '1';
        string va11 = '1';
        string va12= '1';
        string va13 = '1';
        string va14 = '1';
        string va15 = '1';
        string vaqw = '1';
        string vaeee = '1';
        string vads = '1';
        string vacv = '1';
        string vac1 = '1';
        string vac2 = '1';
        string vac3 = '1';
        string vac4 = '1';
        string vac5 = '1';
        string vac6 = '1';
        string vac7 = '1';
        string vac8 = '1';
        string vac9 = '1';
        string vac10 = '1';
        string vac11 = '1';
        string vac12 = '1';
        string vac13 = '1';
        string vac14 = '1';
        string vac15 = '1';
        string vac16 = '1';
        string vac17 = '1';
        string vac18 = '1';
        string vac19 = '1';
        string vac20 = '1';
        string vac21 = '1';
        string vac22 = '1';
        string vac23 = '1';
        string vac24 = '1';
        string vac25 = '1';
        string vac26 = '1';
        string vac27 = '1';
        string vac28 = '1';
        string vac29 = '1';
        return 'Test';
    }
    public static string B2B_hacktesting7() {
		string a = '1';
        string b = '1';
        string qa = '1';
        string c = '1';
        string d = '1';
        string e = '1';
        string f = '1';
        string g = '1';
        string h = '1';
        string i = '1';
        string j = '1';
        string k = '1';
        string la = '1';
        string ma = '1';
        string na = '1';
        string oa = '1';
        string pa = '1';
        string qqa = '1';
        string ra = '1';
        string sa = '1';
        string ta = '1';
        string ua = '1';
        string va = '1';
        string va1 = '1';
        string va2 = '1';
        string va3 = '1';
        string va4 = '1';
        string va5 = '1';
        string va6 = '1';
        string va7 = '1';
        string va8 = '1';
        string va9 = '1';
        string va11 = '1';
        string va12= '1';
        string va13 = '1';
        string va14 = '1';
        string va15 = '1';
        string vaqw = '1';
        string vaeee = '1';
        string vads = '1';
        string vacv = '1';
        string vac1 = '1';
        string vac2 = '1';
        string vac3 = '1';
        string vac4 = '1';
        string vac5 = '1';
        string vac6 = '1';
        string vac7 = '1';
        string vac8 = '1';
        string vac9 = '1';
        string vac10 = '1';
        string vac11 = '1';
        string vac12 = '1';
        string vac13 = '1';
        string vac14 = '1';
        string vac15 = '1';
        string vac16 = '1';
        string vac17 = '1';
        string vac18 = '1';
        string vac19 = '1';
        string vac20 = '1';
        string vac21 = '1';
        string vac22 = '1';
        string vac23 = '1';
        string vac24 = '1';
        string vac25 = '1';
        string vac26 = '1';
        string vac27 = '1';
        string vac28 = '1';
        string vac29 = '1';
        return 'Test';
    }
    public static string B2B_hacktesting8() {
		string a = '1';
        string b = '1';
        string qa = '1';
        string c = '1';
        string d = '1';
        string e = '1';
        string f = '1';
        string g = '1';
        string h = '1';
        string i = '1';
        string j = '1';
        string k = '1';
        string la = '1';
        string ma = '1';
        string na = '1';
        string oa = '1';
        string pa = '1';
        string qqa = '1';
        string ra = '1';
        string sa = '1';
        string ta = '1';
        string ua = '1';
        string va = '1';
        string va1 = '1';
        string va2 = '1';
        string va3 = '1';
        string va4 = '1';
        string va5 = '1';
        string va6 = '1';
        string va7 = '1';
        string va8 = '1';
        string va9 = '1';
        string va11 = '1';
        string va12= '1';
        string va13 = '1';
        string va14 = '1';
        string va15 = '1';
        string vaqw = '1';
        string vaeee = '1';
        string vads = '1';
        string vacv = '1';
        string vac1 = '1';
        string vac2 = '1';
        string vac3 = '1';
        string vac4 = '1';
        string vac5 = '1';
        string vac6 = '1';
        string vac7 = '1';
        string vac8 = '1';
        string vac9 = '1';
        string vac10 = '1';
        string vac11 = '1';
        string vac12 = '1';
        string vac13 = '1';
        string vac14 = '1';
        string vac15 = '1';
        string vac16 = '1';
        string vac17 = '1';
        string vac18 = '1';
        string vac19 = '1';
        string vac20 = '1';
        string vac21 = '1';
        string vac22 = '1';
        string vac23 = '1';
        string vac24 = '1';
        string vac25 = '1';
        string vac26 = '1';
        string vac27 = '1';
        string vac28 = '1';
        string vac29 = '1';
        return 'Test';
    }
        public static string B2B_hacktesting9() {
		string a = '1';
        string b = '1';
        string qa = '1';
        string c = '1';
        string d = '1';
        string e = '1';
        string f = '1';
        string g = '1';
        string h = '1';
        string i = '1';
        string j = '1';
        string k = '1';
        string la = '1';
        string ma = '1';
        string na = '1';
        string oa = '1';
        string pa = '1';
        string qqa = '1';
        string ra = '1';
        string sa = '1';
        string ta = '1';
        string ua = '1';
        string va = '1';
        string va1 = '1';
        string va2 = '1';
        string va3 = '1';
        string va4 = '1';
        string va5 = '1';
        string va6 = '1';
        string va7 = '1';
        string va8 = '1';
        string va9 = '1';
        string va11 = '1';
        string va12= '1';
        string va13 = '1';
        string va14 = '1';
        string va15 = '1';
        string vaqw = '1';
        string vaeee = '1';
        string vads = '1';
        string vacv = '1';
        string vac1 = '1';
        string vac2 = '1';
        string vac3 = '1';
        string vac4 = '1';
        string vac5 = '1';
        string vac6 = '1';
        string vac7 = '1';
        string vac8 = '1';
        string vac9 = '1';
        string vac10 = '1';
        string vac11 = '1';
        string vac12 = '1';
        string vac13 = '1';
        string vac14 = '1';
        string vac15 = '1';
        string vac16 = '1';
        string vac17 = '1';
        string vac18 = '1';
        string vac19 = '1';
        string vac20 = '1';
        string vac21 = '1';
        string vac22 = '1';
        string vac23 = '1';
        string vac24 = '1';
        string vac25 = '1';
        string vac26 = '1';
        string vac27 = '1';
        string vac28 = '1';
        string vac29 = '1';
        return 'Test';
    }
        public static string B2B_hacktesting10() {
		string a = '1';
        string b = '1';
        string qa = '1';
        string c = '1';
        string d = '1';
        string e = '1';
        string f = '1';
        string g = '1';
        string h = '1';
        string i = '1';
        string j = '1';
        string k = '1';
        string la = '1';
        string ma = '1';
        string na = '1';
        string oa = '1';
        string pa = '1';
        string qqa = '1';
        string ra = '1';
        string sa = '1';
        string ta = '1';
        string ua = '1';
        string va = '1';
        string va1 = '1';
        string va2 = '1';
        string va3 = '1';
        string va4 = '1';
        string va5 = '1';
        string va6 = '1';
        string va7 = '1';
        string va8 = '1';
        string va9 = '1';
        string va11 = '1';
        string va12= '1';
        string va13 = '1';
        string va14 = '1';
        string va15 = '1';
        string vaqw = '1';
        string vaeee = '1';
        string vads = '1';
        string vacv = '1';
        string vac1 = '1';
        string vac2 = '1';
        string vac3 = '1';
        string vac4 = '1';
        string vac5 = '1';
        string vac6 = '1';
        string vac7 = '1';
        string vac8 = '1';
        string vac9 = '1';
        string vac10 = '1';
        string vac11 = '1';
        string vac12 = '1';
        string vac13 = '1';
        string vac14 = '1';
        string vac15 = '1';
        string vac16 = '1';
        string vac17 = '1';
        string vac18 = '1';
        string vac19 = '1';
        string vac20 = '1';
        string vac21 = '1';
        string vac22 = '1';
        string vac23 = '1';
        string vac24 = '1';
        string vac25 = '1';
        string vac26 = '1';
        string vac27 = '1';
        string vac28 = '1';
        string vac29 = '1';
        return 'Test';
    }
        public static string B2B_hacktesting11() {
		string a = '1';
        string b = '1';
        string qa = '1';
        string c = '1';
        string d = '1';
        string e = '1';
        string f = '1';
        string g = '1';
        string h = '1';
        string i = '1';
        string j = '1';
        string k = '1';
        string la = '1';
        string ma = '1';
        string na = '1';
        string oa = '1';
        string pa = '1';
        string qqa = '1';
        string ra = '1';
        string sa = '1';
        string ta = '1';
        string ua = '1';
        string va = '1';
        string va1 = '1';
        string va2 = '1';
        string va3 = '1';
        string va4 = '1';
        string va5 = '1';
        string va6 = '1';
        string va7 = '1';
        string va8 = '1';
        string va9 = '1';
        string va11 = '1';
        string va12= '1';
        string va13 = '1';
        string va14 = '1';
        string va15 = '1';
        string vaqw = '1';
        string vaeee = '1';
        string vads = '1';
        string vacv = '1';
        string vac1 = '1';
        string vac2 = '1';
        string vac3 = '1';
        string vac4 = '1';
        string vac5 = '1';
        string vac6 = '1';
        string vac7 = '1';
        string vac8 = '1';
        string vac9 = '1';
        string vac10 = '1';
        string vac11 = '1';
        string vac12 = '1';
        string vac13 = '1';
        string vac14 = '1';
        string vac15 = '1';
        string vac16 = '1';
        string vac17 = '1';
        string vac18 = '1';
        string vac19 = '1';
        string vac20 = '1';
        string vac21 = '1';
        string vac22 = '1';
        string vac23 = '1';
        string vac24 = '1';
        string vac25 = '1';
        string vac26 = '1';
        string vac27 = '1';
        string vac28 = '1';
        string vac29 = '1';
        return 'Test';
    }
    public static string B2B_hacktesting12() {
		string a = '1';
        string b = '1';
        string qa = '1';
        string c = '1';
        string d = '1';
        string e = '1';
        string f = '1';
        string g = '1';
        string h = '1';
        string i = '1';
        string j = '1';
        string k = '1';
        string la = '1';
        string ma = '1';
        string na = '1';
        string oa = '1';
        string pa = '1';
        string qqa = '1';
        string ra = '1';
        string sa = '1';
        string ta = '1';
        string ua = '1';
        string va = '1';
        string va1 = '1';
        string va2 = '1';
        string va3 = '1';
        string va4 = '1';
        string va5 = '1';
        string va6 = '1';
        string va7 = '1';
        string va8 = '1';
        string va9 = '1';
        string va11 = '1';
        string va12= '1';
        string va13 = '1';
        string va14 = '1';
        string va15 = '1';
        string vaqw = '1';
        string vaeee = '1';
        string vads = '1';
        string vacv = '1';
        string vac1 = '1';
        string vac2 = '1';
        string vac3 = '1';
        string vac4 = '1';
        string vac5 = '1';
        string vac6 = '1';
        string vac7 = '1';
        string vac8 = '1';
        string vac9 = '1';
        string vac10 = '1';
        string vac11 = '1';
        string vac12 = '1';
        string vac13 = '1';
        string vac14 = '1';
        string vac15 = '1';
        string vac16 = '1';
        string vac17 = '1';
        string vac18 = '1';
        string vac19 = '1';
        string vac20 = '1';
        string vac21 = '1';
        string vac22 = '1';
        string vac23 = '1';
        string vac24 = '1';
        string vac25 = '1';
        string vac26 = '1';
        string vac27 = '1';
        string vac28 = '1';
        string vac29 = '1';
        return 'Test';
    }
    public static string B2B_hacktesting13() {
		string a = '1';
        string b = '1';
        string qa = '1';
        string c = '1';
        string d = '1';
        string e = '1';
        string f = '1';
        string g = '1';
        string h = '1';
        string i = '1';
        string j = '1';
        string k = '1';
        string la = '1';
        string ma = '1';
        string na = '1';
        string oa = '1';
        string pa = '1';
        string qqa = '1';
        string ra = '1';
        string sa = '1';
        string ta = '1';
        string ua = '1';
        string va = '1';
        string va1 = '1';
        string va2 = '1';
        string va3 = '1';
        string va4 = '1';
        string va5 = '1';
        string va6 = '1';
        string va7 = '1';
        string va8 = '1';
        string va9 = '1';
        string va11 = '1';
        string va12= '1';
        string va13 = '1';
        string va14 = '1';
        string va15 = '1';
        string vaqw = '1';
        string vaeee = '1';
        string vads = '1';
        string vacv = '1';
        string vac1 = '1';
        string vac2 = '1';
        string vac3 = '1';
        string vac4 = '1';
        string vac5 = '1';
        string vac6 = '1';
        string vac7 = '1';
        string vac8 = '1';
        string vac9 = '1';
        string vac10 = '1';
        string vac11 = '1';
        string vac12 = '1';
        string vac13 = '1';
        string vac14 = '1';
        string vac15 = '1';
        string vac16 = '1';
        string vac17 = '1';
        string vac18 = '1';
        string vac19 = '1';
        string vac20 = '1';
        string vac21 = '1';
        string vac22 = '1';
        string vac23 = '1';
        string vac24 = '1';
        string vac25 = '1';
        string vac26 = '1';
        string vac27 = '1';
        string vac28 = '1';
        string vac29 = '1';
        return 'Test';
    }
    public class ResultsClass {
   		Integer totalrecords;
    	Integer duplicaterecords;
        Integer newrecords;
	}
    public static string fm_getfield(List<fm> ip_fm_arr, String ip_rfa_field_name) {
        for(Integer i = 0; i < ip_fm_arr.size(); i++) {
            if (ip_fm_arr[i].rfa_field_name == ip_rfa_field_name) { return ip_fm_arr[i].sf_field_name; }
        }
		return 'OFF';
    }
    public static List<fm> fm_getfields(String ip_baseurl) {
        List<fm> fm_arr = new List<fm>{};    
        if(Test.isRunningTest()){
           fm fmrecord = new fm();
           fmrecord.rfa_field_name = 'Test';
           fmrecord.sf_field_name = 'Test';
           fmrecord.status = 'Test';
           fm_arr.add(fmrecord);
        }            
        else {
            HttpRequest req = new HttpRequest();
			string namespace = '';            
            req.setEndpoint('https://europe-west1-redflag-live.cloudfunctions.net/SF_fm_getfields?baseurl=' + ip_baseurl + '&type=lead' + '&namespace=' + namespace);
            req.setMethod('GET');
            req.setHeader('Content-Type','application/json');
            Http http = new Http();
            HTTPResponse res = http.send(req);
            if (res.getStatusCode() == 200) { 
                JSONParser parser = JSON.createParser(res.getBody());
                while (parser.nextToken() != null) {
                    if ((parser.getCurrentToken() == JSONToken.FIELD_NAME) && (parser.getText() == 'rfa_field_name')) {
                        fm fmrecord = new fm();
                        parser.nextToken();
                        fmrecord.rfa_field_name = parser.getText();    
                        parser.nextToken();
                        parser.nextToken();
                        parser.nextToken();
                        parser.nextToken();
                        parser.nextToken();
                        parser.nextToken();
                        fmrecord.sf_field_name = parser.getText();    
                        parser.nextToken();
                        parser.nextToken();
                        parser.nextToken();
                        parser.nextToken();
                        fmrecord.status = parser.getText();    
                        if (fmrecord.status == 'ON') { fm_arr.add(fmrecord); }
                    }
                }
            }
        }
		return fm_arr;
    }
    public class fm {
		public string rfa_field_name;
        public string sf_field_name;
        public string status;
    }
}