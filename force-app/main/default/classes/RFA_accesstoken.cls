// 26/08/2023 JD Global class for access token
global class RFA_accesstoken {
	public static string RFA_accesstoken() {
        if(Test.isRunningTest()){return 'Test';}
        else {
            HttpRequest req = new HttpRequest();
            req.setEndpoint('https://europe-west1-redflag-live.cloudfunctions.net/SF_getgraphqltoken?baseurl=temp');
            req.setMethod('GET');
            req.setHeader('Content-Type','application/json');
            Http http = new Http();
            HTTPResponse res = http.send(req);
            if (res.getStatusCode() == 200) { return res.getBody(); }
            else return 'FAILED';
        }
    }
}