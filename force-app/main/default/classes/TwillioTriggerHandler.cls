/**
 * TwillioTriggerHandler is responsible for associating incoming mobile messages with existing Contacts.
 * It processes a list of Mobile_Message__c records and links them to the appropriate Contact based on phone numbers.
 */
public class TwillioTriggerHandler {
     /**
     * Attaches a Contact to each Mobile_Message__c record based on the phone number.
     * It standardizes phone number formats and queries for a matching Contact.
     * If a match is found, it updates the Mobile_Message__c record with the Contact.
     * 
     * @param mobileMessageList List of Mobile_Message__c records to process.
     */
    Public static void attachContact(List<Mobile_Message__c> mobileMessageList){
        Set<Id> mmIDSet=new Set<Id>();
        for(Mobile_Message__c mm:mobileMessageList){
            mmIDSet.add(mm.id);
        }
        List<Mobile_Message__c> mmList=[Select id,From_Phone_Number__c,Contact__c from Mobile_Message__c where Id IN:mmIDSet];
        List<Mobile_Message__c> updatemmList=new List<Mobile_Message__c>();
        Set<String> mobileNumberSet=new Set<String>();
        List<Contact> contactList=new List<Contact>();
        for(Mobile_Message__c mm:mmList){
            String fromPhoneNumber=mm.From_Phone_Number__c;
            mobileNumberSet.add(fromPhoneNumber);
            
            If(fromPhoneNumber.startswith('07')){
                
                String fromPhoneNumbersubstring=fromPhoneNumber.substring(2);
                mobileNumberSet.add('+447'+fromPhoneNumbersubstring);
                mobileNumberSet.add('+4407'+fromPhoneNumbersubstring);
                
            }else if(fromPhoneNumber.startswith('+447')){
                
                String fromPhoneNumbersubstring=fromPhoneNumber.substring(4);
                mobileNumberSet.add('07'+fromPhoneNumbersubstring);
                mobileNumberSet.add('+4407'+fromPhoneNumbersubstring);
                
            }else if(fromPhoneNumber.startswith('+4407')){
                
                String fromPhoneNumbersubstring=fromPhoneNumber.substring(5);
                mobileNumberSet.add('07'+fromPhoneNumbersubstring);
                mobileNumberSet.add('+447'+fromPhoneNumbersubstring);
                
            }
        }
        
        if(!mobileNumberSet.isEmpty()){
            contactList=[Select id,MobilePhone,LastActivityDate from Contact where MobilePhone In:mobileNumberSet Order By LastActivityDate Desc];
            if(contactList.isEmpty()){
                contactList=[Select id,Phone,LastActivityDate from Contact where Phone In:mobileNumberSet Order By LastActivityDate Desc];
            }
        }
        
        if(!contactList.isEmpty()){
            for(Mobile_Message__c mm : mmList){
                mm.Contact__c=contactList[0].id;
                updatemmList.add(mm);
            }
            if(!updatemmList.isEmpty()){
                update updatemmList;
            }
            
        }
        
        
        
    }
    
}