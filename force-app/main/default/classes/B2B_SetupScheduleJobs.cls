public class B2B_SetupScheduleJobs {
    @AuraEnabled // This is used if called by js code
	public static string B2B_SchedulerJobs() {
        B2B_Main_Scheduler B2B = new B2B_Main_Scheduler();
        String SchedulableTime1 = '0 0 * * * ?';
		String JobId1 = System.schedule('B2B Scheduler - Every hour at 00', SchedulableTime1, B2B);
        String SchedulableTime2 = '0 10 * * * ?';
		String JobId2 = System.schedule('B2B Scheduler - Every hour at 10', SchedulableTime2, B2B);
        String SchedulableTime3 = '0 20 * * * ?';
		String JobId3 = System.schedule('B2B Scheduler - Every hour at 20', SchedulableTime3, B2B);
        String SchedulableTime4 = '0 30 * * * ?';
		String JobId4 = System.schedule('B2B Scheduler - Every hour at 30', SchedulableTime4, B2B);
        String SchedulableTime5 = '0 40 * * * ?';
		String JobId5 = System.schedule('B2B Scheduler - Every hour at 40', SchedulableTime5, B2B);
        String SchedulableTime6 = '0 50 * * * ?';
		String JobId6 = System.schedule('B2B Scheduler - Every hour at 50', SchedulableTime6, B2B);
        String SchedulableTime7 = '0 5 * * * ?';
		String JobId7 = System.schedule('B2B Scheduler - Every hour at 05', SchedulableTime7, B2B);
        String SchedulableTime8 = '0 15 * * * ?';
		String JobId8 = System.schedule('B2B Scheduler - Every hour at 15', SchedulableTime8, B2B);
        String SchedulableTime9 = '0 25 * * * ?';
		String JobId9 = System.schedule('B2B Scheduler - Every hour at 25', SchedulableTime9, B2B);
        String SchedulableTime10 = '0 35 * * * ?';
		String JobId10 = System.schedule('B2B Scheduler - Every hour at 35', SchedulableTime10, B2B);
        String SchedulableTime11 = '0 45 * * * ?';
		String JobId11 = System.schedule('B2B Scheduler - Every hour at 45', SchedulableTime11, B2B);
        String SchedulableTime12 = '0 55 * * * ?';
		String JobId12 = System.schedule('B2B Scheduler - Every hour at 55', SchedulableTime12, B2B);
        return 'Success';
    }
}