// 24/08/2023 JD 1.0 Created
public class RFA_gazette {
    // Remove if running manually
  	@future(callout=true)
  	
    public static void RFA_gazette() {
        string RFA_AcccessToken = RFA_accesstoken.RFA_accesstoken();
        if (RFA_AcccessToken == 'FAILED') { System.debug('--- Failed to get RFA Access Token - Stop!'); }
        else {
            Datetime querydatetime = DateTime.parse(System.Now().format());
            //string querydatetime1 = '2023-09-14 13:00:00';
            System.debug('--- Process Gazette Data created after: ' + querydatetime);
            HttpRequest req = new HttpRequest();
            req.setEndpoint('https://azp-primary-api.azurewebsites.net/graphql');
            req.setMethod('POST');
            req.setHeader('Authorization','Bearer ' + RFA_AcccessToken);
            req.setHeader('Content-Type','application/json');
            req.setBody('{ "query" : "{ companies ( where: { gazette_notices: { some: { created_date: {  gt:\\"' + querydatetime + '\\"}}} }, take: 1000, skip: 0) {' + ' items { company_name company_number gazette_notices { published_date created_date notice_code_value notice_code_name notice_id uri }}}}"}');    
            Http http = new Http();
            HTTPResponse res = http.send(req);
            JSONParser parser = JSON.createParser(res.getBody());
            while (parser.nextToken() != null) {
                if ((parser.getCurrentToken() == JSONToken.FIELD_NAME) && (parser.getText() == 'company_number')) {
                    parser.nextToken();
                    string CompanyNumber = parser.getText();    
                    parser.nextToken();
                    parser.nextToken();
                    parser.nextToken();
                    parser.nextToken();
                    parser.nextToken();
                    parser.nextToken();
                    parser.nextToken();
                    string ReturnValue = FindCompanyNumber(CompanyNumber);
                    string[] Returnlist = ReturnValue.split(',');
                    string SFAccountID = Returnlist[0];
     				string CompanyName = Returnlist[1]; 
                    if (SFAccountID == 'NOTFOUND') {}
                    else { string results = CreateChatterRecord2(res.getBody(),CompanyNumber, SFAccountID, CompanyName); }
                }
            }  
        }     
    }
	public static string FindCompanyNumber(string ip_CompanyNumber) {
        Account[] accts = [SELECT Name FROM Account WHERE (CompanyNumber__c = :ip_CompanyNumber) ORDER BY Name LIMIT 1];
		if (accts.size() == 1) {
        	string ReturnValue = accts[0].Id + ',' + accts[0].Name;
            return ReturnValue;
        }
        else { return 'NOTFOUND,None'; }
    }
    public static string CreateChatterRecord2(string ip_GazzetteData, string ip_CompanyNumber, string ip_AccountId, string ip_CompanyName) {
        JSONParser parser = JSON.createParser(ip_GazzetteData);
        while (parser.nextToken() != null) {
            if ((parser.getCurrentToken() == JSONToken.FIELD_NAME) && (parser.getText() == 'company_number')) {
                parser.nextToken();
                string CompanyNumber = parser.getText();
                if (CompanyNumber == ip_CompanyNumber) {
                    string PublishDate = '';
                    string TimeStamp = '';
                    string CreateDate = '';                   
                    string NoticeCodeValue = '';
                    string NoticeCodeName = '';
                    string NoticeCodeID = '';
                    string NoticeURL = '';
                    while(parser.nextToken() != JSONToken.END_ARRAY) {
                        if(parser.getCurrentToken() == JSONToken.FIELD_NAME) {
                            String attr2 = parser.getText();
                            parser.nextToken();
                            if(attr2 == 'published_date') {
                                string PublishDateTemp = parser.getText();
                                PublishDate = PublishDateTemp.substring(8, 10) + '/' + PublishDateTemp.substring(5, 7) + '/' + PublishDateTemp.substring(0, 4);
                            }
                            if(attr2 == 'created_date') {
                                CreateDate = parser.getText();
                                TimeStamp  = CreateDate.substring(11, 16);
                            }
                            if(attr2 == 'notice_code_value') { NoticeCodeValue = parser.getText(); }
                            if(attr2 == 'notice_code_name') {
                                NoticeCodeName = parser.getText();
                                NoticeCodeName = NoticeCodeName.replaceall('_',' ');
                                String name = NoticeCodeName.toLowercase();
                                List<String> names = name.split(' ');
                                for (Integer i = 0; i < names.size(); i++) names[i] = names[i].capitalize();
                                NoticeCodeName = String.join(names, ' ');
                            }
                            if(attr2 == 'notice_id') { NoticeCodeID = parser.getText(); }
                            if(attr2 == 'uri') {
                                NoticeURL = parser.getText();
                                Task task = new Task(
                                    Subject     = ip_CompanyName + ' (' + ip_CompanyNumber + ') - Gazette Notice: ' + NoticeCodeName,
                                    Status      = 'Open',
                                    Priority    = 'High',
                                    WhatId      = ip_AccountId,
                                    Description = 'Notice Code: ' + NoticeCodeValue + '\n' + 'Notice ID: ' + NoticeCodeID + '\n' + 'Published Date: ' + PublishDate + '\n' + 'Notice URL: ' + NoticeURL + '\n' + 'Create Time: ' + TimeStamp + '\n'
                                );
                                insert task;
                            }
                        }     
                    }
                }
            }
        }
        return 'Success';
    }
}