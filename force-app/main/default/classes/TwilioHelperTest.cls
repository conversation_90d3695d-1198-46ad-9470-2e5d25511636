@isTest
public class TwilioHelperTest {
    
     @isTest
    static void testSendSMS_Success() {
        //Create Custom Setting Record
        TwillioCredentials__c tl=new TwillioCredentials__c();
        tl.Auth_Token__c='78f1ff2b4c394465f1b44d3a0e0ec61d ';
        tl.Account_SID__c='********************************** ';
        tl.Twillio_URL__c='https://api.twilio.com';
        insert tl;
        
        // Create a mock SMS request
        TwilioHelper.SMSRequest request = new TwilioHelper.SMSRequest();
        request.toPhoneNumber = '+**********';
        request.fromPhoneNumber = '+**********';
        request.messageBody = 'Test message';
        
        List<TwilioHelper.SMSRequest> requests = new List<TwilioHelper.SMSRequest>{ request };
        
        // Mock HTTP callout for success
        Test.startTest();
        Test.setMock(HttpCalloutMock.class, new TwilioMockSuccessResponse());
        List<TwilioHelper.SMSResponse> responses = TwilioHelper.sendSMS(requests);
        Test.stopTest();
        
        // Validate response
        System.assertEquals(1, responses.size(), 'There should be one response');
        System.assertEquals('Success', responses[0].status, 'SMS should be sent successfully');
    }
    
    @isTest
    static void testSendSMS_Failure() {
        //Create Custom Setting Record
        TwillioCredentials__c tl=new TwillioCredentials__c();
        tl.Auth_Token__c='78f1ff2b4c394465f1b44d3a0e0ec61d ';
        tl.Account_SID__c='********************************** ';
        tl.Twillio_URL__c='https://api.twilio.com';
        insert tl;
        // Create a mock SMS request
        TwilioHelper.SMSRequest request = new TwilioHelper.SMSRequest();
        request.toPhoneNumber = '+**********';
        request.fromPhoneNumber = '+**********';
        request.messageBody = 'Test failure message';
        
        List<TwilioHelper.SMSRequest> requests = new List<TwilioHelper.SMSRequest>{ request };
        
        // Mock HTTP callout for failure
        Test.startTest();
        Test.setMock(HttpCalloutMock.class, new TwilioMockFailureResponse());
        List<TwilioHelper.SMSResponse> responses = TwilioHelper.sendSMS(requests);
        Test.stopTest();
        
        // Validate response
        System.assertEquals(1, responses.size(), 'There should be one response');
        System.assertEquals('Error', responses[0].status, 'SMS should fail to send');
    }
}