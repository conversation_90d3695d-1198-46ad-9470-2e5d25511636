public class Twi<PERSON>Helper {
    // Define an inner class to accept inputs from the Flow
    public class SMSRequest {
        @InvocableVariable(required=true)
        public String toPhoneNumber; // The recipient's phone number
        
        @InvocableVariable(required=true)
        public String fromPhoneNumber; // The senders's phone number
        

        @InvocableVariable(required=true)
        public String messageBody; // The SMS message content
    }

    // Define an inner class for returning results to the Flow (optional)
    public class SMSResponse {
        @InvocableVariable
        public String status; // Status of the SMS (e.g., success or error)

        @InvocableVariable
        public String responseMessage; // Detailed response from Twilio or error message
    }

    @InvocableMethod(label='Send SMS via Twilio' description='Send an SMS using Twilio API')
    public static List<SMSResponse> sendSMS(List<SMSRequest> requests) {
        List<SMSResponse> responses = new List<SMSResponse>();
        List<TwillioCredentials__c> twillio=[SELECT Id, Name, Auth_Token__c, Account_SID__c, Twillio_URL__c FROM TwillioCredentials__c Limit 1];
        if(!twillio.isEmpty()){
        String ACCOUNT_SID = twillio[0].Account_SID__c;
        String AUTH_TOKEN = twillio[0].Auth_Token__c;
        String TWILIO_URL  = twillio[0].Twillio_URL__c+'/2010-04-01/Accounts/' + ACCOUNT_SID + '/Messages.json';
     
        for (SMSRequest request : requests) {
            SMSResponse response = new SMSResponse();
            try {
                // Create an HTTP request
                Http http = new Http();
                HttpRequest httpRequest = new HttpRequest();

                // Set up the request properties
               httpRequest.setEndpoint(TWILIO_URL);
                //httpRequest.setEndpoint('callout:Twillio');
                httpRequest.setMethod('POST');
                httpRequest.setHeader('Content-Type', 'application/x-www-form-urlencoded');
                httpRequest.setHeader('Authorization', 'Basic ' + EncodingUtil.base64Encode(Blob.valueOf(ACCOUNT_SID + ':' + AUTH_TOKEN)));

                // Set the body of the request
                String body = 'To=' + EncodingUtil.urlEncode(request.toPhoneNumber, 'UTF-8') +
                              '&From=' + EncodingUtil.urlEncode(request.fromPhoneNumber, 'UTF-8') + // Replace with your Twilio phone number
                              '&Body=' + EncodingUtil.urlEncode(request.messageBody, 'UTF-8');
                httpRequest.setBody(body);

                // Send the HTTP request and handle the response
                HttpResponse httpResponse = http.send(httpRequest);
                if (httpResponse.getStatusCode() == 201) {
                    response.status = 'Success';
                    response.responseMessage = 'SMS sent successfully: ' + httpResponse.getBody();
                    Logger.info('SMS sent successfully')
                    .addTag('HTTP')
                    .addTag('Outbound')
                    .setHttpRequestDetails(httpRequest)
                    .setHttpResponseDetails(httpResponse);
                } else {
                    response.status = 'Error';
                    response.responseMessage = 'Failed to send SMS: ' + httpResponse.getBody();
                    Logger.error('SMS failed to send SMS')
                    .addTag('HTTP')
                    .addTag('Outbound')
                    .setHttpRequestDetails(httpRequest)
                    .setHttpResponseDetails(httpResponse);                    
                }
            } catch (Exception e) {
                response.status = 'Error';
                response.responseMessage = 'Exception occurred: ' + e.getMessage();
                Logger.error('SMS failed to send SMS')
                .addTag('HTTP')
                .addTag('Outbound');                   
            }finally {
                // Save the log entries
               
                Logger.saveLog();
                
            }
            responses.add(response);
        }
        }
        return responses;
    }
}