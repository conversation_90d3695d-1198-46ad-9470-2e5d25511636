@isTest
public class TwillioTriggerHandlerTest {
    
    @testSetup
    static void setupTestData() {
        Account testAccount = new Account(Name = 'Test Account',Type='Customer');
        insert testAccount;
        // Create test Contacts
        Contact contact1 = new Contact(AccountId=testAccount.Id,FirstName = 'John', LastName = 'Doe', MobilePhone = '***********',OwnerId=UserInfo.getUserId());
        Contact contact2 = new Contact(AccountId=testAccount.Id,FirstName = 'Jane', LastName = 'Smith', MobilePhone = '+************',OwnerId=UserInfo.getUserId());
        insert new List<Contact>{ contact1, contact2 };

        // Create test Mobile Messages
        Mobile_Message__c message1 = new Mobile_Message__c(From_Phone_Number__c = '***********',To_Phone_Number__c='************',Message__c='Test23',Status__c='Received');
        Mobile_Message__c message2 = new Mobile_Message__c(From_Phone_Number__c = '+************',To_Phone_Number__c='***********',Message__c='Test23',Status__c='Received');
        insert new List<Mobile_Message__c>{ message1, message2 };
    }
    
    @isTest
    static void testAttachContact() {
        // Retrieve test messages
        List<Mobile_Message__c> messages = [SELECT Id, From_Phone_Number__c FROM Mobile_Message__c];
        
        // Call the method to test
        Test.startTest();
        TwillioTriggerHandler.attachContact(messages);
        Test.stopTest();
        
        // Verify contacts are attached
        List<Mobile_Message__c> updatedMessages = [SELECT Id, Contact__c FROM Mobile_Message__c];
        for (Mobile_Message__c msg : updatedMessages) {
            System.assertNotEquals(null, msg.Contact__c, 'Contact should be attached');
        }
    }
}