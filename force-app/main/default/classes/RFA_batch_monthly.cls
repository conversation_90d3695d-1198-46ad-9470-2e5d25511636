global with sharing class RFA_batch_monthly implements Database.Batchable <SObject>,Database.AllowsCallouts{
    global Database.QueryLocator start (Database.BatchableContext bc) {
        System.debug('Start ' + Date.today());
        return Database.getQueryLocator([SELECT id, name, CompanyNumber__c from Account WHERE CompanyNumber__c != null]);
    }
    global void execute(SchedulableContext SC) {
        System.debug('executebatch');
        database.executebatch(new RFA_batch_monthly(), 50);
    }
	global void execute(Database.BatchableContext bc, List<Account> acList) {
    	System.debug('Process account records: ' + acList.size());
        string RFA_AccessToken = RFA_accesstoken.RFA_accesstoken();
        if (RFA_AccessToken == 'FAILED') {
        	System.debug('--- Failed to get RFA Access Token - Stop!');
            return;
        }
        String BaseURL = URL.getSalesforceBaseUrl().toExternalForm();
        BaseURL = BaseURL.replace('my.salesforce.com','lightning.force.com');
        List<fm> fm_arr = fm_getfields(BaseURL);
        for(Account ac : acList) {    
			System.debug('PROCESS: ' + ac.Name + ' ' + ac.CompanyNumber__c);
            resultsdata results = rfa_getdata(RFA_AccessToken, ac.CompanyNumber__c);
        	if (results.status == 'SUCCESS') { ac = rfa_updatedata(results.data, ac, fm_arr); }
        }
        try {
            update acList;
        } catch(Exception e) { System.debug(e); }
   	}
    global void finish(Database.BatchableContext bc) { System.debug('Finished'); }
    public static resultsdata rfa_getdata(String ip_accesstoken, String ip_companynumber) {
        resultsdata results = new resultsdata();
	    HttpRequest req = new HttpRequest();
       	req.setEndpoint('https://europe-west1-redflag-live.cloudfunctions.net/sf_b2b_getrafacompany_gql?companynumber=' + ip_companynumber + '&accesstoken=' + ip_accesstoken);
       	req.setMethod('GET');
       	req.setHeader('Content-Type','application/json');
       	Http http = new Http();
        HttpResponse res = new HttpResponse();
        res = http.send(req);
        if (res.getStatusCode() == 200) { 
            results.status = 'SUCCESS';
        	results.data = res.getBody();
        }
        else { results.status = 'FAILED'; }
        return results;
    }
    class resultsdata {
        string status;
        string data;
    }
    public static Account rfa_updatedata(String Data, Account ip_account,  List<fm> ip_fm_arr) {
        string namespace = '';
        string sf_field_name = '';
        List<String> Data_Arr = Data.split(',');
        string HaveEmployees = 'N';
        for (Integer i = 0; i < Data_Arr.size(); i++) {
            if (Data_Arr[i] == 'name' && Data_Arr[i+1] != 'null' ) { 
            	sf_field_name = fm_getfield(ip_fm_arr, 'RFA Company name');
                if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, Data_Arr[i+1].capitalize());
            }
            if (Data_Arr[i] == 'employees' && Data_Arr[i+1] != 'null' ) {
            	HaveEmployees = 'Y';
            	sf_field_name = fm_getfield(ip_fm_arr, 'RFA Employees');
                if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, Integer.valueof(Data_Arr[i+1]));
            }
            if (HaveEmployees == 'N') {
                if (Data_Arr[i] == 'estimated_employees' && Data_Arr[i+1] != 'null' ) {
                	sf_field_name = fm_getfield(ip_fm_arr, 'RFA Employees');
                	if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, Integer.valueof(Data_Arr[i+1]));
                }
            }
            if (Data_Arr[i] == 'sic07_descriptions' && Data_Arr[i+1] != 'null' ) {
            	sf_field_name = fm_getfield(ip_fm_arr, 'RFA SIC Description');
                if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, Data_Arr[i+1]);
            }
            // Do not update Phone, address
            if (Data_Arr[i] == 'turnover' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Turnover');
            	if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, decimal.valueof(Data_Arr[i+1]));
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Estimated Turnover');
            	if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, null);
            }
            if (Data_Arr[i] == 'estimated_turnover' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Estimated Turnover');
            	if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, decimal.valueof(Data_Arr[i+1]));
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Turnover');
            	if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, null);
            }
   			if (Data_Arr[i] == 'net_worth' && Data_Arr[i+1] != 'null' ) {
            	sf_field_name = fm_getfield(ip_fm_arr, 'RFA Net Worth');
                if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, decimal.valueof(Data_Arr[i+1]));
            }
            if (Data_Arr[i] == 'cash_in_bank' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Cash In Bank');
                if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, decimal.valueof(Data_Arr[i+1]));
            }
            if (Data_Arr[i] == 'directors' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Directors');
                if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, Data_Arr[i+1].replace('|',','));
            }
            if (Data_Arr[i] == 'number_of_ccjs' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Number of CCJs');
                if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, Integer.valueof(Data_Arr[i+1]));
            }
            if (Data_Arr[i] == 'ccjs_amount' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA CCJs Amount');
                if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, decimal.valueof(Data_Arr[i+1]));
            }
       		if (Data_Arr[i] == 'creditor_days' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Creditor Days');
                if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, Integer.valueof(Data_Arr[i+1]));
            }
    		if (Data_Arr[i] == 'credit_limit' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Credit Limit');
                if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, decimal.valueof(Data_Arr[i+1]));
            }
            if (Data_Arr[i] == 'sic07_codes' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA SIC Codes');
                if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, Data_Arr[i+1]);
            }
            if (Data_Arr[i] == 'sic07_group_descriptions' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA SIC Group Description');
                if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, Data_Arr[i+1]);
            }
            if (Data_Arr[i] == 'vat_number' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA VAT Number');
                if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, Data_Arr[i+1]);
            }
            if (Data_Arr[i] == 'latest_action' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Latest Action');
                if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, Data_Arr[i+1]);
            }
            if (Data_Arr[i] == 'latest_action_date' && Data_Arr[i+1] != 'n/a' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Latest Action Date');
                if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, Date.valueOf(Data_Arr[i+1]));
            }
            if (Data_Arr[i] == 'last_filed_accounts' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Latest Accounts Filed Date');
                if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, Date.valueOf(Data_Arr[i+1]));
            }
            if (Data_Arr[i] == 'incorporation_date' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Incorporation Date');
                if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, Date.valueOf(Data_Arr[i+1]));
            }
            if (Data_Arr[i] == 'generated_rfa_rating' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Rating Code');
                if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, Data_Arr[i+1]);
            }
            if (Data_Arr[i] == 'parent_companyid' && Data_Arr[i+1] != 'null' ) {ip_account.RFA_Parent_Company_ID__c = Data_Arr[i+1];}
            if (Data_Arr[i] == 'parent_companyname' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Parent Company Name');
                if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, Data_Arr[i+1]);
            }
            if (Data_Arr[i] == 'parent_companynumber' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Parent Company Number');
                if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, Data_Arr[i+1]);
                ip_account.RFA_Parent_Company_Number__c = Data_Arr[i+1];
            }
            if (Data_Arr[i] == 'tps_registered' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA TPS Registered');
                if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, Data_Arr[i+1]);
            }
            if (Data_Arr[i] == 'company_type' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Company Type');
                if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, Data_Arr[i+1]);
            }
            if (Data_Arr[i] == 'website' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Website');
                if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, Data_Arr[i+1]);
            }
            // Custom company number
            if (Data_Arr[i] == 'companynumber' && Data_Arr[i+1] != 'null' ) {
                sf_field_name = fm_getfield(ip_fm_arr, 'RFA Company number');
                if (sf_field_name != 'OFF') String field = (String)ip_account.put(namespace + sf_field_name, Data_Arr[i+1]);
            }
            ip_account.RFA_Update_Date__c = Date.today();           
        }
        return ip_account;
    }
	public static string fm_getfield(List<fm> ip_fm_arr, String ip_rfa_field_name) {
        for(Integer i = 0; i < ip_fm_arr.size(); i++) {
            if (ip_fm_arr[i].rfa_field_name == ip_rfa_field_name) { 
                string fieldname = ip_fm_arr[i].sf_field_name; 
                fieldname = fieldname.replace('redflagalert__','');
                return fieldname;
            }
        }
		return 'OFF';
    }
    public static List<fm> fm_getfields(String ip_baseurl) {
        List<fm> fm_arr = new List<fm>{};    
        if(Test.isRunningTest()){
           fm fmrecord = new fm();
           fmrecord.rfa_field_name = 'Test';
           fmrecord.sf_field_name = 'Test';
           fmrecord.status = 'Test';
           fm_arr.add(fmrecord);
        }            
        else {
            HttpRequest req = new HttpRequest();
            string namespace = '';            
            req.setEndpoint('https://europe-west1-redflag-live.cloudfunctions.net/SF_fm_getfields?baseurl=' + ip_baseurl + '&type=account' + '&namespace=' + namespace);
            req.setMethod('GET');
            req.setHeader('Content-Type','application/json');
            Http http = new Http();
            HTTPResponse res = http.send(req);
            if (res.getStatusCode() == 200) { 
                JSONParser parser = JSON.createParser(res.getBody());
                while (parser.nextToken() != null) {
                    if ((parser.getCurrentToken() == JSONToken.FIELD_NAME) && (parser.getText() == 'rfa_field_name')) {
                        fm fmrecord = new fm();
                        parser.nextToken();
                        fmrecord.rfa_field_name = parser.getText();    
                        parser.nextToken();
                        parser.nextToken();
                        parser.nextToken();
                        parser.nextToken();
                        parser.nextToken();
                        parser.nextToken();
                        fmrecord.sf_field_name = parser.getText();    
                        parser.nextToken();
                        parser.nextToken();
                        parser.nextToken();
                        parser.nextToken();
                        fmrecord.status = parser.getText();    
                        if (fmrecord.status == 'ON') { fm_arr.add(fmrecord); }
                    }
                }
            }
        }
		return fm_arr;
    }
    public class fm {
		string rfa_field_name;
        string sf_field_name;
        string status;
    }
}