<?xml version="1.0" encoding="UTF-8"?>
<CustomApplication xmlns="http://soap.sforce.com/2006/04/metadata">
    <consoleConfig>
        <detailPageRefreshMethod>none</detailPageRefreshMethod>
        <keyboardShortcuts>
            <defaultShortcuts>
                <action>FOCUS_CONSOLE</action>
                <active>true</active>
                <keyCommand>ESC</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>FOCUS_NAVIGATOR_TAB</action>
                <active>true</active>
                <keyCommand>V</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>FOCUS_DETAIL_VIEW</action>
                <active>true</active>
                <keyCommand>SHIFT+S</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>FOCUS_PRIMARY_TAB_PANEL</action>
                <active>true</active>
                <keyCommand>P</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>FOCUS_SUBTAB_PANEL</action>
                <active>true</active>
                <keyCommand>S</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>FOCUS_LIST_VIEW</action>
                <active>true</active>
                <keyCommand>N</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>FOCUS_FIRST_LIST_VIEW</action>
                <active>true</active>
                <keyCommand>SHIFT+F</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>FOCUS_SEARCH_INPUT</action>
                <active>true</active>
                <keyCommand>R</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>MOVE_LEFT</action>
                <active>true</active>
                <keyCommand>LEFT ARROW</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>MOVE_RIGHT</action>
                <active>true</active>
                <keyCommand>RIGHT ARROW</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>UP_ARROW</action>
                <active>true</active>
                <keyCommand>UP ARROW</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>DOWN_ARROW</action>
                <active>true</active>
                <keyCommand>DOWN ARROW</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>OPEN_TAB_SCROLLER_MENU</action>
                <active>true</active>
                <keyCommand>D</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>OPEN_TAB</action>
                <active>true</active>
                <keyCommand>T</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>CLOSE_TAB</action>
                <active>true</active>
                <keyCommand>C</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>REFRESH_TAB</action>
                <active>false</active>
                <keyCommand>SHIFT+R</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>ENTER</action>
                <active>true</active>
                <keyCommand>ENTER</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>EDIT</action>
                <active>true</active>
                <keyCommand>E</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>SAVE</action>
                <active>true</active>
                <keyCommand>CTRL+S</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>CONSOLE_LINK_DIALOG</action>
                <active>false</active>
                <keyCommand>U</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>HOTKEYS_PANEL</action>
                <active>false</active>
                <keyCommand>SHIFT+K</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>FOCUS_MACRO</action>
                <active>false</active>
                <keyCommand>M</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>FOCUS_FOOTER_PANEL</action>
                <active>false</active>
                <keyCommand>F</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>TOGGLE_LIST_VIEW</action>
                <active>false</active>
                <keyCommand>SHIFT+N</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>TOGGLE_LEFT_SIDEBAR</action>
                <active>false</active>
                <keyCommand>SHIFT+LEFT ARROW</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>TOGGLE_RIGHT_SIDEBAR</action>
                <active>false</active>
                <keyCommand>SHIFT+RIGHT ARROW</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>TOGGLE_TOP_SIDEBAR</action>
                <active>false</active>
                <keyCommand>SHIFT+UP ARROW</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>TOGGLE_BOTTOM_SIDEBAR</action>
                <active>false</active>
                <keyCommand>SHIFT+DOWN ARROW</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>TOGGLE_APP_LEVEL_COMPONENTS</action>
                <active>false</active>
                <keyCommand>Z</keyCommand>
            </defaultShortcuts>
            <defaultShortcuts>
                <action>REOPEN_LAST_TAB</action>
                <active>false</active>
                <keyCommand>SHIFT+C</keyCommand>
            </defaultShortcuts>
        </keyboardShortcuts>
        <listPlacement>
            <location>full</location>
        </listPlacement>
        <listRefreshMethod>none</listRefreshMethod>
    </consoleConfig>
    <defaultLandingTab>standard-home</defaultLandingTab>
    <isNavAutoTempTabsDisabled>false</isNavAutoTempTabsDisabled>
    <isNavPersonalizationDisabled>false</isNavPersonalizationDisabled>
    <isNavTabPersistenceDisabled>false</isNavTabPersistenceDisabled>
    <isServiceCloudConsole>true</isServiceCloudConsole>
    <preferences>
        <enableCustomizeMyTabs>false</enableCustomizeMyTabs>
        <enableKeyboardShortcuts>true</enableKeyboardShortcuts>
        <enableListViewHover>true</enableListViewHover>
        <enableListViewReskin>true</enableListViewReskin>
        <enableMultiMonitorComponents>true</enableMultiMonitorComponents>
        <enablePinTabs>true</enablePinTabs>
        <enableTabHover>false</enableTabHover>
        <enableTabLimits>false</enableTabLimits>
        <saveUserSessions>true</saveUserSessions>
    </preferences>
    <tabs>standard-Account</tabs>
    <tabs>standard-Contact</tabs>
    <tabs>standard-Case</tabs>
    <tabs>standard-Opportunity</tabs>
    <tabs>standard-Lead</tabs>
    <tabs>standard-Knowledge</tabs>
    <tabs>MobileLanding</tabs>
    <tabs>Lender_Product__c</tabs>
    <tabs>Opportunity_Lender__c</tabs>
    <tabs>Junction_Lenders__c</tabs>
    <tabs>Assignment_Group__c</tabs>
    <tabs>Assignment_Group_Member__c</tabs>
    <workspaceConfig>
        <mappings>
            <tab>Assignment_Group_Member__c</tab>
        </mappings>
        <mappings>
            <tab>Assignment_Group__c</tab>
        </mappings>
        <mappings>
            <tab>Junction_Lenders__c</tab>
        </mappings>
        <mappings>
            <tab>Lender_Product__c</tab>
        </mappings>
        <mappings>
            <tab>MobileLanding</tab>
        </mappings>
        <mappings>
            <tab>Opportunity_Lender__c</tab>
        </mappings>
        <mappings>
            <tab>standard-Account</tab>
        </mappings>
        <mappings>
            <fieldName>AccountId</fieldName>
            <tab>standard-Case</tab>
        </mappings>
        <mappings>
            <fieldName>AccountId</fieldName>
            <tab>standard-Contact</tab>
        </mappings>
        <mappings>
            <tab>standard-Knowledge</tab>
        </mappings>
        <mappings>
            <tab>standard-Lead</tab>
        </mappings>
        <mappings>
            <fieldName>AccountId</fieldName>
            <tab>standard-Opportunity</tab>
        </mappings>
    </workspaceConfig>
</CustomApplication>
