<?xml version="1.0" encoding="UTF-8"?>
<ValidationRule xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Contact_with_no_Mobile_number</fullName>
    <active>true</active>
    <errorConditionFormula>AND(
				NOT(ISBLANK(Contact__c)),
				NOT (
   				OR(
           BEGINS(Contact__r.MobilePhone, &quot;07&quot;),
           BEGINS(Contact__r.MobilePhone, &quot;447&quot;),
           BEGINS(Contact__r.MobilePhone, &quot;4407&quot;),
           BEGINS(Contact__r.Phone, &quot;07&quot;),
           BEGINS(Contact__r.Phone, &quot;447&quot;),
           BEGINS(Contact__r.Phone, &quot;4407&quot;)
       )
				)
)</errorConditionFormula>
    <errorDisplayField>Contact__c</errorDisplayField>
    <errorMessage>The Contact Selected does not have either a mobile or phone number starting 07, 447 or 4407</errorMessage>
</ValidationRule>
