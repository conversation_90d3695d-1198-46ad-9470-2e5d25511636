<?xml version="1.0" encoding="UTF-8"?>
<ValidationRule xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>To_Number_not_valid</fullName>
    <active>true</active>
    <errorConditionFormula>AND(
				NOT(TEXT(Status__c) = &apos;Received&apos;),
				NOT(ISBLANK(To_Phone_Number__c)),
				NOT (
   				OR(
           BEGINS(To_Phone_Number__c, &quot;07&quot;),
           BEGINS(To_Phone_Number__c, &quot;447&quot;),
           BEGINS(To_Phone_Number__c, &quot;+447&quot;),
           BEGINS(To_Phone_Number__c, &quot;+4407&quot;),
           BEGINS(To_Phone_Number__c, &quot;4407&quot;)
       )
    )
)</errorConditionFormula>
    <errorDisplayField>To_Phone_Number__c</errorDisplayField>
    <errorMessage>A non-valid UK Mobile number has been entered. Mobile numbers must start with either 07, +447</errorMessage>
</ValidationRule>
