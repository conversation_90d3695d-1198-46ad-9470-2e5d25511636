<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Lending_Stage__c</fullName>
    <externalId>false</externalId>
    <label>Lending Stage</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>Picklist</type>
    <valueSet>
        <restricted>true</restricted>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>Awaiting Info</fullName>
                <default>false</default>
                <label>Awaiting Info</label>
            </value>
            <value>
                <fullName>Proposed to Funder</fullName>
                <default>false</default>
                <label>Proposed to Funder</label>
            </value>
            <value>
                <fullName>Approved - awaiting decision</fullName>
                <default>false</default>
                <label>Approved - awaiting decision</label>
            </value>
            <value>
                <fullName>Approved - awaiting docs</fullName>
                <default>false</default>
                <label>Approved - awaiting docs</label>
            </value>
            <value>
                <fullName>Approved - awaiting D &amp; S</fullName>
                <default>false</default>
                <label>Approved - awaiting D &amp; S</label>
            </value>
            <value>
                <fullName>Approved - awaiting Supplier Invoice</fullName>
                <default>false</default>
                <label>Approved - awaiting Supplier Invoice</label>
            </value>
            <value>
                <fullName>Awaiting Supplier Payment</fullName>
                <default>false</default>
                <label>Awaiting Supplier Payment</label>
            </value>
            <value>
                <fullName>Awaiting Halo Payment</fullName>
                <default>false</default>
                <label>Awaiting Halo Payment</label>
            </value>
            <value>
                <fullName>Rejected</fullName>
                <default>false</default>
                <label>Rejected</label>
            </value>
            <value>
                <fullName>Accepted</fullName>
                <default>false</default>
                <isActive>false</isActive>
                <label>Accepted</label>
            </value>
            <value>
                <fullName>Additional Info Requested</fullName>
                <default>false</default>
                <isActive>false</isActive>
                <label>Additional Info Requested</label>
            </value>
            <value>
                <fullName>Under Review</fullName>
                <default>false</default>
                <isActive>false</isActive>
                <label>Under Review</label>
            </value>
        </valueSetDefinition>
    </valueSet>
</CustomField>
