<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Funding_Type_s__c</fullName>
    <externalId>false</externalId>
    <label>Funding Type(s)</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>MultiselectPicklist</type>
    <valueSet>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>Hire Purchase</fullName>
                <default>false</default>
                <label>Hire Purchase</label>
            </value>
            <value>
                <fullName>Finance Lease</fullName>
                <default>false</default>
                <label>Finance Lease</label>
            </value>
            <value>
                <fullName>Re-Finance</fullName>
                <default>false</default>
                <label>Re-Finance</label>
            </value>
            <value>
                <fullName>Sale &amp; Leaseback</fullName>
                <default>false</default>
                <label>Sale &amp; Leaseback</label>
            </value>
            <value>
                <fullName>Sale &amp; HP back</fullName>
                <default>false</default>
                <label>Sale &amp; HP back</label>
            </value>
            <value>
                <fullName>Bridging Loan</fullName>
                <default>false</default>
                <label>Bridging Loan</label>
            </value>
            <value>
                <fullName>VAT Loan</fullName>
                <default>false</default>
                <label>VAT Loan</label>
            </value>
            <value>
                <fullName>Buy to Let</fullName>
                <default>false</default>
                <label>Buy to Let</label>
            </value>
            <value>
                <fullName>Conditional Sale (Regulated HP)</fullName>
                <default>false</default>
                <label>Conditional Sale (Regulated HP)</label>
            </value>
            <value>
                <fullName>Existing Business</fullName>
                <default>false</default>
                <label>Existing Business</label>
            </value>
            <value>
                <fullName>Fixed term lease</fullName>
                <default>false</default>
                <label>Fixed term lease</label>
            </value>
            <value>
                <fullName>Foreign Exchange</fullName>
                <default>false</default>
                <label>Foreign Exchange</label>
            </value>
            <value>
                <fullName>Hire purchase (non-regulated)</fullName>
                <default>false</default>
                <label>Hire purchase (non-regulated)</label>
            </value>
            <value>
                <fullName>Hire purchase balloon</fullName>
                <default>false</default>
                <label>Hire purchase balloon</label>
            </value>
            <value>
                <fullName>Introduction Fee</fullName>
                <default>false</default>
                <label>Introduction Fee</label>
            </value>
            <value>
                <fullName>Invoice Finance</fullName>
                <default>false</default>
                <label>Invoice Finance</label>
            </value>
            <value>
                <fullName>Minimum term lease</fullName>
                <default>false</default>
                <label>Minimum term lease</label>
            </value>
            <value>
                <fullName>Mortgage</fullName>
                <default>false</default>
                <label>Mortgage</label>
            </value>
            <value>
                <fullName>New Business</fullName>
                <default>false</default>
                <label>New Business</label>
            </value>
            <value>
                <fullName>Short Term Loan &gt; 24M</fullName>
                <default>false</default>
                <label>Short Term Loan &gt; 24M</label>
            </value>
            <value>
                <fullName>Term Loan &lt; 24M</fullName>
                <default>false</default>
                <label>Term Loan &lt; 24M</label>
            </value>
        </valueSetDefinition>
    </valueSet>
    <visibleLines>4</visibleLines>
</CustomField>
