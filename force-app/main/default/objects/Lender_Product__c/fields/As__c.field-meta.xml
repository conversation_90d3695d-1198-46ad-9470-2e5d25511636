<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>As__c</fullName>
    <externalId>false</externalId>
    <formula>If ( INCLUDES(Asset_s__c ,&quot;Agriculture&quot; ),&quot;Agriculture , &quot;, &quot;&quot;) +
If ( INCLUDES(Asset_s__c ,&quot;Bus/Coach&quot; ),&quot;Bus/Coach , &quot;, &quot;&quot;) +
If ( INCLUDES(Asset_s__c ,&quot;Cars - Business&quot; ),&quot;Cars - Business , &quot;, &quot;&quot;) +
If ( INCLUDES(Asset_s__c ,&quot;Garage Equipment&quot; ),&quot;Garage Equipment , &quot;, &quot;&quot;) +
If ( INCLUDES(Asset_s__c ,&quot;Plant&quot; ),&quot;Plant , &quot;, &quot;&quot;) +
If ( INCLUDES(Asset_s__c ,&quot;Trailers&quot; ),&quot;Trailers , &quot;, &quot;&quot;) +
If ( INCLUDES(Asset_s__c ,&quot;Trucks&quot; ),&quot;Trucks , &quot;, &quot;&quot;) +
If ( INCLUDES(Asset_s__c ,&quot;Warehouse Equipment&quot; ),&quot;Warehouse Equipment &quot;, &quot;&quot;)</formula>
    <formulaTreatBlanksAs>BlankAsZero</formulaTreatBlanksAs>
    <label>As</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>Text</type>
    <unique>false</unique>
</CustomField>
