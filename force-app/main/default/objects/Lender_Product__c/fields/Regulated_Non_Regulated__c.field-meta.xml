<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Regulated_Non_Regulated__c</fullName>
    <externalId>false</externalId>
    <label>Regulated / Non-Regulated</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>MultiselectPicklist</type>
    <valueSet>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>Regulated</fullName>
                <default>false</default>
                <label>Regulated</label>
            </value>
            <value>
                <fullName>Non-Regulated</fullName>
                <default>false</default>
                <label>Non-Regulated</label>
            </value>
        </valueSetDefinition>
    </valueSet>
    <visibleLines>3</visibleLines>
</CustomField>
